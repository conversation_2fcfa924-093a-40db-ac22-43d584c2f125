body {
    font-family: "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}
.container {
    display: flex;
    flex-direction: column;
    margin: 0 20px;
    min-height: 100vh;
}
.header {
    background: linear-gradient(135deg, #4285f4, #34a853);
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
}
.header h1 {
    margin: 0;
    font-size: 28px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}
.header p {
    margin: 8px 0 0;
    opacity: 0.9;
    font-size: 16px;
}
.settings-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    background: transparent;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    transition: transform 0.3s;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 40px;
    height: 40px;
}
.settings-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(30deg);
}
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
    margin: 20px 0;
}

/* 新的左右分栏布局 */
.content-wrapper {
    display: flex;
    gap: 20px;
    flex: 1;
    height: 600px; /* 固定高度，避免内容变化时高度变化 */
    overflow: hidden; /* 防止内容溢出导致页面高度变化 */
}

.left-panel {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
}

.right-panel {
    flex: 1;
    min-width: 0;
}

.panel-section {
    background-color: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    background-color: #f5f5f5;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h3 {
    margin: 0;
    color: #4285f4;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header i {
    font-size: 18px;
}

/* 聊天面板标题控制按钮 */
.chat-header-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
    font-size: 16px;
}

.chat-control-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-control-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.control-btn {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    box-shadow: none;
}

.control-btn:hover {
    background-color: #f0f0f0;
    border-color: #4285f4;
    color: #4285f4;
    transform: none;
    box-shadow: none;
}

.control-btn.active {
    background-color: #4285f4;
    border-color: #4285f4;
    color: white;
}

.control-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.control-checkbox:hover {
    background-color: #f0f0f0;
    border-color: #4285f4;
}

.control-checkbox input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.control-checkbox label {
    margin: 0;
    cursor: pointer;
    user-select: none;
}

/* 聊天界面样式 */
.chat-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 16px;
    background-color: white;
    overflow-y: auto;
    height: 540px; /* 固定高度，防止无限增长 */
    max-height: 540px; /* 确保不会超出限制 */
    min-height: 540px; /* 确保最小高度 */
}

.message {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
    gap: 12px;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background-color: #4285f4;
    color: white;
}

.user-message .message-avatar {
    background-color: #34a853;
    color: white;
}

.message-content {
    flex: 1;
    background-color: #f8f9fa;
    padding: 12px 16px;
    border-radius: 12px;
    border-top-left-radius: 4px;
    font-size: 14px; /* 减小2个字号，从默认16px减小到14px */
}

.user-message .message-content {
    background-color: #e3f2fd;
    border-top-left-radius: 12px;
    border-top-right-radius: 4px;
}

.message-content p {
    margin: 0 0 8px 0;
    line-height: 1.5; /* 稍微增加行高，适应较小的字体 */
}

.message-content p:last-child {
    margin-bottom: 0;
}

.chat-input-area {
    padding: 16px;
    background-color: white;
    border-top: 1px solid #e0e0e0;
    flex-shrink: 0; /* 防止输入区域被压缩 */
    height: 80px; /* 固定高度，确保输入区域稳定 */
    box-sizing: border-box; /* 包含padding在高度计算中 */
}

.display-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.display-content {
    flex: 1;
    background-color: white;
    height: 100%; /* 使用100%高度，由父容器控制 */
    display: none;
    overflow: hidden;
}

.display-content.active {
    display: flex;
    flex-direction: column;
}

#chart-container {
    flex: 1;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%; /* 确保图表容器使用全部可用高度 */
    overflow: hidden; /* 防止图表溢出 */
}
.preview-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
}

.no-data-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 150px;
    color: #999;
    text-align: center;
}

.no-data-preview i {
    font-size: 32px;
    margin-bottom: 8px;
    color: #ddd;
}

.input-area {
    padding: 16px;
    background-color: white;
}

.file-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s;
    margin: 16px;
    background-color: white;
}

.file-upload-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 40px;
}
.file-upload:hover {
    border-color: #4285f4;
    background-color: #f0f7ff;
}
.file-upload-icon {
    font-size: 36px;
    color: #4285f4;
    margin-bottom: 10px;
}
.file-upload-label {
    display: inline-block;
    background-color: #4285f4;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-bottom: 10px;
}
.file-upload-label:hover {
    background-color: #3367d6;
}
.file-name {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
}

.privacy-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-top: 15px;
    padding: 8px 12px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    font-size: 13px;
    color: #856404;
    max-width: 280px;
}

.privacy-notice i {
    font-size: 14px;
    color: #f39c12;
}

/* 模型选择样式 */
.model-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin: 20px 0;
    width: 100%;
    max-width: 280px;
}

.model-label {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
    margin: 0;
}

.model-select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    color: #495057;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.model-select:hover {
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.model-select:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}

.model-select option {
    padding: 8px;
    color: #495057;
}

#excel-file {
    display: none;
}
.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
}

@media (min-width: 600px) {
    .input-group {
        flex-direction: row;
        gap: 0;
    }
}
input[type="text"] {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    transition: border 0.3s, box-shadow 0.3s;
}
input[type="text"]:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}
button {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 12px 20px;
    margin-left: 10px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s;
    white-space: nowrap;
}

@media (max-width: 600px) {
    button {
        margin-left: 0;
        width: 100%;
    }
}
button:hover {
    background-color: #3367d6;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* 基础表格样式 */
table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 10px;
    font-size: 14px;
}

th, td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f0f0f0;
}

/* 高级Excel表格样式 */
.excel-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 13px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.excel-table th {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    font-weight: 600;
    color: #495057;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.excel-table td {
    border: 1px solid #dee2e6;
    padding: 6px 12px;
    vertical-align: top;
    background-color: white;
}

.excel-table tr:nth-child(even) td {
    background-color: #f8f9fa;
}

.excel-table tr:hover td {
    background-color: #e3f2fd;
}

/* 空值单元格样式 */
.empty-cell {
    color: #999;
    font-style: italic;
    font-size: 12px;
}

/* 表格容器样式 */
.table-container {
    border-radius: 6px;
    overflow: auto; /* 允许滚动查看内容 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: 460px; /* 增加高度，减少下方留白 */
    max-height: 460px; /* 确保不会超出限制 */
}

/* 工作表标题样式 */
.sheet-title h4 {
    margin: 0;
    color: #1976d2;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 数据统计样式 */
.data-statistics {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-left: 4px solid #4caf50;
}

/* 无数据消息样式 */
.no-data-message {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px dashed #ddd;
}
.error {
    color: #d32f2f;
    margin-top: 10px;
    padding: 10px;
    background-color: #ffebee;
    border-radius: 6px;
    display: none;
}
.footer {
    text-align: center;
    padding: 15px;
    color: #777;
    font-size: 14px;
    border-top: 1px solid #eee;
    margin-top: 20px;
}
.no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    color: #999;
    font-size: 16px;
    flex-direction: column;
    text-align: center;
}
.no-data i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #ddd;
}
.no-data small {
    font-size: 14px;
    color: #bbb;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
        gap: 15px;
        height: 700px; /* 移动设备上稍微增加高度 */
    }

    .left-panel {
        flex: none;
        order: 2;
        height: 300px; /* 固定高度 */
        overflow: hidden;
    }

    .right-panel {
        flex: none;
        order: 1;
        height: 380px; /* 固定高度 */
        overflow: hidden;
    }

    .main-content {
        margin: 10px 0;
        padding: 15px;
    }

    .container {
        margin: 0 10px;
    }

    .chat-messages {
        height: 200px; /* 移动设备上的固定高度 */
        max-height: 200px;
        min-height: 200px;
    }
}
/* 模态窗口样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 100;
    align-items: center;
    justify-content: center;
}
.modal.active {
    display: flex;
}
.modal-content {
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    position: relative;
    animation: modalFadeIn 0.3s;
}
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.modal-header h3 {
    margin: 0;
    color: #333;
}
.close-modal {
    background: transparent;
    border: none;
    font-size: 24px;
    line-height: 1;
    color: #999;
    cursor: pointer;
    padding: 0;
    margin: 0;
    box-shadow: none;
}
.close-modal:hover {
    color: #333;
    transform: none;
    box-shadow: none;
}
.modal-body {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

.form-group input[type="text"]:focus {
    border-color: #4285f4;
    outline: none;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}
.modal-footer {
    display: flex;
    justify-content: flex-end;
}
.btn-primary {
    background-color: #4285f4;
}
.btn-secondary {
    background-color: #6c757d;
    margin-right: 10px;
}
.upload-text {
    margin-top: 10px;
    font-size: 15px;
    color: #666;
}
.action-btn {
    background-color: transparent;
    color: #4285f4;
    border: 1px solid #4285f4;
    padding: 6px 12px;
    font-size: 13px;
    box-shadow: none;
    margin-left: 5px;
    display: flex;
    align-items: center;
}
.action-btn:hover {
    background-color: rgba(66, 133, 244, 0.1);
    transform: none;
    box-shadow: none;
}
.action-btn i {
    margin-right: 5px;
}
.header-actions {
    display: flex;
    align-items: center;
}

/* 全屏模式样式 */
.fullscreen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: white;
    z-index: 1000;
    display: none;
    flex-direction: column;
}

.fullscreen-overlay.active {
    display: flex;
}

.fullscreen-header {
    background: linear-gradient(135deg, #4285f4, #34a853);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.fullscreen-title {
    margin: 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.fullscreen-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.fullscreen-control-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.fullscreen-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.fullscreen-content {
    flex: 1;
    padding: 20px;
    overflow: auto;
    display: flex;
    flex-direction: column;
}

.fullscreen-chart-container {
    flex: 1;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-table-container {
    flex: 1;
    overflow: auto;
}

/* 图表推荐样式 */
.recommendation-list {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.recommendation-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 12px;
}

.recommendation-item:hover {
    background-color: #e3f2fd;
    border-color: #4285f4;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.recommendation-item i:first-child {
    color: #4285f4;
    font-size: 14px; /* 稍微减小图标大小，与文字大小更协调 */
    flex-shrink: 0;
}

.recommendation-item span {
    flex: 1;
    font-size: 12px; /* 减小2个字号，从14px减小到12px */
    color: #333;
    font-weight: 500;
}

.recommendation-item i:last-child {
    color: #666;
    font-size: 12px; /* 减小箭头图标大小，与文字大小协调 */
    opacity: 0.7;
    transition: all 0.3s ease;
}

.recommendation-item:hover i:last-child {
    color: #4285f4;
    opacity: 1;
    transform: translateX(2px);
}

/* 下一步建议样式 */
.suggestions-list {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 12px;
}

.suggestion-item:hover {
    background-color: #e8f5e8;
    border-color: #28a745;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.suggestion-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: #28a745;
    color: white;
    border-radius: 6px;
    flex-shrink: 0;
}

.suggestion-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.suggestion-text {
    font-size: 12px; /* 减小2个字号，从14px减小到12px */
    color: #333;
    font-weight: 500;
    line-height: 1.4; /* 稍微增加行高，适应较小的字体 */
}

.suggestion-type {
    font-size: 12px;
    color: #666;
    opacity: 0.8;
}

.suggestion-arrow {
    color: #666;
    font-size: 14px;
    opacity: 0.7;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.suggestion-item:hover .suggestion-arrow {
    color: #28a745;
    opacity: 1;
    transform: translateX(2px);
}

/* 历史记录样式 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
    margin: 15px 0;
}

.no-history {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-history i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.no-history p {
    font-size: 16px;
    margin-bottom: 5px;
}

.no-history small {
    font-size: 12px;
    color: #999;
}

.history-item {
    display: flex;
    flex-direction: column;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    background-color: #e3f2fd;
    border-color: #4285f4;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.history-item-question {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    flex: 1;
    margin-right: 10px;
    line-height: 1.4;
}

.history-item-time {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.history-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #777;
}

.history-item-type {
    display: flex;
    align-items: center;
    gap: 4px;
}

.history-item-type i {
    color: #4285f4;
}

.history-item-file {
    font-style: italic;
}

.history-actions {
    display: flex;
    justify-content: center;
    padding: 15px 0;
    border-top: 1px solid #e9ecef;
    margin-top: 15px;
}

.btn-secondary {
    padding: 8px 16px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Loading 动画样式 */
.loading-indicator {
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #4285f4;
    animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 弹出式Loading模态框样式 */
.loading-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-modal.active {
    display: flex;
}

.loading-modal-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    min-width: 320px;
    animation: modalFadeIn 0.3s ease-out;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.loading-spinner {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

/* 项目Logo图标容器 */
.loading-robot-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    animation: robotPulse 2s ease-in-out infinite;
}

.loading-robot-icon svg {
    width: 24px;
    height: 24px;
    color: white;
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}

/* 现代化渐变圆环Loading动画 */
.modern-spinner {
    width: 40px;
    height: 40px;
    position: relative;
    margin: 0 auto;
}

.modern-spinner::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent, #3b82f6, #8b5cf6, transparent);
    mask: radial-gradient(farthest-side, transparent calc(100% - 3px), black calc(100% - 3px));
    -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 3px), black calc(100% - 3px));
    animation: modernSpin 1s linear infinite;
}

.loading-text {
    font-size: 16px;
    color: #374151;
    font-weight: 500;
    margin-top: 8px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes modernSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes robotPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 30px rgba(139, 92, 246, 0.4);
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 图表类型切换模态窗口样式 */
.chart-type-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.chart-type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #fafafa;
    min-height: 80px;
}

.chart-type-item:hover {
    border-color: #4285f4;
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}

.chart-type-item.active {
    border-color: #4285f4;
    background-color: #e3f2fd;
    color: #4285f4;
}

.chart-type-item i {
    font-size: 24px;
    margin-bottom: 8px;
    color: #666;
    transition: color 0.3s;
}

.chart-type-item:hover i,
.chart-type-item.active i {
    color: #4285f4;
}

.chart-type-item span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    transition: color 0.3s;
}

.chart-type-item:hover span,
.chart-type-item.active span {
    color: #4285f4;
}
