// API管理模块 - 处理与LLM API的交互

// 获取用户ID的辅助函数
function getUserId() {
    try {
        // 从localStorage获取企业微信用户信息
        const userInfoJson = localStorage.getItem('wechat_work_user');
        if (userInfoJson) {
            const userInfo = JSON.parse(userInfoJson);
            return userInfo.userId || '';
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }
    return '';
}

// 生成可视化图表推荐
async function generateVisualizationRecommendations(data, fileName) {
    // 检查是否配置了LLM
    // const apiUrl = document.getElementById('api-url').value.trim();
    // const apiKey = document.getElementById('api-key').value.trim();
    // const apiModel = document.getElementById('api-model').value.trim();

    // if (!apiUrl || !apiKey || !apiModel) {
    //     // 如果没有配置LLM，显示提示信息
    //     addChatMessage('bot', '💡 提示：配置LLM模型后，我可以为您智能推荐最适合的图表类型。请点击右上角齿轮图标进行配置。');
    //     return;
    // }

    if (!data || data.length === 0) {
        return;
    }

    // 显示弹出式loading状态
    showLoadingModal('🤖 正在分析您的数据，为您推荐最适合的可视化方案...');

    try {
        // 准备数据样本（表头 + 前5行数据）
        let sampleData = [];
        if (data.length <= 6) {
            sampleData = data;
        } else {
            const header = data.slice(0, 1); // 表头
            const firstRows = data.slice(1, 6); // 前5行数据
            sampleData = [...header, ...firstRows];
        }

        const dataString = sampleData.map(row => row.join('\t')).join('\n');

        // 获取推荐提示词
        const prompt = getVisualizationRecommendationPrompt(dataString);

        // 获取用户选择的模型类型
        const modelType = document.getElementById('model-type').value || 'local';

        // 调用LLM API
        const apiUrl = 'https://chat-ai.groupama-sdig.com:8000/api/chart/llm'
        // const apiUrl = 'http://localhost:8000/api/chart/llm'
        const apiKey = 'c2stbWVuZ2NoYXQtYm90IQ=='
        const userId = getUserId(); // 获取用户ID
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                query: '请生成三个可视化方案',
                prompt: prompt,
                temperature: 0.7,
                user_id: userId,
                model_type: modelType
            })
            // body: JSON.stringify({
            //     model: apiModel,
            //     messages: [{ role: "user", content: prompt }],
            //     temperature: 0.7
            // })
        });

        if (!response.ok) {
            throw new Error('API请求失败');
        }

        const result = await response.json();
        // const generatedText = result.choices[0].message.content;
        const generatedText = result.data.result;

        // 解析返回的JSON数组
        let recommendations;
        try {
            // 清理返回的文本，移除可能的代码块标记
            let cleanedText = generatedText.trim()
                .replace(/```json\s*/g, '')
                .replace(/```\s*$/g, '')
                .replace(/^[^[]*/, '') // 移除数组前的任何文本
                .replace(/[^\]]*$/, ''); // 移除数组后的任何文本

            // 如果文本不以[开始，尝试提取数组
            if (!cleanedText.startsWith('[')) {
                const match = cleanedText.match(/\[[\s\S]*\]/);
                if (match) {
                    cleanedText = match[0];
                }
            }

            recommendations = JSON.parse(cleanedText);

            // 验证返回的是数组且包含3个元素
            if (!Array.isArray(recommendations) || recommendations.length !== 3) {
                throw new Error('返回的推荐格式不正确');
            }
        } catch (parseError) {
            console.error('解析推荐结果失败:', parseError);
            // 隐藏弹出式loading
            hideLoadingModal();
            addChatMessage('bot', '生成图表推荐时遇到问题，请直接告诉我您想要创建什么样的图表。');
            return;
        }

        // 隐藏弹出式loading
        hideLoadingModal();

        // 显示推荐消息
        addRecommendationMessage(recommendations);

    } catch (error) {
        console.error('生成推荐失败:', error);
        // 隐藏弹出式loading
        hideLoadingModal();
        addChatMessage('bot', '生成图表推荐时遇到问题，请直接告诉我您想要创建什么样的图表。');
    }
}

// 使用LLM API生成图表
async function generateChart(apiUrl, apiKey, apiModel, data, question) {
    const endpoint = apiUrl;

    // 检测表头占多少行
    const headerRows = detectHeaderRows(data);
    console.log(`检测到表头占 ${headerRows} 行`);

    // 使用前5行和后5行数据作为样本数据
    let sampleData = [];

    if (data.length <= 11) {
        // 如果数据总行数不超过11行（包含表头），使用全部数据
        sampleData = data;
    } else {
        // 取表头 + 前5行数据 + 后5行数据
        const header = data.slice(0, headerRows); // 表头（可能多行）
        const dataStartIndex = headerRows; // 数据开始的行索引
        const firstRows = data.slice(dataStartIndex, dataStartIndex + 5); // 前5行数据
        const lastRows = data.slice(-5); // 后5行数据

        sampleData = [...header, ...firstRows, ...lastRows];
    }

    const dataString = sampleData.map(row => row.join('\t')).join('\n');

    // 使用导入的 Prompt 函数，并传入header行数信息
    const prompt2 = getPrompt2(dataString, question, headerRows);

    try {
        // 获取用户选择的模型类型
        const modelType = document.getElementById('model-type').value || 'local';

        const apiUrl = 'https://chat-ai.groupama-sdig.com:8000/api/chart/llm'
        // const apiUrl = 'http://localhost:8000/api/chart/llm'
        const apiKey = 'c2stbWVuZ2NoYXQtYm90IQ=='
        const userId = getUserId(); // 获取用户ID
        const response = await fetch(`${apiUrl}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                query: question,
                prompt: prompt2,
                temperature: 0.7,
                user_id: userId,
                model_type: modelType
            })
            // body: JSON.stringify({
            //     model: apiModel,
            //     messages: [{ role: "user", content: prompt2 }],
            //     temperature: 0.7
            // })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'API请求失败');
        }

        const result = await response.json();
        console.log(result)
        // const generatedText = result.choices[0].message.content;
        const generatedText = result.data.result;
        
        // 处理返回的结果，提取JSON配置
        let templateConfig;
        try {
            // 预处理JSON字符串，移除JavaScript风格的注释和代码块标记
            let cleanedJsonText = generatedText.trim()
                .replace(/\/\/.*$/gm, '') // 移除单行注释
                .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
                .replace(/```json\s*/g, '') // 移除可能的json代码块开始标记
                .replace(/```\s*$/g, ''); // 移除可能的代码块结束标记
            
            // 如果文本以花括号开始，但不是以花括号结束，尝试提取完整JSON对象
            if (cleanedJsonText.startsWith('{') && !cleanedJsonText.endsWith('}')) {
                const match = cleanedJsonText.match(/(\{[\s\S]*\})/);
                if (match) {
                    cleanedJsonText = match[0];
                }
            }
            
            // 尝试直接解析清理后的文本为JSON
            templateConfig = JSON.parse(cleanedJsonText);
            
            // 验证必需的字段存在
            if (!templateConfig.dataProcessor || !templateConfig.chartTemplate) {
                throw new Error('返回的JSON缺少必需的dataProcessor或chartTemplate字段');
            }
            
            // 验证chartTemplate中的必要配置
            if (!templateConfig.chartTemplate.series) {
                throw new Error('返回的chartTemplate缺少必需的series配置');
            }
        } catch (error) {
            // 如果直接解析失败，尝试使用正则表达式提取花括号内的内容
            const configMatch = generatedText.match(/\{[\s\S]*?\}/);
            if (configMatch) {
                // 同样处理提取出的内容中的注释
                const cleanedConfig = configMatch[0]
                    .replace(/\/\/.*$/gm, '')
                    .replace(/\/\*[\s\S]*?\*\//g, '');
                
                try {
                    templateConfig = JSON.parse(cleanedConfig);
                    
                    // 验证必需的字段存在
                    if (!templateConfig.dataProcessor || !templateConfig.chartTemplate) {
                        throw new Error('返回的JSON缺少必需的dataProcessor或chartTemplate字段');
                    }
                } catch (parseError) {
                    throw new Error('无法解析返回的图表模板配置: ' + parseError.message);
                }
            } else {
                throw new Error('无法解析返回的图表模板配置');
            }
        }

        // 应用数据处理逻辑
        const processedData = {};
        const dataProcessor = templateConfig.dataProcessor;

        // 预处理数据：过滤空值、null值、undefined值
        // 使用检测到的表头行数来正确跳过表头
        const cleanData = data.slice(headerRows).map(row => {
            return row.map(cell => {
                // 处理各种空值情况
                if (cell === null || cell === undefined || cell === '' ||
                    (typeof cell === 'string' && cell.trim() === '') ||
                    cell === 'null' || cell === 'undefined' || cell === 'NULL') {
                    return null; // 统一转换为null，便于后续处理
                }
                return cell;
            });
        }).filter(row => {
            // 过滤掉完全为空的行
            return row.some(cell => cell !== null && cell !== undefined && cell !== '');
        });

        // 创建处理上下文，确保步骤之间可以相互引用结果
        const processingContext = {
            data: cleanData, // 使用清理后的数据
            rawData: data.slice(headerRows), // 保留原始数据的引用（跳过表头）
            // 添加数据处理工具函数
            utils: {
                // 安全的数值转换，过滤空值
                toNumber: (val) => {
                    if (val === null || val === undefined || val === '' ||
                        (typeof val === 'string' && val.trim() === '')) {
                        return 0;
                    }
                    const num = Number(val);
                    return isNaN(num) ? 0 : num;
                },
                // 数值格式化函数，避免过长小数位
                formatNumber: (value) => {
                    if (value === null || value === undefined || isNaN(value)) {
                        return value;
                    }

                    const num = Number(value);

                    // 如果是整数，直接返回
                    if (Number.isInteger(num)) {
                        return num;
                    }

                    // 获取小数部分
                    const decimalPart = num.toString().split('.')[1];
                    if (!decimalPart) {
                        return num;
                    }

                    const decimalLength = decimalPart.length;

                    // 根据小数位数决定保留的位数
                    if (decimalLength > 4) {
                        // 大于4位小数，保留4位
                        return parseFloat(num.toFixed(4));
                    } else if (decimalLength > 2) {
                        // 大于2位小于等于4位小数，保留2位
                        return parseFloat(num.toFixed(2));
                    } else {
                        // 小于等于2位小数，保持原样
                        return num;
                    }
                },
                // 过滤数组中的空值
                filterEmpty: (arr) => {
                    return arr.filter(item =>
                        item !== null && item !== undefined && item !== '' &&
                        !(typeof item === 'string' && item.trim() === '')
                    );
                },
                // 安全的字符串处理
                toString: (val) => {
                    if (val === null || val === undefined) return '';
                    return String(val).trim();
                }
            }
        };

        // 按顺序处理每个数据处理步骤
        for (const key in dataProcessor) {
            try {
                // 处理可能存在的占位符
                let processorCode = dataProcessor[key];

                // 替换所有{{placeholder}}格式的占位符
                const placeholderRegex = /\{\{(\w+)\}\}/g;
                let match;
                while (match = placeholderRegex.exec(processorCode)) {
                    const placeholder = match[1];
                    if (processedData[placeholder] !== undefined) {
                        // 全局替换所有匹配的占位符
                        processorCode = processorCode.replace(
                            new RegExp(`\\{\\{${placeholder}\\}\\}`, 'g'),
                            `processedData["${placeholder}"]`
                        );
                    } else {
                        throw new Error(`引用了未定义的数据处理步骤: "${placeholder}"`);
                    }
                }

                // 使用Function构造器创建函数，传入所有已处理的数据作为上下文
                let processorFn = new Function(
                    'processedData',
                    ...Object.keys(processingContext),
                    `return ${processorCode}`
                );

                // 执行函数并传入相应的参数值
                let result = processorFn(
                    processedData,
                    ...Object.values(processingContext)
                );

                // 对结果进行额外的空值清理（如果结果是数组）
                if (Array.isArray(result)) {
                    result = result.filter(item => {
                        if (item === null || item === undefined || item === '') return false;
                        if (typeof item === 'string' && item.trim() === '') return false;
                        if (typeof item === 'number' && isNaN(item)) return false;
                        return true;
                    });
                }

                // 保存结果到处理上下文和最终结果中
                processingContext[key] = result;
                processedData[key] = result;
            } catch (error) {
                console.error(`处理数据步骤 "${key}" 失败:`, error);
                throw new Error(`数据处理步骤 "${key}" 失败: ${error.message}`);
            }
        }

        // 将处理后的数据应用到图表模板中
        const chartTemplate = JSON.parse(JSON.stringify(templateConfig.chartTemplate));
        const applyData = (obj) => {
            for (const key in obj) {
                if (typeof obj[key] === 'string' && obj[key].match(/\{\{.*?\}\}/)) {
                    const placeholder = obj[key].match(/\{\{(.*?)\}\}/)[1];
                    obj[key] = processedData[placeholder];
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    applyData(obj[key]);
                }
            }
        };

        applyData(chartTemplate);

        // 应用图表布局和样式优化
        optimizeChartLayout(chartTemplate);

        console.log(chartTemplate)

        // 确保所有series都显示标签
        if (chartTemplate.series) {
            if (Array.isArray(chartTemplate.series)) {
                chartTemplate.series.forEach(series => {
                    if (!series.label) {
                        if (series.type === 'pie' || series.type === 'doughnut') {
                            series.label = {
                                show: true,
                                formatter: '{b}: {c} ({d}%)'
                            };
                        } else {
                            series.label = {
                                show: true,
                                position: 'top'
                            };
                        }
                    }
                });
            } else if (typeof chartTemplate.series === 'object' && !chartTemplate.series.label) {
                if (chartTemplate.series.type === 'pie' || chartTemplate.series.type === 'doughnut') {
                    chartTemplate.series.label = {
                        show: true,
                        formatter: '{b}: {c} ({d}%)'
                    };
                } else {
                    chartTemplate.series.label = {
                        show: true,
                        position: 'top'
                    };
                }
            }
        }

        // 渲染图表
        if (document.getElementById('chart-container')) {
            myChart.setOption(chartTemplate, true);
            console.log(chartTemplate)
        } else {
            console.error('图表容器元素不存在');
            throw new Error('图表容器元素不存在');
        }
        return templateConfig;
    } catch (error) {
        throw error;
    }
}

// 生成下一步操作建议
async function generateNextStepSuggestions(rawDataInput, currentConfig, chartType) {
    try {
        // 获取建议提示词
        const prompt = getSuggestionsPrompt(rawDataInput, currentConfig, chartType);

        // 获取用户选择的模型类型
        const modelType = document.getElementById('model-type').value || 'local';

        // 调用LLM API
        const apiUrl = 'https://chat-ai.groupama-sdig.com:8000/api/chart/llm'
        // const apiUrl = 'http://localhost:8000/api/chart/llm'
        const apiKey = 'c2stbWVuZ2NoYXQtYm90IQ=='
        const userId = getUserId(); // 获取用户ID

        const response = await fetch(`${apiUrl}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                query: '请生成下一步操作建议',
                prompt: prompt,
                temperature: 0.3,
                user_id: userId,
                model_type: modelType
            })
        });

        if (!response.ok) {
            throw new Error('API请求失败');
        }

        const result = await response.json();
        const generatedText = result.data.result;

        // 解析返回的建议
        let suggestions;
        try {
            // 清理返回的文本，移除可能的代码块标记
            let cleanedText = generatedText.trim()
                .replace(/```json\s*/g, '')
                .replace(/```\s*$/g, '')
                .replace(/^[^[]*/, '') // 移除数组前的任何文本
                .replace(/[^\]]*$/, ''); // 移除数组后的任何文本

            // 如果文本不以[开始，尝试找到第一个[
            if (!cleanedText.startsWith('[')) {
                const arrayMatch = cleanedText.match(/(\[[\s\S]*\])/);
                if (arrayMatch) {
                    cleanedText = arrayMatch[0];
                }
            }

            suggestions = JSON.parse(cleanedText);

            // 验证返回的是数组且包含有效元素
            if (!Array.isArray(suggestions) || suggestions.length === 0) {
                throw new Error('返回的建议格式不正确');
            }

            // 验证每个建议项的格式
            suggestions.forEach((suggestion, index) => {
                if (!suggestion.text || !suggestion.type) {
                    throw new Error(`建议项 ${index + 1} 格式不正确`);
                }
                if (!['chart-type', 'config-option'].includes(suggestion.type)) {
                    throw new Error(`建议项 ${index + 1} 的类型不正确`);
                }
            });

        } catch (parseError) {
            console.error('解析建议结果失败:', parseError);
            // 返回默认建议
            suggestions = [
                { "text": "切换为折线图", "type": "chart-type" },
                { "text": "切换为饼图", "type": "chart-type" },
                { "text": "调整图表颜色", "type": "config-option" },
                { "text": "修改图表标题", "type": "config-option" },
                { "text": "显示/隐藏数据标签", "type": "config-option" }
            ];
        }

        return suggestions;

    } catch (error) {
        console.error('生成建议失败:', error);
        // 返回默认建议
        return [
            { "text": "切换为折线图", "type": "chart-type" },
            { "text": "切换为饼图", "type": "chart-type" },
            { "text": "调整图表颜色", "type": "config-option" },
            { "text": "修改图表标题", "type": "config-option" },
            { "text": "显示/隐藏数据标签", "type": "config-option" }
        ];
    }
}
