// 事件处理模块 - 管理所有的事件监听器和处理函数

// 页面加载完成后初始化图表并尝试从localStorage加载API密钥
window.addEventListener('DOMContentLoaded', function() {
    initChart();

    // 从localStorage加载LLM配置
    const savedApiUrl = localStorage.getItem('llm_api_url');
    const savedApiKey = localStorage.getItem('llm_api_key');
    const savedApiModel = localStorage.getItem('llm_api_model');

    if (savedApiUrl) {
        document.getElementById('api-url').value = savedApiUrl;
    }
    if (savedApiKey) {
        document.getElementById('api-key').value = savedApiKey;
    }
    if (savedApiModel) {
        document.getElementById('api-model').value = savedApiModel;
    }

    // 初始化模型选择状态
    const modelSelect = document.getElementById('model-type');
    const privacyNotice = document.getElementById('privacy-notice');

    // 从localStorage加载模型选择偏好，默认选择本地模型
    const savedModelType = localStorage.getItem('model_type') || 'local';
    modelSelect.value = savedModelType;

    // 根据初始选择设置隐私提示显示状态（外部模型时显示）
    if (savedModelType === 'external') {
        privacyNotice.style.display = 'flex';
    } else {
        privacyNotice.style.display = 'none';
    }
});

// 窗口大小改变时重新调整图表大小
window.addEventListener('resize', function() {
    if (myChart) {
        setTimeout(() => {
            if (myChart && document.getElementById('chart-container')) {
                myChart.resize();
            }
        }, 100);
    }
});

// 窗口大小改变时重绘图表
window.addEventListener('resize', function() {
    if (myChart && document.getElementById('chart-container')) {
        myChart.resize();
    }
    if (fullscreenChart) {
        fullscreenChart.resize();
    }
});

// 点击模态窗口背景关闭模态窗口
window.addEventListener('click', function(e) {
    const settingsModal = document.getElementById('settings-modal');
    const chartTypeModal = document.getElementById('chart-type-modal');
    const historyModal = document.getElementById('history-modal');

    if (e.target === settingsModal) {
        settingsModal.classList.remove('active');
    }

    if (e.target === chartTypeModal) {
        chartTypeModal.classList.remove('active');
    }
    if (e.target === historyModal) {
        historyModal.classList.remove('active');
    }
});

// ESC键退出全屏
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isFullscreen) {
        exitFullscreen();
    }
});

// 图表类型选择事件
document.addEventListener('DOMContentLoaded', function() {
    const chartTypeItems = document.querySelectorAll('.chart-type-item');
    chartTypeItems.forEach(item => {
        item.addEventListener('click', function() {
            const chartType = this.getAttribute('data-type');

            // 更新选中状态
            chartTypeItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');

            // 切换图表类型
            switchChartType(chartType);
        });
    });
});

// 历史记录按钮事件
document.getElementById('history-btn').addEventListener('click', function() {
    showHistoryModal();
});

// 重新上传按钮事件（右侧）
document.getElementById('reupload-file-btn').addEventListener('click', function() {
    resetUpload();
});

// 切换视图按钮事件
document.getElementById('toggle-view-btn').addEventListener('click', function() {
    const isDataView = document.getElementById('data-display').classList.contains('active');
    if (isDataView) {
        showChartView();
    } else {
        showDataView();
    }
});

// 图表类型切换按钮事件
document.getElementById('chart-type-btn').addEventListener('click', function() {
    if (!currentChartData || !currentChartConfig) {
        addChatMessage('bot', '请先生成图表后再切换类型。');
        return;
    }
    document.getElementById('chart-type-modal').classList.add('active');
});

// 切换标签显示按钮事件
document.getElementById('toggle-labels-btn').addEventListener('click', function() {
    if (!myChart || !chartCreated) {
        addChatMessage('bot', '请先生成图表后再切换标签显示。');
        return;
    }
    toggleLabels();
});

// X轴标签显示控制checkbox事件
document.getElementById('show-all-xaxis-labels').addEventListener('change', function() {
    if (!myChart || !chartCreated) {
        addChatMessage('bot', '请先生成图表后再切换X轴标签显示。');
        this.checked = showAllXAxisLabels; // 恢复原状态
        return;
    }
    toggleXAxisLabels();
});

// 关闭图表类型切换模态窗口
document.getElementById('close-chart-type-modal').addEventListener('click', function() {
    document.getElementById('chart-type-modal').classList.remove('active');
});

// 关闭历史记录模态窗口
document.getElementById('close-history-modal').addEventListener('click', function() {
    document.getElementById('history-modal').classList.remove('active');
});

// 清空历史记录按钮事件
document.getElementById('clear-history-btn').addEventListener('click', function() {
    if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
        clearChartHistory();
        document.getElementById('history-modal').classList.remove('active');
    }
});

// 设置按钮点击事件
// document.getElementById('settings-btn').addEventListener('click', function() {
//     document.getElementById('settings-modal').classList.add('active');
// });

// 关闭模态窗口
document.getElementById('close-modal').addEventListener('click', function() {
    document.getElementById('settings-modal').classList.remove('active');
});

document.getElementById('cancel-settings').addEventListener('click', function() {
    document.getElementById('settings-modal').classList.remove('active');
});

// 保存LLM配置
document.getElementById('save-settings').addEventListener('click', function() {
    const apiUrl = document.getElementById('api-url').value.trim();
    const apiKey = document.getElementById('api-key').value.trim();
    const apiModel = document.getElementById('api-model').value.trim();

    // 保存配置到localStorage
    if (apiUrl) {
        localStorage.setItem('llm_api_url', apiUrl);
    }
    if (apiKey) {
        localStorage.setItem('llm_api_key', apiKey);
    }
    if (apiModel) {
        localStorage.setItem('llm_api_model', apiModel);
    }

    document.getElementById('settings-modal').classList.remove('active');

    // 显示保存成功提示
    addChatMessage('bot', 'LLM模型配置已保存成功！');
});

// 文件选择变化事件
document.getElementById('excel-file').addEventListener('change', function(e) {
    const fileInput = e.target;
    const file = fileInput.files[0];
    const fileNameEl = document.getElementById('file-name');

    if (file) {
        fileNameEl.textContent = file.name;
        // 自动触发数据预览
        processExcelFile(file);
    } else {
        fileNameEl.textContent = "未选择文件";
    }
});

// 模型选择变化事件
document.getElementById('model-type').addEventListener('change', function(e) {
    const selectedModel = e.target.value;
    const privacyNotice = document.getElementById('privacy-notice');

    // 保存用户选择到localStorage
    localStorage.setItem('model_type', selectedModel);

    if (selectedModel === 'external') {
        // 选择外部模型时显示隐私提示
        privacyNotice.style.display = 'flex';
    } else {
        // 选择本地模型时隐藏隐私提示
        privacyNotice.style.display = 'none';
    }
});

// 支持Enter键发送消息
document.getElementById('question').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('generate-btn').click();
    }
});

// 全屏按钮事件
document.getElementById('fullscreen-btn').addEventListener('click', function() {
    enterFullscreen();
});

// 退出全屏按钮事件
document.getElementById('exit-fullscreen-btn').addEventListener('click', function() {
    exitFullscreen();
});

// 全屏模式下的切换视图按钮事件
document.getElementById('fullscreen-toggle-btn').addEventListener('click', function() {
    const isDataView = document.getElementById('fullscreen-text').textContent === '数据预览';
    if (isDataView) {
        showFullscreenChartView();
    } else {
        showFullscreenDataView();
    }
});

// 下载按钮事件监听器
document.getElementById('download-btn').addEventListener('click', function() {
    showDownloadMenu(false);
});

// 全屏下载按钮事件监听器
document.getElementById('fullscreen-download-btn').addEventListener('click', function() {
    showDownloadMenu(true);
});

// 生成图表按钮点击事件
document.getElementById('generate-btn').addEventListener('click', async function() {
    const apiUrl = document.getElementById('api-url').value.trim() || '';
    const apiKey = document.getElementById('api-key').value.trim() || '';
    const apiModel = document.getElementById('api-model').value.trim() || '';
    const question = document.getElementById('question').value.trim();

    // if (!apiUrl || !apiKey || !apiModel) {
    //     showError('请点击右上角齿轮图标配置完整的LLM模型信息（API地址、密钥、模型名称）');
    //     return;
    // }

    if (!excelData) {
        showError('请先上传Excel文件');
        return;
    }

    if (!question) {
        showError('请输入您的问题');
        return;
    }

    // 添加用户消息到聊天（如果不是从建议点击触发的）
    if (!window.isFromSuggestionClick) {
        addChatMessage('user', question);
    } else {
        // 重置标志
        window.isFromSuggestionClick = false;
    }

    showLoadingModal('正在分析数据并生成图表，请稍候...');
    hideError();

    try {
        const result = await generateChart(apiUrl, apiKey, apiModel, excelData, question);
        hideLoadingModal();

        // 保存图表数据和配置以支持类型切换
        if (result && result.dataProcessor) {
            // 重新执行数据处理逻辑来保存处理后的数据
            const processedData = {};
            const dataProcessor = result.dataProcessor;

            // 检测表头占多少行
            const headerRows = detectHeaderRows(excelData);

            // 预处理数据：过滤空值、null值、undefined值
            const cleanData = excelData.slice(headerRows).map(row => {
                return row.map(cell => {
                    if (cell === null || cell === undefined || cell === '' ||
                        (typeof cell === 'string' && cell.trim() === '') ||
                        cell === 'null' || cell === 'undefined' || cell === 'NULL') {
                        return null;
                    }
                    return cell;
                });
            }).filter(row => {
                return row.some(cell => cell !== null && cell !== undefined && cell !== '');
            });

            // 创建处理上下文
            const processingContext = {
                data: cleanData,
                rawData: excelData.slice(headerRows),
                utils: {
                    toNumber: (val) => {
                        if (val === null || val === undefined || val === '' ||
                            (typeof val === 'string' && val.trim() === '')) {
                            return 0;
                        }
                        const num = Number(val);
                        return isNaN(num) ? 0 : num;
                    },
                    formatNumber: formatNumber,
                    filterEmpty: (arr) => {
                        return arr.filter(item =>
                            item !== null && item !== undefined && item !== '' &&
                            !(typeof item === 'string' && item.trim() === '')
                        );
                    },
                    toString: (val) => {
                        if (val === null || val === undefined) return '';
                        return String(val).trim();
                    }
                }
            };

            // 按顺序处理每个数据处理步骤
            for (const key in dataProcessor) {
                try {
                    let processorCode = dataProcessor[key];

                    // 替换占位符
                    const placeholderRegex = /\{\{(\w+)\}\}/g;
                    let match;
                    while (match = placeholderRegex.exec(processorCode)) {
                        const placeholder = match[1];
                        if (processedData[placeholder] !== undefined) {
                            processorCode = processorCode.replace(
                                new RegExp(`\\{\\{${placeholder}\\}\\}`, 'g'),
                                `processedData["${placeholder}"]`
                            );
                        }
                    }

                    // 创建并执行处理函数
                    let processorFn = new Function(
                        'processedData',
                        ...Object.keys(processingContext),
                        `return ${processorCode}`
                    );

                    let result = processorFn(
                        processedData,
                        ...Object.values(processingContext)
                    );

                    // 过滤结果中的空值
                    if (Array.isArray(result)) {
                        result = result.filter(item => {
                            if (item === null || item === undefined || item === '') return false;
                            if (typeof item === 'string' && item.trim() === '') return false;
                            if (typeof item === 'number' && isNaN(item)) return false;
                            return true;
                        });
                    }

                    processingContext[key] = result;
                    processedData[key] = result;
                } catch (error) {
                    console.warn(`无法保存数据处理步骤 "${key}":`, error);
                }
            }

            // 保存处理后的数据
            currentChartData = processedData;

            // 特别保存categories数据，用于横向柱状图
            if (!currentChartData.categories && result.chartTemplate && result.chartTemplate.xAxis && result.chartTemplate.xAxis.data) {
                currentChartData.categories = result.chartTemplate.xAxis.data;
                console.log('从chartTemplate.xAxis.data保存categories:', currentChartData.categories);
            }

            // 尝试检测当前图表类型
            if (result.chartTemplate && result.chartTemplate.series) {
                const firstSeries = Array.isArray(result.chartTemplate.series)
                    ? result.chartTemplate.series[0]
                    : result.chartTemplate.series;
                currentChartType = firstSeries.type || 'bar';
            }

            console.log('保存的图表数据:', currentChartData);
        }

        // 图表生成成功，自动切换到图表视图
        showChartView();
        chartCreated = true;

        // 在图表渲染完成后保存原始配置
        setTimeout(() => {
            if (myChart) {
                // 保存AI生成的原始图表配置（用于"原图"功能）
                originalChartConfig = JSON.parse(JSON.stringify(myChart.getOption()));

                // 同时保存原始图表的配置信息，特别是坐标轴数据
                const originalChartOption = myChart.getOption();
                currentChartConfig = {
                    ...result.chartTemplate,
                    // 保存原始图表的坐标轴配置
                    originalXAxis: originalChartOption.xAxis,
                    originalYAxis: originalChartOption.yAxis,
                    originalSeries: originalChartOption.series
                };

                console.log('保存的原始图表配置:', originalChartConfig);
                console.log('保存的图表配置:', currentChartConfig);
            }
        }, 100);

        // 显示下载按钮和图表类型切换按钮（图表生成成功后）
        document.getElementById('download-btn').style.display = 'flex';
        document.getElementById('chart-type-btn').style.display = 'flex';
        document.getElementById('toggle-labels-btn').style.display = 'flex';
        document.getElementById('xaxis-labels-control').style.display = 'flex';

        // 显示左侧历史记录按钮
        document.getElementById('history-btn').style.display = 'flex';

        // 检测并设置初始图表类型图标
        const detectedChartType = detectChartType(result.chartTemplate);
        updateChartTypeButtonIcon(detectedChartType);

        // 延迟添加到历史记录，确保图表完全渲染后再保存配置
        setTimeout(() => {
            if (myChart) {
                // 获取实际渲染后的图表配置
                const actualChartConfig = myChart.getOption();
                addToChartHistory(question, actualChartConfig, currentChartData, detectedChartType);
            }
        }, 200);

        // 添加成功消息到聊天
        addChatMessage('bot', '图表生成成功！我已经根据您的要求创建了可视化图表，您可以在右侧查看结果。');

        // // 生成下一步操作建议
        // setTimeout(async () => {
        //     try {
        //         // 准备原始数据输入（用于建议生成）
        //         const sampleDataForSuggestions = excelData.slice(0, 6).map(row => row.join('\t')).join('\n');

        //         // 获取当前图表类型
        //         const currentType = detectChartType(result.chartTemplate) || 'bar';

        //         // 生成建议
        //         const suggestions = await generateNextStepSuggestions(
        //             sampleDataForSuggestions,
        //             result.chartTemplate,
        //             currentType
        //         );

        //         // 显示建议
        //         addNextStepSuggestionsMessage(suggestions);
        //     } catch (error) {
        //         console.error('生成下一步建议失败:', error);
        //         // 如果生成建议失败，不影响主流程，只是不显示建议
        //     }
        // }, 1000); // 延迟1秒生成建议，确保图表渲染完成

        // 清空输入框
        document.getElementById('question').value = '';
    } catch (error) {
        hideLoadingModal();
        addChatMessage('bot', '抱歉，生成图表时遇到了问题。请尝试更换问题描述或重新尝试。');
    }
});
