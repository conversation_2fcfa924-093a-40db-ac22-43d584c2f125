// 下载管理模块 - 处理图表的下载功能

// 下载图表功能
function downloadChart(format = 'png') {
    if (!myChart || !chartCreated) {
        showError('请先生成图表后再下载');
        return;
    }

    try {
        let dataURL;
        let filename;

        if (format === 'png') {
            dataURL = myChart.getDataURL({
                type: 'png',
                pixelRatio: 2,
                backgroundColor: '#fff'
            });
            filename = `chart_${new Date().getTime()}.png`;
        } else if (format === 'jpg') {
            dataURL = myChart.getDataURL({
                type: 'jpeg',
                pixelRatio: 2,
                backgroundColor: '#fff'
            });
            filename = `chart_${new Date().getTime()}.jpg`;
        } else if (format === 'svg') {
            dataURL = myChart.getDataURL({
                type: 'svg'
            });
            filename = `chart_${new Date().getTime()}.svg`;
        }

        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 显示成功消息
        addChatMessage('bot', `图表已成功下载为 ${format.toUpperCase()} 格式！`);
    } catch (error) {
        console.error('下载图表失败:', error);
        showError('下载图表失败，请重试');
    }
}

// 下载全屏图表功能
function downloadFullscreenChart(format = 'png') {
    if (!fullscreenChart) {
        showError('请先在全屏模式下显示图表');
        return;
    }

    try {
        let dataURL;
        let filename;

        if (format === 'png') {
            dataURL = fullscreenChart.getDataURL({
                type: 'png',
                pixelRatio: 2,
                backgroundColor: '#fff'
            });
            filename = `fullscreen_chart_${new Date().getTime()}.png`;
        } else if (format === 'jpg') {
            dataURL = fullscreenChart.getDataURL({
                type: 'jpeg',
                pixelRatio: 2,
                backgroundColor: '#fff'
            });
            filename = `fullscreen_chart_${new Date().getTime()}.jpg`;
        } else if (format === 'svg') {
            dataURL = fullscreenChart.getDataURL({
                type: 'svg'
            });
            filename = `fullscreen_chart_${new Date().getTime()}.svg`;
        }

        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 显示成功消息
        addChatMessage('bot', `全屏图表已成功下载为 ${format.toUpperCase()} 格式！`);
    } catch (error) {
        console.error('下载全屏图表失败:', error);
        showError('下载图表失败，请重试');
    }
}

// 显示下载选项菜单
function showDownloadMenu(isFullscreen = false) {
    // 创建下载选项菜单
    const menu = document.createElement('div');
    menu.className = 'download-menu';
    menu.innerHTML = `
        <div class="download-menu-content">
            <div class="download-menu-header">选择下载格式</div>
            <button class="download-option" data-format="png">
                <i class="bi bi-file-earmark-image"></i>
                PNG 图片 (推荐)
            </button>
            <button class="download-option" data-format="jpg">
                <i class="bi bi-file-earmark-image"></i>
                JPG 图片
            </button>
            <button class="download-option" data-format="svg">
                <i class="bi bi-file-earmark-code"></i>
                SVG 矢量图
            </button>
        </div>
    `;

    // 添加样式
    menu.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10000;
        background: rgba(0, 0, 0, 0.8);
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const menuContent = menu.querySelector('.download-menu-content');
    menuContent.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        min-width: 250px;
    `;

    const header = menu.querySelector('.download-menu-header');
    header.style.cssText = `
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        text-align: center;
        color: #333;
    `;

    const options = menu.querySelectorAll('.download-option');
    options.forEach(option => {
        option.style.cssText = `
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 15px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            color: #333;
        `;

        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#f0f0f0';
            option.style.borderColor = '#007bff';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = 'white';
            option.style.borderColor = '#ddd';
        });

        option.addEventListener('click', () => {
            const format = option.getAttribute('data-format');
            document.body.removeChild(menu);

            if (isFullscreen) {
                downloadFullscreenChart(format);
            } else {
                downloadChart(format);
            }
        });
    });

    // 点击背景关闭菜单
    menu.addEventListener('click', (e) => {
        if (e.target === menu) {
            document.body.removeChild(menu);
        }
    });

    document.body.appendChild(menu);
}
