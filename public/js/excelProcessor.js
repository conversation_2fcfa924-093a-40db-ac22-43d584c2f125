// Excel数据处理模块 - 处理Excel文件的读取、解析和显示

// 处理Excel文件
function processExcelFile(file) {
    if (!file) {
        showError('请先选择Excel文件');
        return;
    }

    // 检查是否是重新上传（如果已经有数据，说明是重新上传）
    const isReupload = excelData !== null && excelData !== undefined;

    // 如果是重新上传，清空聊天消息并重置图表相关状态
    if (isReupload) {
        clearChatMessages();

        // 重置图表切换相关状态
        currentChartData = null;
        currentChartConfig = null;
        currentChartType = null;
        originalChartConfig = null;
        showLabels = true;
        showAllXAxisLabels = true;

        // 重置checkbox状态
        const checkbox = document.getElementById('show-all-xaxis-labels');
        if (checkbox) {
            checkbox.checked = true;
        }

        // 如果已经创建了图表，则重置图表
        if (chartCreated && myChart) {
            myChart.setOption({
                title: {
                    text: '等待数据...',
                    left: 'center',
                    top: 'center',
                    textStyle: {
                        color: '#999',
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                series: []
            });
            chartCreated = false;
        }

        // 隐藏图表相关的控制按钮
        document.getElementById('download-btn').style.display = 'none';
        document.getElementById('chart-type-btn').style.display = 'none';
        document.getElementById('toggle-labels-btn').style.display = 'none';
        document.getElementById('xaxis-labels-control').style.display = 'none';

        // 隐藏左侧历史记录按钮
        document.getElementById('history-btn').style.display = 'none';
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            workbook = XLSX.read(data, { type: 'array' });

            const firstSheetName = workbook.SheetNames[0];
            if (!firstSheetName) {
                showError('Excel 文件中没有找到工作表。');
                return;
            }

            // 设置当前工作表名称
            currentSheetName = firstSheetName;

            // 处理并显示当前工作表数据
            processAndDisplaySheet(currentSheetName);

            // 切换到数据预览视图
            showDataView();

            // 显示右侧控制按钮（下载按钮只在图表生成后显示）
            document.getElementById('reupload-file-btn').style.display = 'flex';
            document.getElementById('toggle-view-btn').style.display = 'flex';
            document.getElementById('fullscreen-btn').style.display = 'flex';

            // 添加聊天消息
            if (excelData && excelData.length > 0) {
                const uploadMessage = isReupload
                    ? `新文件 "${file.name}" 上传成功！我已经为您预览了数据。现在您可以告诉我想要创建什么样的图表。`
                    : `文件 "${file.name}" 上传成功！我已经为您预览了数据。现在您可以告诉我想要创建什么样的图表。`;

                addChatMessage('bot', uploadMessage);

                // 延迟生成图表推荐，确保数据预览先完成渲染
                setTimeout(() => {
                    generateVisualizationRecommendations(excelData, file.name);
                }, 500);
            } else {
                addChatMessage('bot', `文件 "${file.name}" 已加载，但所有行均为空或无有效数据。请检查文件内容。`);
            }

            hideError();
        } catch (error) {
            console.error("文件处理错误:", error);
            showError(`处理文件时出错: ${error.message}`);
        }
    };
    reader.readAsArrayBuffer(file);
}

// 处理并显示指定工作表的数据
function processAndDisplaySheet(sheetName) {
    if (!workbook || !workbook.Sheets[sheetName]) {
        showError(`工作表 "${sheetName}" 不存在。`);
        return;
    }

    try {
        const worksheet = workbook.Sheets[sheetName];

        // 使用高级方法构建HTML表格，支持合并单元格和空值过滤
        const htmlTable = buildTableFromSheet(worksheet);

        // 提取有效数据行用于图表生成（过滤空行并处理合并单元格）
        excelData = extractValidDataFromSheet(worksheet);

        // 显示数据预览
        showDataPreview(htmlTable, excelData, sheetName);
    } catch (error) {
        console.error('处理工作表数据时出错:', error);
        showError(`处理工作表 "${sheetName}" 时出错: ${error.message}`);
    }
}

/**
 * 从 worksheet 中提取有效的数据行（过滤空行），并处理合并单元格
 * @param {Object} worksheet - SheetJS 的 worksheet 对象。
 * @returns {Array} 有效数据行的二维数组。
 */
function extractValidDataFromSheet(worksheet) {
    if (!worksheet || !worksheet['!ref']) {
        return [];
    }

    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const merges = worksheet['!merges'] || [];
    const validRows = [];

    // 创建合并单元格值映射表
    const mergeValueMap = new Map();
    merges.forEach(merge => {
        // 获取合并单元格的源值（左上角单元格的值）
        const sourceCellAddress = { c: merge.s.c, r: merge.s.r };
        const sourceCellRef = XLSX.utils.encode_cell(sourceCellAddress);
        const sourceCell = worksheet[sourceCellRef];
        const sourceValue = sourceCell ? XLSX.utils.format_cell(sourceCell) : '';

        // 将合并范围内的所有单元格都映射到源值
        for (let R_merge = merge.s.r; R_merge <= merge.e.r; ++R_merge) {
            for (let C_merge = merge.s.c; C_merge <= merge.e.c; ++C_merge) {
                const mergedCellKey = `${R_merge}_${C_merge}`;
                mergeValueMap.set(mergedCellKey, sourceValue);
            }
        }
    });

    for (let R = range.s.r; R <= range.e.r; ++R) {
        // 检查当前行是否有效（非空）
        let isRowEffectivelyEmpty = true;
        const currentRow = [];

        // 先构建当前行的数据，处理合并单元格
        for (let C = range.s.c; C <= range.e.c; ++C) {
            const cellAddress = { c: C, r: R };
            const cellRef = XLSX.utils.encode_cell(cellAddress);
            const cell = worksheet[cellRef];
            const cellKey = `${R}_${C}`;

            let cellContent = '';

            // 首先尝试获取单元格的直接值
            if (cell) {
                cellContent = XLSX.utils.format_cell(cell);
            }

            // 如果单元格为空或null，检查是否有合并单元格的值可以使用
            if (!cellContent || cellContent.trim() === '') {
                if (mergeValueMap.has(cellKey)) {
                    cellContent = mergeValueMap.get(cellKey);
                }
            }

            currentRow.push(cellContent);
        }

        // 检查行是否有效（与buildTableFromSheet中的逻辑保持一致）
        for (let C_check = range.s.c; C_check <= range.e.c; ++C_check) {
            const cellAddressForRowCheck = { c: C_check, r: R };
            const cellRefForRowCheck = XLSX.utils.encode_cell(cellAddressForRowCheck);
            const currentCellObject = worksheet[cellRefForRowCheck];
            const cellKeyForCheck = `${R}_${C_check}`;
            let actualCellContentForThisRow = "";

            if (currentCellObject) {
                let isCoveredByPriorRowspan = false;
                // 检查此单元格是否被先前行的 rowspan 覆盖
                if (merges && merges.length > 0) {
                    for (const merge of merges) {
                        if (R >= merge.s.r && R <= merge.e.r && C_check >= merge.s.c && C_check <= merge.e.c) {
                            if (merge.s.r < R) {
                                isCoveredByPriorRowspan = true;
                                break;
                            }
                        }
                    }
                }

                if (!isCoveredByPriorRowspan) {
                    actualCellContentForThisRow = XLSX.utils.format_cell(currentCellObject).trim();
                }
            }

            // 如果直接单元格内容为空，检查合并单元格值
            if (actualCellContentForThisRow === '' && mergeValueMap.has(cellKeyForCheck)) {
                actualCellContentForThisRow = mergeValueMap.get(cellKeyForCheck).trim();
            }

            if (actualCellContentForThisRow !== '') {
                isRowEffectivelyEmpty = false;
                break;
            }
        }

        // 如果行不为空，添加到有效行列表中
        if (!isRowEffectivelyEmpty) {
            validRows.push(currentRow);
        }
    }

    return validRows;
}

/**
 * 根据 SheetJS 的 worksheet 对象构建 HTML 表格，并处理合并单元格及过滤空行。
 * @param {Object} worksheet - SheetJS 的 worksheet 对象。
 * @returns {string} HTML 表格字符串。
 */
function buildTableFromSheet(worksheet) {
    if (!worksheet || !worksheet['!ref']) {
        return '<p class="no-data-message">工作表数据为空或格式不正确。</p>';
    }

    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const merges = worksheet['!merges'] || [];

    // 创建合并单元格跟踪器
    const mergedCellsTracker = [];
    for (let R_tracker = 0; R_tracker <= range.e.r; ++R_tracker) {
        mergedCellsTracker[R_tracker] = [];
        for (let C_tracker = 0; C_tracker <= range.e.c; ++C_tracker) {
            mergedCellsTracker[R_tracker][C_tracker] = false;
        }
    }

    // 标记被合并的单元格
    merges.forEach(merge => {
        for (let R_merge = merge.s.r; R_merge <= merge.e.r; ++R_merge) {
            for (let C_merge = merge.s.c; C_merge <= merge.e.c; ++C_merge) {
                if (R_merge !== merge.s.r || C_merge !== merge.s.c) {
                    mergedCellsTracker[R_merge][C_merge] = true;
                }
            }
        }
    });

    let html = '<table class="excel-table">';
    let hasVisibleRows = false;
    let displayedRows = 0;
    const maxDisplayRows = 50; // 最多显示30行

    for (let R = range.s.r; R <= range.e.r && displayedRows < maxDisplayRows; ++R) {
    // for (let R = range.s.r; R <= range.e.r; ++R) {
        // 检查当前行是否有效（非空）
        let isRowEffectivelyEmpty = true;
        for (let C_check = range.s.c; C_check <= range.e.c; ++C_check) {
            const cellAddressForRowCheck = { c: C_check, r: R };
            const cellRefForRowCheck = XLSX.utils.encode_cell(cellAddressForRowCheck);
            const currentCellObject = worksheet[cellRefForRowCheck];
            let actualCellContentForThisRow = "";

            if (currentCellObject) {
                let isCoveredByPriorRowspan = false;
                // 检查此单元格是否被先前行的 rowspan 覆盖
                if (merges && merges.length > 0) {
                    for (const merge of merges) {
                        if (R >= merge.s.r && R <= merge.e.r && C_check >= merge.s.c && C_check <= merge.e.c) {
                            if (merge.s.r < R) {
                                isCoveredByPriorRowspan = true;
                                break;
                            }
                        }
                    }
                }

                if (!isCoveredByPriorRowspan) {
                    actualCellContentForThisRow = XLSX.utils.format_cell(currentCellObject).trim();
                }
            }

            if (actualCellContentForThisRow !== '') {
                isRowEffectivelyEmpty = false;
                break;
            }
        }

        if (isRowEffectivelyEmpty) {
            continue; // 跳过空行
        }

        hasVisibleRows = true;
        displayedRows++;
        html += '<tr>';

        for (let C = range.s.c; C <= range.e.c; ++C) {
            if (mergedCellsTracker[R][C]) {
                continue; // 跳过被合并的单元格
            }

            const cellAddress = { c: C, r: R };
            const cellRef = XLSX.utils.encode_cell(cellAddress);
            const cell = worksheet[cellRef];

            let cellContent = cell ? XLSX.utils.format_cell(cell) : '';

            // 处理空值显示
            if (!cellContent || cellContent.trim() === '') {
                cellContent = '<span class="empty-cell">(空)</span>';
            } else {
                // HTML转义
                cellContent = cellContent.replace(/&/g, "&amp;")
                                       .replace(/</g, "&lt;")
                                       .replace(/>/g, "&gt;")
                                       .replace(/"/g, "&quot;")
                                       .replace(/'/g, "&#039;");
            }

            let colspan = 1;
            let rowspan = 1;

            // 检查合并单元格信息
            const mergeInfo = merges.find(m => m.s.r === R && m.s.c === C);
            if (mergeInfo) {
                colspan = mergeInfo.e.c - mergeInfo.s.c + 1;
                rowspan = mergeInfo.e.r - mergeInfo.s.r + 1;
            }

            // 判断是否为表头
            let tag = 'td';
            let isHeaderCellCandidate = (R === range.s.r);
            if (isHeaderCellCandidate) {
                for (const merge of merges) {
                    if (merge.s.r < R && merge.e.r >= R && merge.s.c <= C && merge.e.c >= C) {
                        if(merge.s.c === C) {
                             isHeaderCellCandidate = false;
                             break;
                        }
                    }
                }
            }
            if(isHeaderCellCandidate) tag = 'th';

            html += `<${tag}`;
            if (colspan > 1) html += ` colspan="${colspan}"`;
            if (rowspan > 1) html += ` rowspan="${rowspan}"`;
            html += `>${cellContent}</${tag}>`;
        }
        html += '</tr>';
    }
    html += '</table>';

    // 如果没有可见行，返回提示信息
    if (!hasVisibleRows) {
        return '<p class="no-data-message">文件中所有行均为空或无有效数据可显示。</p>';
    }

    return html;
}
