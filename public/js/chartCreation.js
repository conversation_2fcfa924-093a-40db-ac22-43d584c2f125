// 图表创建模块 - 处理图表的创建、切换和配置生成

// 从当前图表中提取类别数据的辅助函数
function extractCategoriesFromCurrentChart() {
    if (!myChart || !myChart.getOption) {
        return [];
    }

    const currentOption = myChart.getOption();

    // 尝试从X轴获取类别数据
    if (currentOption.xAxis) {
        const xAxis = Array.isArray(currentOption.xAxis) ? currentOption.xAxis[0] : currentOption.xAxis;
        if (xAxis && xAxis.data && Array.isArray(xAxis.data) && xAxis.data.length > 0) {
            // 过滤掉"项目"和"类别"开头的默认标签
            const categories = xAxis.data.filter(item =>
                !String(item).startsWith('项目') &&
                !String(item).startsWith('类别') &&
                String(item).trim() !== ''
            );
            if (categories.length > 0) {
                console.log('从当前图表X轴提取到有效类别:', categories);
                return categories.map(cat => String(cat));
            }
        }
    }

    // 尝试从Y轴获取类别数据
    if (currentOption.yAxis) {
        const yAxis = Array.isArray(currentOption.yAxis) ? currentOption.yAxis[0] : currentOption.yAxis;
        if (yAxis && yAxis.data && Array.isArray(yAxis.data) && yAxis.data.length > 0) {
            // 过滤掉"项目"和"类别"开头的默认标签
            const categories = yAxis.data.filter(item =>
                !String(item).startsWith('项目') &&
                !String(item).startsWith('类别') &&
                String(item).trim() !== ''
            );
            if (categories.length > 0) {
                console.log('从当前图表Y轴提取到有效类别:', categories);
                return categories.map(cat => String(cat));
            }
        }
    }

    return [];
}

// 图表类型切换功能
function switchChartType(newType) {
    console.log('切换图表类型到:', newType);
    console.log('当前图表数据:', currentChartData);
    console.log('当前图表配置:', currentChartConfig);

    if (!currentChartData || !currentChartConfig) {
        addChatMessage('bot', '请先生成图表后再切换类型。');
        return;
    }

    if (!chartTypeTemplates[newType]) {
        addChatMessage('bot', '不支持的图表类型。');
        return;
    }

    try {
        // 获取新图表类型的模板
        const template = chartTypeTemplates[newType];

        let newChartConfig;

        if (newType === 'original') {
            // 如果选择原图，直接使用保存的原始配置
            if (!originalChartConfig) {
                throw new Error('没有找到原始图表配置');
            }
            newChartConfig = JSON.parse(JSON.stringify(originalChartConfig));
            console.log('使用原始图表配置:', newChartConfig);

            // 原图不需要额外的布局优化，直接使用保存的配置
        } else {
            // 创建新的图表配置
            newChartConfig = createChartConfigFromTemplate(newType, currentChartData, currentChartConfig);

            console.log('生成的新图表配置:', newChartConfig);

            // 验证配置是否有效
            if (!newChartConfig || !newChartConfig.series || newChartConfig.series.length === 0) {
                throw new Error('生成的图表配置无效或没有数据系列');
            }

            // 应用图表布局和样式优化
            optimizeChartLayout(newChartConfig);
        }

        // 渲染新图表
        if (myChart && document.getElementById('chart-container')) {
            myChart.setOption(newChartConfig, true);
            currentChartType = newType;

            // 更新图表类型按钮图标
            updateChartTypeButtonIcon(newType);

            // 更新聊天消息
            addChatMessage('bot', `已成功切换到${template.name}！`);

            // 关闭模态窗口
            document.getElementById('chart-type-modal').classList.remove('active');
        } else {
            throw new Error('图表容器不存在');
        }
    } catch (error) {
        console.error('切换图表类型失败:', error);
        addChatMessage('bot', `切换图表类型时遇到问题：${error.message}。请重新尝试。`);
    }
}

// 根据模板创建图表配置
function createChartConfigFromTemplate(chartType, chartData, originalConfig) {
    const template = chartTypeTemplates[chartType];

    console.log('创建图表配置，类型:', chartType, '数据:', chartData, '原始配置:', originalConfig);

    // 基础配置
    const newConfig = {
        title: originalConfig.title || {},
        tooltip: originalConfig.tooltip || {},
        legend: originalConfig.legend || {},
        grid: originalConfig.grid || {},
        series: []
    };

    // 根据图表类型处理数据和配置
    switch (chartType) {
        case 'bar':
        case 'line':
        case 'area':
            newConfig.series = createBarLineAreaSeries(chartType, chartData, template.template);
            // 为柱状图、折线图、面积图设置坐标轴，优先使用原始图表的坐标轴配置
            newConfig.xAxis = createXAxisConfig(chartData, originalConfig.originalXAxis || originalConfig.xAxis);
            newConfig.yAxis = createYAxisConfig(chartData, originalConfig.originalYAxis || originalConfig.yAxis);
            break;
        case 'horizontal-bar':
            newConfig.series = createHorizontalBarSeries(chartData, template.template);
            // 横向柱状图需要交换X轴和Y轴的类型
            newConfig.xAxis = {
                type: 'value',
                // 为横向柱状图的X轴添加特殊配置
                axisLabel: {
                    show: true, // 确保标签显示
                    fontSize: 11,
                    color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
                    margin: 8
                },
                // 确保X轴有足够的空间显示数值（为右侧标签留出空间）
                max: function(value) {
                    // 自动计算最大值，并留出30%的空间给右侧标签
                    return Math.ceil(value.max * 1.3);
                },
                ...(originalConfig.originalYAxis || originalConfig.yAxis || {})
            };

            // 获取Y轴类别数据
            let yAxisData = [];

            console.log('横向柱状图数据获取 - chartData:', chartData);
            console.log('横向柱状图数据获取 - currentChartData:', currentChartData);
            console.log('横向柱状图数据获取 - originalConfig:', originalConfig);

            // 方法0：检查是否是从饼图切换过来的数据（最优先）
            if (currentChartConfig && currentChartConfig.series && Array.isArray(currentChartConfig.series)) {
                const currentSeries = currentChartConfig.series[0];
                if (currentSeries && currentSeries.type === 'pie' && currentSeries.data && Array.isArray(currentSeries.data)) {
                    console.log('从饼图数据中提取横向柱状图Y轴类别:', currentSeries.data);

                    const pieData = currentSeries.data;
                    if (pieData.length > 0 && typeof pieData[0] === 'object' && pieData[0].name !== undefined) {
                        yAxisData = pieData.map(item => String(item.name));
                        console.log('方法0成功 - 从饼图数据获取Y轴类别:', yAxisData);
                    }
                }
            }

            // 如果从饼图没有获取到数据，继续尝试其他方法
            if (yAxisData.length === 0) {
                // 方法0.5：使用辅助函数从当前图表提取有效类别
                const extractedCategories = extractCategoriesFromCurrentChart();
                if (extractedCategories.length > 0) {
                    yAxisData = extractedCategories;
                    console.log('方法0.5成功 - 从当前图表提取有效类别:', yAxisData);
                }
            }

            if (yAxisData.length === 0) {
                // 方法1：从原始X轴配置获取（最优先，因为这是原图的类别标签）
                if (originalConfig.originalXAxis) {
                    if (Array.isArray(originalConfig.originalXAxis)) {
                        // 如果是数组，取第一个
                        const xAxis = originalConfig.originalXAxis[0];
                        if (xAxis && xAxis.data && Array.isArray(xAxis.data)) {
                            yAxisData = xAxis.data.map(cat => String(cat));
                            console.log('方法1a成功 - 从originalConfig.originalXAxis[0].data获取:', yAxisData);
                        }
                    } else if (originalConfig.originalXAxis.data && Array.isArray(originalConfig.originalXAxis.data)) {
                        // 如果是对象
                        yAxisData = originalConfig.originalXAxis.data.map(cat => String(cat));
                        console.log('方法1b成功 - 从originalConfig.originalXAxis.data获取:', yAxisData);
                    }
                }

                // 方法2：从当前X轴配置获取
                if (yAxisData.length === 0 && originalConfig.xAxis) {
                    if (Array.isArray(originalConfig.xAxis)) {
                        const xAxis = originalConfig.xAxis[0];
                        if (xAxis && xAxis.data && Array.isArray(xAxis.data)) {
                            yAxisData = xAxis.data.map(cat => String(cat));
                            console.log('方法2a成功 - 从originalConfig.xAxis[0].data获取:', yAxisData);
                        }
                    } else if (originalConfig.xAxis.data && Array.isArray(originalConfig.xAxis.data)) {
                        yAxisData = originalConfig.xAxis.data.map(cat => String(cat));
                        console.log('方法2b成功 - 从originalConfig.xAxis.data获取:', yAxisData);
                    }
                }

                // 方法3：从chartData.categories获取
                if (yAxisData.length === 0 && chartData.categories && Array.isArray(chartData.categories)) {
                    yAxisData = chartData.categories.map(cat => String(cat));
                    console.log('方法3成功 - 从chartData.categories获取:', yAxisData);
                }

                // 方法4：从currentChartData.categories获取
                if (yAxisData.length === 0 && currentChartData && currentChartData.categories && Array.isArray(currentChartData.categories)) {
                    yAxisData = currentChartData.categories.map(cat => String(cat));
                    console.log('方法4成功 - 从currentChartData.categories获取:', yAxisData);
                }
                // 方法5：尝试从chartData的其他字段获取类别信息
                if (yAxisData.length === 0) {
                    const keys = Object.keys(chartData);
                    console.log('横向柱状图Y轴可用的数据键:', keys);

                    // 检查是否有饼图格式的数据
                    const pieDataKey = keys.find(key => {
                        const data = chartData[key];
                        return Array.isArray(data) && data.length > 0 &&
                               typeof data[0] === 'object' && data[0].name !== undefined;
                    });

                    if (pieDataKey) {
                        console.log('从饼图格式数据中提取Y轴类别:', pieDataKey);
                        yAxisData = chartData[pieDataKey].map(item => String(item.name));
                        console.log('方法5a成功 - 从饼图格式数据获取:', yAxisData);
                    } else {
                        // 寻找可能的类别字段
                        const categoryKey = keys.find(key =>
                            Array.isArray(chartData[key]) &&
                            chartData[key].length > 0 &&
                            (key.toLowerCase().includes('category') ||
                             key.toLowerCase().includes('name') ||
                             key.toLowerCase().includes('label') ||
                             key.toLowerCase().includes('x') ||
                             key.toLowerCase().includes('axis'))
                        );

                        console.log('找到的类别键:', categoryKey);

                        if (categoryKey && Array.isArray(chartData[categoryKey])) {
                            yAxisData = chartData[categoryKey].map(cat => String(cat));
                            console.log('方法5b成功 - 从chartData其他字段获取:', yAxisData);
                        } else {
                            // 最后尝试从当前图表的实际显示数据中获取
                            if (myChart && myChart.getOption) {
                                const currentOption = myChart.getOption();
                                if (currentOption.xAxis && currentOption.xAxis[0] && currentOption.xAxis[0].data) {
                                    yAxisData = currentOption.xAxis[0].data.map(cat => String(cat));
                                    console.log('方法6a - 从当前图表X轴获取:', yAxisData);
                                } else if (currentOption.yAxis && currentOption.yAxis[0] && currentOption.yAxis[0].data) {
                                    yAxisData = currentOption.yAxis[0].data.map(cat => String(cat));
                                    console.log('方法6b - 从当前图表Y轴获取:', yAxisData);
                                }
                            }

                            // 如果仍然没有数据，才生成默认类别
                            if (yAxisData.length === 0) {
                                const firstSeries = newConfig.series[0];
                                if (firstSeries && firstSeries.data && Array.isArray(firstSeries.data)) {
                                    yAxisData = firstSeries.data.map((_, index) => `类别${index + 1}`);
                                    console.log('方法7 - 生成默认类别:', yAxisData);
                                }
                            }
                        }
                    }
                }
            }

            console.log('最终横向柱状图Y轴数据:', yAxisData);

            // 构建Y轴配置，确保data不被覆盖
            const baseYAxisConfig = originalConfig.originalXAxis || originalConfig.xAxis || {};
            newConfig.yAxis = {
                type: 'category',
                ...baseYAxisConfig,
                data: yAxisData  // 确保data在最后设置，不会被覆盖
            };
            break;
        case 'pie':
            newConfig.series = createPieSeries(chartData, template.template);
            // 饼图不需要坐标轴
            break;
        default:
            throw new Error(`不支持的图表类型: ${chartType}`);
    }

    console.log('生成的新配置:', newConfig);
    return newConfig;
}

// 创建X轴配置
function createXAxisConfig(chartData, originalXAxis) {
    console.log('创建X轴配置，输入数据:', chartData, '原始X轴:', originalXAxis);

    // 优先使用原始图表的X轴配置
    if (originalXAxis) {
        // 处理数组形式的X轴配置
        if (Array.isArray(originalXAxis) && originalXAxis.length > 0) {
            const firstXAxis = originalXAxis[0];
            if (firstXAxis && firstXAxis.data && Array.isArray(firstXAxis.data) && firstXAxis.data.length > 0) {
                console.log('使用原始X轴数据（数组形式）:', firstXAxis.data);
                return {
                    type: 'category',
                    ...firstXAxis
                };
            }
        }
        // 处理对象形式的X轴配置
        else if (originalXAxis.data && Array.isArray(originalXAxis.data) && originalXAxis.data.length > 0) {
            console.log('使用原始X轴数据（对象形式）:', originalXAxis.data);
            return {
                type: 'category',
                ...originalXAxis
            };
        }
    }

    // 检查是否是从饼图切换过来的数据，从饼图数据中提取类别名称
    if (currentChartConfig && currentChartConfig.series && Array.isArray(currentChartConfig.series)) {
        const currentSeries = currentChartConfig.series[0];
        if (currentSeries && currentSeries.type === 'pie' && currentSeries.data && Array.isArray(currentSeries.data)) {
            console.log('从饼图数据中提取X轴类别:', currentSeries.data);

            const pieData = currentSeries.data;
            if (pieData.length > 0 && typeof pieData[0] === 'object' && pieData[0].name !== undefined) {
                const categories = pieData.map(item => String(item.name));
                console.log('从饼图提取的类别:', categories);

                return {
                    type: 'category',
                    data: categories,
                    ...originalXAxis
                };
            }
        }
    }

    // 如果没有原始X轴数据，则从chartData中提取
    const xAxisConfig = {
        type: 'category',
        data: [],
        ...originalXAxis
    };

    // 尝试从数据中提取类别信息
    if (chartData.categories && Array.isArray(chartData.categories)) {
        xAxisConfig.data = chartData.categories.map(cat => String(cat));
    } else {
        // 尝试从其他数据结构中提取类别
        const keys = Object.keys(chartData);
        console.log('X轴配置可用键:', keys);

        // 检查是否有饼图格式的数据
        const pieDataKey = keys.find(key => {
            const data = chartData[key];
            return Array.isArray(data) && data.length > 0 &&
                   typeof data[0] === 'object' && data[0].name !== undefined;
        });

        if (pieDataKey) {
            console.log('从饼图格式数据中提取类别:', pieDataKey);
            xAxisConfig.data = chartData[pieDataKey].map(item => String(item.name));
        } else {
            const categoryKey = keys.find(key =>
                key.toLowerCase().includes('category') ||
                key.toLowerCase().includes('name') ||
                key.toLowerCase().includes('label') ||
                key.toLowerCase().includes('x') ||
                key.toLowerCase().includes('axis')
            );

            console.log('找到的类别键:', categoryKey);

            if (categoryKey && Array.isArray(chartData[categoryKey])) {
                xAxisConfig.data = chartData[categoryKey].map(cat => String(cat));
            } else {
                // 使用辅助函数从当前图表提取有效类别
                const extractedCategories = extractCategoriesFromCurrentChart();
                if (extractedCategories.length > 0) {
                    xAxisConfig.data = extractedCategories;
                    console.log('从当前图表提取有效类别:', xAxisConfig.data);
                }

                // 如果仍然没有数据，尝试从数据键中提取
                if (xAxisConfig.data.length === 0) {
                    const dataKeys = keys.filter(key =>
                        Array.isArray(chartData[key]) &&
                        chartData[key].length > 0 &&
                        !key.toLowerCase().includes('category') &&
                        !key.toLowerCase().includes('name') &&
                        !key.toLowerCase().includes('label')
                    );

                    console.log('用于生成X轴标签的数据键:', dataKeys);

                    if (dataKeys.length > 0) {
                        const firstDataArray = chartData[dataKeys[0]];
                        // 检查是否是饼图格式数据
                        if (firstDataArray.length > 0 && typeof firstDataArray[0] === 'object' && firstDataArray[0].value !== undefined) {
                            xAxisConfig.data = firstDataArray.map(item => String(item.name || `类别${firstDataArray.indexOf(item) + 1}`));
                        } else {
                            xAxisConfig.data = firstDataArray.map((_, index) => `类别${index + 1}`);
                        }
                    } else {
                        // 最后的备选方案
                        xAxisConfig.data = ['类别1', '类别2', '类别3', '类别4', '类别5'];
                    }
                }
            }
        }
    }

    console.log('生成的X轴配置:', xAxisConfig);
    return xAxisConfig;
}

// 创建Y轴配置
function createYAxisConfig(chartData, originalYAxis) {
    const yAxisConfig = {
        type: 'value',
        ...originalYAxis
    };

    console.log('生成的Y轴配置:', yAxisConfig);
    return yAxisConfig;
}

// 创建横向柱状图系列
function createHorizontalBarSeries(chartData, template) {
    const series = [];

    console.log('创建横向柱状图，数据:', chartData);

    // 首先尝试从当前图表配置的原始系列数据中提取
    if (currentChartConfig && currentChartConfig.originalSeries && Array.isArray(currentChartConfig.originalSeries)) {
        console.log('使用原始系列数据:', currentChartConfig.originalSeries);
        currentChartConfig.originalSeries.forEach(originalSeries => {
            if (originalSeries.data && Array.isArray(originalSeries.data)) {
                const seriesConfig = {
                    name: originalSeries.name || '数据系列',
                    type: 'bar',
                    data: originalSeries.data.map(val => formatNumber(val)),
                    ...template,
                    label: {
                        show: showLabels,
                        position: 'right',
                        formatter: function(params) {
                            return formatNumber(params.value);
                        }
                    }
                };
                series.push(seriesConfig);
            }
        });

        if (series.length > 0) {
            console.log('使用原始系列数据生成的横向柱状图系列:', series);
            return series;
        }
    }

    // 检查是否是从饼图切换过来的数据（饼图数据格式为 [{name, value}]）
    if (currentChartConfig && currentChartConfig.series && Array.isArray(currentChartConfig.series)) {
        const currentSeries = currentChartConfig.series[0];
        if (currentSeries && currentSeries.type === 'pie' && currentSeries.data && Array.isArray(currentSeries.data)) {
            console.log('检测到饼图数据，进行横向柱状图转换:', currentSeries.data);

            // 从饼图数据中提取数值
            const pieData = currentSeries.data;
            if (pieData.length > 0 && typeof pieData[0] === 'object' && pieData[0].value !== undefined) {
                const values = pieData.map(item => formatNumber(item.value || 0));

                const seriesConfig = {
                    name: currentSeries.name || '数据系列',
                    type: 'bar',
                    data: values,
                    ...template,
                    label: {
                        show: showLabels,
                        position: 'right',
                        formatter: function(params) {
                            return formatNumber(params.value);
                        }
                    }
                };
                series.push(seriesConfig);

                console.log('从饼图数据转换的横向柱状图系列:', series);
                return series;
            }
        }
    }

    // 如果没有原始系列数据，则从chartData中提取
    if (chartData.categories && chartData.values) {
        // 使用categories和values数据
        const seriesConfig = {
            name: chartData.seriesName || '数据',
            type: 'bar',
            data: Array.isArray(chartData.values) ? chartData.values.map(value => formatNumber(value)) : [],
            ...template,
            label: {
                show: showLabels,
                position: 'right',
                formatter: function(params) {
                    return formatNumber(params.value);
                }
            }
        };

        series.push(seriesConfig);
    } else if (chartData.series && Array.isArray(chartData.series)) {
        // 多系列数据
        chartData.series.forEach(seriesData => {
            let data = [];

            // 检查是否是饼图格式的数据
            if (seriesData.data && Array.isArray(seriesData.data) &&
                seriesData.data.length > 0 && typeof seriesData.data[0] === 'object' &&
                seriesData.data[0].value !== undefined) {
                // 饼图格式数据，提取数值
                data = seriesData.data.map(item => formatNumber(item.value || 0));
            } else {
                // 普通数值数组
                data = seriesData.data ? seriesData.data.map(val => formatNumber(val)) : [];
            }

            const seriesConfig = {
                name: seriesData.name || '数据系列',
                type: 'bar',
                data: data,
                ...template,
                label: {
                    show: showLabels,
                    position: 'right',
                    formatter: function(params) {
                        return formatNumber(params.value);
                    }
                }
            };
            series.push(seriesConfig);
        });
    } else {
        // 尝试从其他数据结构中提取
        const keys = Object.keys(chartData);
        console.log('横向柱状图可用的数据键:', keys);

        // 排除可能的类别字段，寻找数值数据
        const excludeKeys = ['categories', 'labels', 'names', 'x', 'xAxis'];
        const possibleDataKeys = keys.filter(key =>
            Array.isArray(chartData[key]) &&
            chartData[key].length > 0 &&
            !excludeKeys.some(exclude => key.toLowerCase().includes(exclude.toLowerCase()))
        );

        console.log('可能的数据键:', possibleDataKeys);

        if (possibleDataKeys.length > 0) {
            possibleDataKeys.forEach(dataKey => {
                // 确保数据数组的完整性
                const rawData = chartData[dataKey];
                let processedData = [];

                // 检查是否是饼图格式的数据
                if (rawData.length > 0 && typeof rawData[0] === 'object' && rawData[0].value !== undefined) {
                    // 饼图格式数据，提取数值
                    processedData = rawData.map(item => formatNumber(item.value || 0));
                } else {
                    // 普通数值数组
                    processedData = rawData.map(val => {
                        const num = formatNumber(val);
                        return num !== null && num !== undefined && !isNaN(num) ? num : 0;
                    });
                }

                const seriesConfig = {
                    name: dataKey,
                    type: 'bar',
                    data: processedData,
                    ...template,
                    label: {
                        show: showLabels,
                        position: 'right',
                        formatter: function(params) {
                            return formatNumber(params.value);
                        }
                    }
                };
                series.push(seriesConfig);
            });
        } else {
            // 如果没有找到合适的数据，尝试使用第一个数组
            const firstArrayKey = keys.find(key => Array.isArray(chartData[key]) && chartData[key].length > 0);
            if (firstArrayKey) {
                const rawData = chartData[firstArrayKey];
                let processedData = [];

                // 检查是否是饼图格式的数据
                if (rawData.length > 0 && typeof rawData[0] === 'object' && rawData[0].value !== undefined) {
                    // 饼图格式数据，提取数值
                    processedData = rawData.map(item => formatNumber(item.value || 0));
                } else {
                    // 普通数值数组
                    processedData = rawData.map(val => {
                        const num = formatNumber(val);
                        return num !== null && num !== undefined && !isNaN(num) ? num : 0;
                    });
                }

                const seriesConfig = {
                    name: firstArrayKey,
                    type: 'bar',
                    data: processedData,
                    ...template,
                    label: {
                        show: showLabels,
                        position: 'right',
                        formatter: function(params) {
                            return formatNumber(params.value);
                        }
                    }
                };
                series.push(seriesConfig);
            } else {
                // 创建示例数据
                const seriesConfig = {
                    name: '示例数据',
                    type: 'bar',
                    data: [10, 20, 30, 40, 50],
                    ...template,
                    label: {
                        show: showLabels,
                        position: 'right',
                        formatter: function(params) {
                            return formatNumber(params.value);
                        }
                    }
                };
                series.push(seriesConfig);
            }
        }
    }

    console.log('生成的横向柱状图系列:', series);
    return series;
}

// 创建柱状图/折线图/面积图系列
function createBarLineAreaSeries(chartType, chartData, template) {
    const series = [];

    console.log('创建柱状图/折线图/面积图，数据:', chartData);

    // 首先尝试从原始系列数据中提取（如果有的话）
    if (currentChartConfig && currentChartConfig.originalSeries && Array.isArray(currentChartConfig.originalSeries)) {
        console.log('使用原始系列数据:', currentChartConfig.originalSeries);
        currentChartConfig.originalSeries.forEach(originalSeries => {
            if (originalSeries.data && Array.isArray(originalSeries.data)) {
                series.push({
                    ...template,
                    name: originalSeries.name || '数据系列',
                    data: originalSeries.data.map(val => formatNumber(val))
                });
            }
        });

        if (series.length > 0) {
            console.log('使用原始系列数据生成的系列:', series);
            return series;
        }
    }

    // 检查是否是从饼图切换过来的数据（饼图数据格式为 [{name, value}]）
    if (currentChartConfig && currentChartConfig.series && Array.isArray(currentChartConfig.series)) {
        const currentSeries = currentChartConfig.series[0];
        if (currentSeries && currentSeries.type === 'pie' && currentSeries.data && Array.isArray(currentSeries.data)) {
            console.log('检测到饼图数据，进行转换:', currentSeries.data);

            // 从饼图数据中提取数值
            const pieData = currentSeries.data;
            if (pieData.length > 0 && typeof pieData[0] === 'object' && pieData[0].value !== undefined) {
                const values = pieData.map(item => formatNumber(item.value || 0));

                series.push({
                    ...template,
                    name: currentSeries.name || '数据系列',
                    data: values
                });

                console.log('从饼图数据转换的系列:', series);
                return series;
            }
        }
    }

    // 如果没有原始系列数据，则从chartData中提取
    if (chartData.categories && chartData.values) {
        // 单系列数据
        series.push({
            ...template,
            name: chartData.seriesName || '数据系列',
            data: Array.isArray(chartData.values) ? chartData.values.map(val => formatNumber(val)) : []
        });
    } else if (chartData.series && Array.isArray(chartData.series)) {
        // 多系列数据
        chartData.series.forEach(seriesData => {
            // 检查是否是饼图格式的数据
            if (seriesData.data && Array.isArray(seriesData.data) &&
                seriesData.data.length > 0 && typeof seriesData.data[0] === 'object' &&
                seriesData.data[0].value !== undefined) {
                // 饼图格式数据，提取数值
                const values = seriesData.data.map(item => formatNumber(item.value || 0));
                series.push({
                    ...template,
                    name: seriesData.name || '数据系列',
                    data: values
                });
            } else {
                // 普通数值数组
                series.push({
                    ...template,
                    name: seriesData.name || '数据系列',
                    data: seriesData.data ? seriesData.data.map(val => formatNumber(val)) : []
                });
            }
        });
    } else {
        // 尝试从其他数据结构中提取
        const keys = Object.keys(chartData);
        console.log('可用的数据键:', keys);

        // 排除可能的类别字段，寻找数值数据
        const excludeKeys = ['categories', 'labels', 'names', 'x', 'xAxis'];
        const possibleDataKeys = keys.filter(key =>
            Array.isArray(chartData[key]) &&
            chartData[key].length > 0 &&
            !excludeKeys.some(exclude => key.toLowerCase().includes(exclude.toLowerCase()))
        );

        console.log('可能的数据键:', possibleDataKeys);

        if (possibleDataKeys.length > 0) {
            possibleDataKeys.forEach(dataKey => {
                // 确保数据数组的完整性
                const rawData = chartData[dataKey];

                // 检查是否是饼图格式的数据
                if (rawData.length > 0 && typeof rawData[0] === 'object' && rawData[0].value !== undefined) {
                    // 饼图格式数据，提取数值
                    const processedData = rawData.map(item => formatNumber(item.value || 0));
                    series.push({
                        ...template,
                        name: dataKey,
                        data: processedData
                    });
                } else {
                    // 普通数值数组
                    const processedData = rawData.map(val => {
                        const num = formatNumber(val);
                        return num !== null && num !== undefined && !isNaN(num) ? num : 0;
                    });
                    series.push({
                        ...template,
                        name: dataKey,
                        data: processedData
                    });
                }
            });
        } else {
            // 如果没有找到合适的数据，尝试使用第一个数组
            const firstArrayKey = keys.find(key => Array.isArray(chartData[key]) && chartData[key].length > 0);
            if (firstArrayKey) {
                const rawData = chartData[firstArrayKey];

                // 检查是否是饼图格式的数据
                if (rawData.length > 0 && typeof rawData[0] === 'object' && rawData[0].value !== undefined) {
                    // 饼图格式数据，提取数值
                    const processedData = rawData.map(item => formatNumber(item.value || 0));
                    series.push({
                        ...template,
                        name: firstArrayKey,
                        data: processedData
                    });
                } else {
                    // 普通数值数组
                    const processedData = rawData.map(val => {
                        const num = formatNumber(val);
                        return num !== null && num !== undefined && !isNaN(num) ? num : 0;
                    });
                    series.push({
                        ...template,
                        name: firstArrayKey,
                        data: processedData
                    });
                }
            } else {
                // 创建示例数据
                series.push({
                    ...template,
                    name: '示例数据',
                    data: [10, 20, 30, 40, 50]
                });
            }
        }
    }

    console.log('生成的系列数据:', series);
    return series;
}

// 创建饼图系列
function createPieSeries(chartData, template) {
    const series = [];

    console.log('=== 开始创建饼图 ===');
    console.log('输入的chartData:', chartData);
    console.log('当前图表配置:', currentChartConfig);

    let pieData = [];
    let seriesName = '数据分布';

    // 优先从原始图表配置中提取数据
    if (currentChartConfig && currentChartConfig.originalXAxis && currentChartConfig.originalSeries) {
        console.log('从原始图表配置中提取饼图数据');

        // 获取X轴类别数据
        let categories = [];
        if (currentChartConfig.originalXAxis && Array.isArray(currentChartConfig.originalXAxis)) {
            // 如果是数组，取第一个
            const xAxis = currentChartConfig.originalXAxis[0];
            if (xAxis && xAxis.data && Array.isArray(xAxis.data)) {
                categories = xAxis.data;
            }
        } else if (currentChartConfig.originalXAxis && currentChartConfig.originalXAxis.data) {
            // 如果是对象
            categories = currentChartConfig.originalXAxis.data;
        }

        // 获取系列数据
        let seriesArray = Array.isArray(currentChartConfig.originalSeries) ? currentChartConfig.originalSeries : [currentChartConfig.originalSeries];

        if (categories.length > 0 && seriesArray.length > 0) {
            // 使用第一个系列的数据
            const firstSeries = seriesArray[0];
            if (firstSeries.data && Array.isArray(firstSeries.data)) {
                console.log('使用原始图表的类别和数据:', categories, firstSeries.data);

                pieData = categories.map((name, index) => ({
                    name: String(name),
                    value: formatNumber(firstSeries.data[index] || 0)
                })).filter(item => item.value > 0);

                seriesName = firstSeries.name || '数据分布';
            }
        }
    }

    // 如果从原始配置中没有获取到数据，尝试从chartData中提取
    if (pieData.length === 0) {
        console.log('从chartData中提取饼图数据');

        // 方法1：标准的categories + values组合
        if (chartData.categories && chartData.values &&
            Array.isArray(chartData.categories) && Array.isArray(chartData.values)) {

            console.log('使用方法1：categories + values');
            console.log('categories:', chartData.categories);
            console.log('values:', chartData.values);

            pieData = chartData.categories.map((name, index) => ({
                name: String(name),
                value: formatNumber(chartData.values[index] || 0)
            })).filter(item => item.value > 0);

            seriesName = chartData.seriesName || '数据分布';
        }
        // 方法2：从series数组中提取
        else if (chartData.series && Array.isArray(chartData.series) && chartData.series.length > 0) {
            console.log('使用方法2：从series数组提取');

            const firstSeries = chartData.series[0];
            if (firstSeries.data && Array.isArray(firstSeries.data)) {
                // 如果series数据已经是饼图格式 [{name, value}]
                if (firstSeries.data.length > 0 && typeof firstSeries.data[0] === 'object' && firstSeries.data[0].name !== undefined) {
                    pieData = firstSeries.data.map(item => ({
                        name: String(item.name),
                        value: formatNumber(item.value || 0)
                    })).filter(item => item.value > 0);
                } else {
                    // 如果是数值数组，生成默认名称
                    pieData = firstSeries.data.map((value, index) => ({
                        name: `项目${index + 1}`,
                        value: formatNumber(value || 0)
                    })).filter(item => item.value > 0);
                }
                seriesName = firstSeries.name || '数据分布';
            }
        }
        // 方法3：寻找包含名称和数值的键值对
        else {
            console.log('使用方法3：寻找名称和数值键');

            const keys = Object.keys(chartData);
            console.log('可用的数据键:', keys);

            // 寻找可能的名称字段（优先级排序）
            const nameKeys = [
                'categories', 'names', 'labels', 'xAxisData', 'x', 'category', 'name', 'label'
            ];
            const valueKeys = [
                'values', 'data', 'yData', 'y', 'value', 'count', 'amount'
            ];

            let nameKey = null;
            let valueKey = null;

            // 查找名称字段
            for (const key of nameKeys) {
                if (keys.includes(key) && Array.isArray(chartData[key]) && chartData[key].length > 0) {
                    nameKey = key;
                    break;
                }
            }

            // 查找数值字段
            for (const key of valueKeys) {
                if (keys.includes(key) && Array.isArray(chartData[key]) && chartData[key].length > 0) {
                    valueKey = key;
                    break;
                }
            }

            // 如果没找到标准名称，尝试模糊匹配
            if (!nameKey) {
                nameKey = keys.find(key =>
                    Array.isArray(chartData[key]) &&
                    chartData[key].length > 0 &&
                    (key.toLowerCase().includes('name') ||
                     key.toLowerCase().includes('category') ||
                     key.toLowerCase().includes('label') ||
                     key.toLowerCase().includes('x'))
                );
            }

            // 如果没找到标准数值，尝试模糊匹配
            if (!valueKey) {
                valueKey = keys.find(key =>
                    Array.isArray(chartData[key]) &&
                    chartData[key].length > 0 &&
                    key !== nameKey &&
                    (key.toLowerCase().includes('value') ||
                     key.toLowerCase().includes('data') ||
                     key.toLowerCase().includes('y') ||
                     key.toLowerCase().includes('count') ||
                     key.toLowerCase().includes('amount'))
                );
            }

            console.log('找到的名称键:', nameKey);
            console.log('找到的数值键:', valueKey);

            if (nameKey && valueKey) {
                console.log('名称数据:', chartData[nameKey]);
                console.log('数值数据:', chartData[valueKey]);

                pieData = chartData[nameKey].map((name, index) => ({
                    name: String(name),
                    value: formatNumber(chartData[valueKey][index] || 0)
                })).filter(item => item.value > 0);

                seriesName = valueKey;
            }
            // 方法4：使用第一个数组作为数值，生成默认名称
            else {
                console.log('使用方法4：第一个数组作为数值');

                const firstArrayKey = keys.find(key => Array.isArray(chartData[key]) && chartData[key].length > 0);

                if (firstArrayKey) {
                    console.log('使用数组键:', firstArrayKey);
                    console.log('数组数据:', chartData[firstArrayKey]);

                    pieData = chartData[firstArrayKey].map((value, index) => ({
                        name: `项目${index + 1}`,
                        value: formatNumber(value || 0)
                    })).filter(item => item.value > 0);

                    seriesName = firstArrayKey;
                }
            }
        }
    }

    // 创建饼图系列
    if (pieData.length > 0) {
        series.push({
            ...template,
            name: seriesName,
            data: pieData
        });
    }

    console.log('最终生成的饼图数据:', pieData);
    console.log('最终生成的饼图系列:', series);
    console.log('=== 饼图创建完成 ===');

    return series;
}
