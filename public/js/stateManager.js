// 状态管理模块 - 管理全局变量和应用状态

// 全局状态变量
let excelData = null;
let workbook = null;
let currentSheetName = null; // 当前选中的工作表名称
let myChart = null;
let chartCreated = false;
let fullscreenChart = null;
let isFullscreen = false;

// 图表切换功能相关变量
let currentChartData = null; // 保存当前图表的处理后数据
let currentChartConfig = null; // 保存当前图表的原始配置
let currentChartType = null; // 当前图表类型
let originalChartConfig = null; // 保存AI生成的原始图表配置
let showLabels = true; // 是否显示数值标签
let showAllXAxisLabels = true; // 是否显示全部X轴标签

// 历史记录相关变量
let chartHistory = []; // 存储历史图表记录
let maxHistorySize = 20; // 最大历史记录数量

// 重置上传状态，准备重新上传
function resetUpload() {
    // 清空聊天消息
    clearChatMessages();

    // 显示上传区域
    showUploadView();

    // 隐藏右侧控制按钮
    document.getElementById('reupload-file-btn').style.display = 'none';
    document.getElementById('toggle-view-btn').style.display = 'none';
    document.getElementById('download-btn').style.display = 'none';
    document.getElementById('fullscreen-btn').style.display = 'none';
    document.getElementById('chart-type-btn').style.display = 'none';
    document.getElementById('toggle-labels-btn').style.display = 'none';
    document.getElementById('xaxis-labels-control').style.display = 'none';

    // 隐藏左侧历史记录按钮
    document.getElementById('history-btn').style.display = 'none';

    // 重置文件输入框
    document.getElementById('excel-file').value = '';
    document.getElementById('file-name').textContent = '未选择文件';

    // 重置数据状态
    excelData = null;
    workbook = null;
    currentSheetName = null;

    // 重置图表切换相关状态
    currentChartData = null;
    currentChartConfig = null;
    currentChartType = null;
    originalChartConfig = null;
    showLabels = true; // 重置标签显示状态
    showAllXAxisLabels = true; // 重置X轴标签显示状态

    // 重置checkbox状态
    const checkbox = document.getElementById('show-all-xaxis-labels');
    if (checkbox) {
        checkbox.checked = true;
    }

    // 如果已经创建了图表，则重置图表
    if (chartCreated && myChart) {
        myChart.setOption({
            title: {
                text: '等待数据...',
                left: 'center',
                top: 'center',
                textStyle: {
                    color: '#999',
                    fontSize: 16,
                    fontWeight: 'normal'
                }
            },
            series: []
        });
        chartCreated = false;
        // document.getElementById('chart-no-data').style.display = 'flex';
    }

    // 添加聊天消息
    addChatMessage('bot', '请重新上传Excel文件以开始新的分析。');
}

// 切换数值标签显示/隐藏
function toggleLabels() {
    showLabels = !showLabels;

    // 更新按钮图标和提示
    const toggleBtn = document.getElementById('toggle-labels-btn');
    if (toggleBtn) {
        const icon = toggleBtn.querySelector('i');
        if (showLabels) {
            icon.className = 'bi bi-tag';
            toggleBtn.title = '隐藏数值';
        } else {
            icon.className = 'bi bi-eye-slash';
            toggleBtn.title = '显示数值';
        }
    }

    // 重新渲染当前图表
    if (myChart) {
        const currentOption = myChart.getOption();

        // 更新所有系列的标签显示状态
        if (currentOption.series) {
            if (Array.isArray(currentOption.series)) {
                currentOption.series.forEach(series => {
                    if (series.type !== 'pie') { // 饼图标签保持显示
                        if (series.label) {
                            series.label.show = showLabels;
                        }
                    }
                });
            } else if (currentOption.series.type !== 'pie') {
                if (currentOption.series.label) {
                    currentOption.series.label.show = showLabels;
                }
            }
        }

        // 重新设置图表配置
        myChart.setOption(currentOption, true);
    }

    // 添加聊天消息反馈
    const statusText = showLabels ? '显示' : '隐藏';
    addChatMessage('bot', `已${statusText}图表数值标签。`);
}

// 切换X轴标签显示/隐藏
function toggleXAxisLabels() {
    showAllXAxisLabels = !showAllXAxisLabels;

    // 更新checkbox状态
    const checkbox = document.getElementById('show-all-xaxis-labels');
    if (checkbox) {
        checkbox.checked = showAllXAxisLabels;
    }

    // 重新渲染当前图表
    if (myChart) {
        const currentOption = myChart.getOption();

        // 更新X轴标签显示策略
        if (currentOption.xAxis) {
            const xAxisArray = Array.isArray(currentOption.xAxis) ? currentOption.xAxis : [currentOption.xAxis];

            xAxisArray.forEach(xAxis => {
                if (xAxis.data && Array.isArray(xAxis.data)) {
                    const dataLength = xAxis.data.length;

                    if (showAllXAxisLabels) {
                        // 显示全部标签模式
                        xAxis.axisLabel = {
                            ...xAxis.axisLabel,
                            interval: 0 // 显示全部标签
                        };

                        // 根据数据量调整旋转角度
                        if (dataLength > 20) {
                            xAxis.axisLabel.rotate = 45;
                        } else if (dataLength > 12) {
                            xAxis.axisLabel.rotate = 30;
                        } else if (dataLength > 8) {
                            xAxis.axisLabel.rotate = 25;
                        } else {
                            xAxis.axisLabel.rotate = 0;
                        }
                    } else {
                        // 智能隐藏模式
                        if (dataLength > 20) {
                            xAxis.axisLabel = {
                                ...xAxis.axisLabel,
                                rotate: 45,
                                interval: Math.ceil(dataLength / 15)
                            };
                        } else if (dataLength > 12) {
                            xAxis.axisLabel = {
                                ...xAxis.axisLabel,
                                rotate: 30,
                                interval: Math.ceil(dataLength / 8)
                            };
                        } else if (dataLength > 8) {
                            xAxis.axisLabel = {
                                ...xAxis.axisLabel,
                                rotate: 25,
                                interval: 'auto'
                            };
                        } else {
                            xAxis.axisLabel = {
                                ...xAxis.axisLabel,
                                rotate: 0,
                                interval: 'auto'
                            };
                        }
                    }
                }
            });
        }

        // 重新设置图表配置
        myChart.setOption(currentOption, true);
    }

    // 添加聊天消息反馈
    const statusText = showAllXAxisLabels ? '显示全部' : '智能隐藏';
    addChatMessage('bot', `已切换到${statusText}X轴标签模式。`);
}

// 添加图表到历史记录
function addToChartHistory(question, chartConfig, chartData, chartType) {
    const historyItem = {
        id: Date.now() + Math.random(), // 唯一ID
        timestamp: new Date(),
        question: question,
        chartConfig: JSON.parse(JSON.stringify(chartConfig)), // 深拷贝配置
        chartData: JSON.parse(JSON.stringify(chartData)), // 深拷贝数据
        chartType: chartType,
        fileName: document.getElementById('file-name').textContent || '未知文件'
    };

    // 添加到历史记录开头
    chartHistory.unshift(historyItem);

    // 限制历史记录数量
    if (chartHistory.length > maxHistorySize) {
        chartHistory = chartHistory.slice(0, maxHistorySize);
    }

    console.log('已添加到历史记录:', historyItem);
    console.log('当前历史记录数量:', chartHistory.length);
}

// 从历史记录恢复图表
function restoreFromHistory(historyId) {
    const historyItem = chartHistory.find(item => item.id === historyId);
    if (!historyItem) {
        addChatMessage('bot', '未找到指定的历史记录。');
        return;
    }

    try {
        // 恢复图表配置
        currentChartConfig = JSON.parse(JSON.stringify(historyItem.chartConfig));
        currentChartData = JSON.parse(JSON.stringify(historyItem.chartData));
        currentChartType = historyItem.chartType;
        originalChartConfig = JSON.parse(JSON.stringify(historyItem.chartConfig));

        // 确保图表实例存在
        if (!myChart) {
            initChart();
        }

        // 渲染图表
        if (myChart && document.getElementById('chart-container')) {
            // 使用完整的图表配置进行渲染，确保清除之前的配置
            myChart.clear();
            myChart.setOption(historyItem.chartConfig, true);
            chartCreated = true;

            // 切换到图表视图
            showChartView();

            // 显示相关控制按钮
            document.getElementById('download-btn').style.display = 'flex';
            document.getElementById('chart-type-btn').style.display = 'flex';
            document.getElementById('toggle-labels-btn').style.display = 'flex';
            document.getElementById('xaxis-labels-control').style.display = 'flex';

            // 显示左侧历史记录按钮
            document.getElementById('history-btn').style.display = 'flex';

            // 更新图表类型按钮图标
            updateChartTypeButtonIcon(historyItem.chartType);

            // 重置标签显示状态
            showLabels = true;
            showAllXAxisLabels = true;
            const checkbox = document.getElementById('show-all-xaxis-labels');
            if (checkbox) {
                checkbox.checked = true;
            }

            // 确保图表正确调整大小
            setTimeout(() => {
                if (myChart && document.getElementById('chart-container')) {
                    myChart.resize();
                }
            }, 100);

            // 添加恢复成功消息
            const timeStr = historyItem.timestamp.toLocaleString();
            addChatMessage('bot', `已成功恢复历史图表（${timeStr}）：${historyItem.question}`);

            console.log('已恢复历史图表:', historyItem);
        } else {
            throw new Error('图表容器不存在或图表实例创建失败');
        }
    } catch (error) {
        console.error('恢复历史图表失败:', error);
        addChatMessage('bot', `恢复历史图表时遇到问题：${error.message}`);
    }
}

// 清空历史记录
function clearChartHistory() {
    chartHistory = [];
    addChatMessage('bot', '历史记录已清空。');
    console.log('历史记录已清空');
}

// 获取历史记录列表
function getChartHistory() {
    return chartHistory;
}
