// UI管理模块 - 管理用户界面的显示和交互

// 初始化ECharts图表
function initChart() {
    if (!myChart) {
        const chartDom = document.getElementById('chart-container');
        if (!chartDom) {
            console.error('图表容器元素不存在');
            return;
        }

        // 设置ECharts全局默认主题，确保轴标签颜色为黑色
        const customTheme = {
            color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
            backgroundColor: 'transparent',
            textStyle: {
                color: '#000'
            },
            title: {
                textStyle: {
                    color: '#333'
                }
            },
            legend: {
                textStyle: {
                    color: '#000'
                }
            },
            xAxis: {
                axisLabel: {
                    show: true,
                    color: '#000'
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    }
                }
            },
            yAxis: {
                axisLabel: {
                    show: true,
                    color: '#000'
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    }
                }
            }
        };

        // 注册自定义主题
        echarts.registerTheme('customDark', customTheme);

        // 使用自定义主题初始化图表
        myChart = echarts.init(chartDom, 'customDark');

        // 设置一个默认的空图表配置
        myChart.setOption({
            title: {
                text: '等待数据...',
                left: 'center',
                top: 'center',
                textStyle: {
                    color: '#999',
                    fontSize: 16,
                    fontWeight: 'normal'
                }
            }
        });
    }
}

// 显示上传视图
function showUploadView() {
    document.getElementById('upload-display').classList.add('active');
    document.getElementById('data-display').classList.remove('active');
    document.getElementById('chart-display').classList.remove('active');

    document.getElementById('display-icon').className = 'bi bi-file-earmark-excel';
    document.getElementById('display-text').textContent = '文件上传';
}

// 显示数据预览视图
function showDataView() {
    document.getElementById('upload-display').classList.remove('active');
    document.getElementById('data-display').classList.add('active');
    document.getElementById('chart-display').classList.remove('active');

    document.getElementById('display-icon').className = 'bi bi-table';
    document.getElementById('display-text').textContent = '数据预览';
    document.getElementById('toggle-view-btn').innerHTML = '<i class="bi bi-bar-chart-fill"></i>';
    document.getElementById('toggle-view-btn').title = '切换到图表视图';
}

// 显示图表视图
function showChartView() {
    document.getElementById('upload-display').classList.remove('active');
    document.getElementById('data-display').classList.remove('active');
    document.getElementById('chart-display').classList.add('active');

    document.getElementById('display-icon').className = 'bi bi-bar-chart-fill';
    document.getElementById('display-text').textContent = '图表展示';
    document.getElementById('toggle-view-btn').innerHTML = '<i class="bi bi-table"></i>';
    document.getElementById('toggle-view-btn').title = '切换到数据预览';

    // 调整图表大小
    if (myChart) {
        setTimeout(() => {
            if (myChart && document.getElementById('chart-container')) {
                myChart.resize();
            }
        }, 100);
    }
}

// 清空聊天消息
function clearChatMessages() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.innerHTML = '';
    }
}

// 添加聊天消息
function addChatMessage(type, content) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = type === 'bot' ? '<i class="bi bi-robot"></i>' : '<i class="bi bi-person-fill"></i>';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `<p>${content}</p>`;

    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 添加可点击的推荐消息
function addRecommendationMessage(recommendations) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message bot-message';

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = '<i class="bi bi-robot"></i>';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    let recommendationHTML = '<p>基于您的数据，我为您推荐以下3个可视化方案：</p>';
    recommendationHTML += '<div class="recommendation-list">';

    recommendations.forEach((recommendation, index) => {
        recommendationHTML += `
            <div class="recommendation-item" data-recommendation="${recommendation}">
                <i class="bi bi-bar-chart-fill"></i>
                <span>${recommendation}</span>
                <i class="bi bi-arrow-right-circle"></i>
            </div>
        `;
    });

    recommendationHTML += '</div>';
    recommendationHTML += '<p style="font-size: 12px; color: #666; margin-top: 10px;">💡 点击任意推荐项目即可自动生成对应图表</p>';

    contentDiv.innerHTML = recommendationHTML;

    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);

    // 为推荐项添加点击事件
    const recommendationItems = messageDiv.querySelectorAll('.recommendation-item');
    recommendationItems.forEach(item => {
        item.addEventListener('click', function() {
            const recommendation = this.getAttribute('data-recommendation');
            // 将推荐内容填入输入框并自动发送
            document.getElementById('question').value = recommendation;
            document.getElementById('generate-btn').click();
        });
    });

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 添加下一步操作建议消息
function addNextStepSuggestionsMessage(suggestions) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message bot-message';

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = '<i class="bi bi-robot"></i>';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    let suggestionsHTML = '<p>💡 基于当前图表，我为您推荐以下操作：</p>';
    suggestionsHTML += '<div class="suggestions-list">';

    suggestions.forEach((suggestion, index) => {
        const iconClass = suggestion.type === 'chart-type' ? 'bi-graph-up' : 'bi-gear';
        const typeLabel = suggestion.type === 'chart-type' ? '图表类型' : '配置选项';

        suggestionsHTML += `
            <div class="suggestion-item" data-suggestion="${suggestion.text}" data-type="${suggestion.type}">
                <div class="suggestion-icon">
                    <i class="bi ${iconClass}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-text">${suggestion.text}</div>
                    <div class="suggestion-type">${typeLabel}</div>
                </div>
                <div class="suggestion-arrow">
                    <i class="bi bi-chevron-right"></i>
                </div>
            </div>
        `;
    });

    suggestionsHTML += '</div>';
    suggestionsHTML += '<p style="font-size: 12px; color: #666; margin-top: 10px;">💡 点击任意建议即可执行对应操作</p>';

    contentDiv.innerHTML = suggestionsHTML;

    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);

    // 为建议项添加点击事件
    const suggestionItems = messageDiv.querySelectorAll('.suggestion-item');
    suggestionItems.forEach(item => {
        item.addEventListener('click', function() {
            const suggestionText = this.getAttribute('data-suggestion');
            const suggestionType = this.getAttribute('data-type');

            // 先在聊天记录中添加用户消息
            addChatMessage('user', suggestionText);

            // 设置标志，表示这是从建议点击触发的，避免重复添加用户消息
            window.isFromSuggestionClick = true;

            // 将建议内容填入输入框并自动发送
            document.getElementById('question').value = suggestionText;
            document.getElementById('generate-btn').click();
        });
    });

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 显示历史记录列表
function showHistoryModal() {
    const historyModal = document.getElementById('history-modal');
    const historyList = document.getElementById('history-list');
    const history = getChartHistory();

    // 清空历史列表
    historyList.innerHTML = '';

    if (history.length === 0) {
        // 显示无历史记录提示
        historyList.innerHTML = `
            <div class="no-history">
                <i class="bi bi-clock-history"></i>
                <p>暂无历史记录</p>
                <small>生成图表后，历史记录将显示在这里</small>
            </div>
        `;
    } else {
        // 显示历史记录列表
        history.forEach(item => {
            const historyItemDiv = document.createElement('div');
            historyItemDiv.className = 'history-item';
            historyItemDiv.setAttribute('data-history-id', item.id);

            const timeStr = item.timestamp.toLocaleString();
            const chartTypeIcon = getChartTypeIcon(item.chartType);

            historyItemDiv.innerHTML = `
                <div class="history-item-header">
                    <div class="history-item-question">${item.question}</div>
                    <div class="history-item-time">${timeStr}</div>
                </div>
                <div class="history-item-meta">
                    <div class="history-item-type">
                        <i class="bi ${chartTypeIcon}"></i>
                        <span>${getChartTypeName(item.chartType)}</span>
                    </div>
                    <div class="history-item-file">${item.fileName}</div>
                </div>
            `;

            // 添加点击事件
            historyItemDiv.addEventListener('click', function() {
                const historyId = parseFloat(this.getAttribute('data-history-id'));
                restoreFromHistory(historyId);
                historyModal.classList.remove('active');
            });

            historyList.appendChild(historyItemDiv);
        });
    }

    // 显示模态窗口
    historyModal.classList.add('active');
}

// 获取图表类型图标
function getChartTypeIcon(chartType) {
    const iconMap = {
        'bar': 'bi-bar-chart-fill',
        'line': 'bi-graph-up',
        'pie': 'bi-pie-chart-fill',
        'horizontal-bar': 'bi-bar-chart',
        'area': 'bi-graph-up-arrow',
        'original': 'bi-star-fill'
    };
    return iconMap[chartType] || 'bi-bar-chart-fill';
}

// 获取图表类型名称
function getChartTypeName(chartType) {
    const nameMap = {
        'bar': '柱状图',
        'line': '折线图',
        'pie': '饼图',
        'horizontal-bar': '横向柱状图',
        'area': '面积图',
        'original': '原图'
    };
    return nameMap[chartType] || '柱状图';
}

// 显示数据预览
function showDataPreview(htmlTable, arrayData, sheetName) {
    const previewContent = document.getElementById('preview-content');
    previewContent.innerHTML = '';

    // 如果没有HTML表格或数组数据，显示错误信息
    if (!htmlTable || !arrayData || arrayData.length === 0) {
        previewContent.innerHTML = '<div class="no-data"><i class="bi bi-file-earmark-excel"></i><p>数据为空</p></div>';
        return;
    }

    // 添加工作表名称标题和选择器
    if (sheetName && workbook) {
        const titleDiv = document.createElement('div');
        titleDiv.className = 'sheet-title';
        titleDiv.style.marginBottom = '15px';
        titleDiv.style.padding = '10px';
        titleDiv.style.backgroundColor = '#e3f2fd';
        titleDiv.style.borderRadius = '5px';
        titleDiv.style.borderLeft = '4px solid #2196f3';
        titleDiv.style.display = 'flex';
        titleDiv.style.alignItems = 'center';
        titleDiv.style.justifyContent = 'space-between';

        // 左侧标题
        const titleText = document.createElement('h4');
        titleText.innerHTML = `<i class="bi bi-file-earmark-excel"></i> 工作表:`;
        titleText.style.margin = '0';
        titleText.style.marginRight = '10px';

        // 右侧工作表选择器
        const sheetSelector = document.createElement('select');
        sheetSelector.id = 'sheet-selector';
        sheetSelector.style.padding = '5px 10px';
        sheetSelector.style.borderRadius = '3px';
        sheetSelector.style.border = '1px solid #ccc';
        sheetSelector.style.backgroundColor = 'white';
        sheetSelector.style.fontSize = '14px';
        sheetSelector.style.minWidth = '150px';

        // 添加所有工作表选项
        workbook.SheetNames.forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            if (name === sheetName) {
                option.selected = true;
            }
            sheetSelector.appendChild(option);
        });

        // 添加工作表切换事件
        sheetSelector.addEventListener('change', function() {
            const selectedSheet = this.value;
            if (selectedSheet !== currentSheetName) {
                currentSheetName = selectedSheet;
                processAndDisplaySheet(selectedSheet);
            }
        });

        titleDiv.appendChild(titleText);
        titleDiv.appendChild(sheetSelector);
        previewContent.appendChild(titleDiv);
    }

    // 检查是否有有效的表格内容
    if (htmlTable.includes('<table')) {
        // 创建表格容器
        const tableContainer = document.createElement('div');
        tableContainer.className = 'table-container';
        tableContainer.style.maxHeight = '540px';
        tableContainer.style.overflowY = 'auto';
        tableContainer.style.border = '1px solid #ddd';
        tableContainer.style.borderRadius = '5px';
        tableContainer.innerHTML = htmlTable;
        previewContent.appendChild(tableContainer);

        // 添加数据统计信息
        addDataStatistics(previewContent, arrayData);

        // 添加显示限制提示
        const limitMessage = document.createElement('p');
        limitMessage.textContent = '* 预览最多显示前30行有效数据，空行已自动过滤';
        limitMessage.style.textAlign = 'center';
        limitMessage.style.color = '#777';
        limitMessage.style.fontSize = '12px';
        limitMessage.style.marginTop = '10px';
        previewContent.appendChild(limitMessage);
    } else {
        // 显示无数据消息
        previewContent.innerHTML = htmlTable;
    }
}

// 添加数据统计信息
function addDataStatistics(container, data) {
    const statsDiv = document.createElement('div');
    statsDiv.className = 'data-statistics';
    statsDiv.style.marginTop = '15px';
    statsDiv.style.padding = '15px';
    statsDiv.style.backgroundColor = '#f8f9fa';
    statsDiv.style.borderRadius = '5px';
    statsDiv.style.fontSize = '14px';
    statsDiv.style.border = '1px solid #e9ecef';

    // 计算数据统计
    const totalRows = data.length > 0 ? data.length - 1 : 0; // 减去表头
    const totalCols = data.length > 0 ? data[0].length : 0;
    const totalCells = totalRows * totalCols;
    let emptyCells = 0;
    let validRows = 0;

    // 统计空值和有效行
    for (let i = 1; i < data.length; i++) {
        let hasValidData = false;
        for (let j = 0; j < data[i].length; j++) {
            const cell = data[i][j];
            if (cell === null || cell === undefined || cell === '' ||
                (typeof cell === 'string' && cell.trim() === '')) {
                emptyCells++;
            } else {
                hasValidData = true;
            }
        }
        if (hasValidData) {
            validRows++;
        }
    }

    const dataCompleteness = totalCells > 0 ? ((totalCells - emptyCells) / totalCells * 100).toFixed(1) : 0;
    const validRowsPercentage = totalRows > 0 ? ((validRows / totalRows) * 100).toFixed(1) : 0;

    // statsDiv.innerHTML = `
    //     <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
    //         <div><strong>📊 数据概览</strong></div>
    //         <div>总行数: <span style="color: #2196f3; font-weight: bold;">${totalRows}</span></div>
    //         <div>总列数: <span style="color: #2196f3; font-weight: bold;">${totalCols}</span></div>
    //         <div>有效行数: <span style="color: #4caf50; font-weight: bold;">${validRows}</span> (${validRowsPercentage}%)</div>
    //         <div>数据完整度: <span style="color: ${dataCompleteness >= 80 ? '#4caf50' : dataCompleteness >= 60 ? '#ff9800' : '#f44336'}; font-weight: bold;">${dataCompleteness}%</span></div>
    //         <div>空值数量: <span style="color: #f44336; font-weight: bold;">${emptyCells}</span></div>
    //     </div>
    // `;
    statsDiv.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <div><strong>📊 数据概览</strong></div>
            <div>总行数: <span style="color: #2196f3; font-weight: bold;">${totalRows}</span></div>
            <div>总列数: <span style="color: #2196f3; font-weight: bold;">${totalCols}</span></div>
            <div>有效行数: <span style="color: #4caf50; font-weight: bold;">${validRows}</span> (${validRowsPercentage}%)</div>
            <div>空值数量: <span style="color: #f44336; font-weight: bold;">${emptyCells}</span></div>
        </div>
    `;

    container.appendChild(statsDiv);
}
