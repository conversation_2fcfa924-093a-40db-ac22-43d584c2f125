// iOS 16 兼容性 Polyfills
// 这个文件包含了iOS 16 Safari可能缺失的JavaScript特性的polyfill

(function() {
  'use strict';

  // 检测是否为iOS 16或更早版本
  function isOldIOS() {
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    if (!isIOS) return false;
    
    // 提取iOS版本号
    const match = userAgent.match(/OS (\d+)_/);
    if (match) {
      const version = parseInt(match[1], 10);
      return version <= 16;
    }
    return false;
  }

  // 如果不是旧版iOS，直接返回
  if (!isOldIOS()) {
    return;
  }

  console.log('检测到iOS 16或更早版本，加载兼容性polyfills...');

  // Polyfill for Array.prototype.at()
  if (!Array.prototype.at) {
    Array.prototype.at = function(index) {
      const len = this.length;
      const relativeIndex = index < 0 ? len + index : index;
      return (relativeIndex >= 0 && relativeIndex < len) ? this[relativeIndex] : undefined;
    };
  }

  // Polyfill for String.prototype.at()
  if (!String.prototype.at) {
    String.prototype.at = function(index) {
      const len = this.length;
      const relativeIndex = index < 0 ? len + index : index;
      return (relativeIndex >= 0 && relativeIndex < len) ? this[relativeIndex] : undefined;
    };
  }

  // Polyfill for Object.hasOwn()
  if (!Object.hasOwn) {
    Object.hasOwn = function(obj, prop) {
      return Object.prototype.hasOwnProperty.call(obj, prop);
    };
  }

  // 确保Promise.allSettled存在
  if (!Promise.allSettled) {
    Promise.allSettled = function(promises) {
      return Promise.all(promises.map(promise =>
        Promise.resolve(promise).then(
          value => ({ status: 'fulfilled', value }),
          reason => ({ status: 'rejected', reason })
        )
      ));
    };
  }

  // 确保AbortController存在
  if (typeof AbortController === 'undefined') {
    window.AbortController = class AbortController {
      constructor() {
        this.signal = {
          aborted: false,
          addEventListener: function() {},
          removeEventListener: function() {},
          dispatchEvent: function() {}
        };
      }
      abort() {
        this.signal.aborted = true;
      }
    };
  }

  // 确保AbortSignal.timeout存在
  if (typeof AbortSignal !== 'undefined' && !AbortSignal.timeout) {
    AbortSignal.timeout = function(milliseconds) {
      const controller = new AbortController();
      setTimeout(() => controller.abort(), milliseconds);
      return controller.signal;
    };
  }

  // 确保globalThis存在
  if (typeof globalThis === 'undefined') {
    (function() {
      if (typeof self !== 'undefined') {
        self.globalThis = self;
      } else if (typeof window !== 'undefined') {
        window.globalThis = window;
      } else if (typeof global !== 'undefined') {
        global.globalThis = global;
      }
    })();
  }

  console.log('iOS 16兼容性polyfills加载完成');
})();
