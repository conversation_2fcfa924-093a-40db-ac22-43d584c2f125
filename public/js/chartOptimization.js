// 图表优化模块 - 优化图表布局、样式和显示效果

// 优化图表布局和样式
function optimizeChartLayout(chartTemplate) {
    // 1. 优化图表布局和间距
    if (!chartTemplate.grid) {
        chartTemplate.grid = {};
    }

    // 检查是否为横向柱状图
    let isHorizontalBar = false;
    if (chartTemplate.series) {
        const seriesArray = Array.isArray(chartTemplate.series) ? chartTemplate.series : [chartTemplate.series];
        isHorizontalBar = seriesArray.some(s =>
            s.type === 'bar' &&
            (s.label && s.label.position === 'right' || s.label && s.label.position === 'insideRight')
        );
    }

    // 设置合适的边距，为图例、标题和标签留出空间
    if (isHorizontalBar) {
        // 横向柱状图需要更多的左右边距
        chartTemplate.grid = {
            left: '5%', // 增加左侧边距给Y轴标签
            right: '10%', // 增加右侧边距给数值标签（显示在柱子右侧）
            top: chartTemplate.title ? '15%' : '10%',
            bottom: chartTemplate.legend ? '15%' : '10%',
            containLabel: false, // 不包含标签在内，让标签可以显示在图表区域外
            ...chartTemplate.grid
        };
    } else {
        // 普通图表的边距设置
        chartTemplate.grid = {
            left: '5%',
            right: '10%',
            top: chartTemplate.title ? '18%' : '12%', // 适度增加顶部空间给数值标签
            bottom: chartTemplate.legend ? '18%' : '12%', // 适度增加底部空间给X轴标签
            containLabel: true,
            ...chartTemplate.grid
        };
    }

    // 2. 优化标题显示
    if (chartTemplate.title) {
        // 保存原始标题配置
        const originalTitle = { ...chartTemplate.title };

        chartTemplate.title = {
            left: 'center',
            top: '10',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#333'
            },
            ...originalTitle,  // 应用原始配置
            // 确保关键样式不被覆盖
            left: originalTitle.left || 'center',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#333',
                ...(originalTitle.textStyle || {})
            }
        };
    }

    // 3. 优化图例显示
    if (chartTemplate.legend) {
        chartTemplate.legend = {
            type: 'scroll',
            orient: 'horizontal',
            left: 'center',
            top: 'bottom',
            bottom: '5%',
            itemGap: 20,
            textStyle: {
                fontSize: 12,
                color: '#000' // 优化颜色，改为纯黑色，确保最佳可读性
            },
            ...chartTemplate.legend
        };
    }

    // 4. 优化提示框
    if (!chartTemplate.tooltip) {
        chartTemplate.tooltip = {};
    }

    // 保存原有的formatter，如果存在的话
    const originalFormatter = chartTemplate.tooltip.formatter;

    // 创建优化的formatter函数
    const optimizedFormatter = function(params) {
        try {
            if (Array.isArray(params)) {
                // 多系列数据
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    // 确保获取正确的数值，避免显示公式
                    let value = param.value;

                    // 如果value是对象或函数，尝试获取实际数值
                    if (typeof value === 'object' && value !== null) {
                        if (value.value !== undefined) {
                            value = value.value;
                        } else if (Array.isArray(value) && value.length > 0) {
                            value = value[value.length - 1]; // 取最后一个值（通常是实际数值）
                        }
                    } else if (typeof value === 'function') {
                        // 如果是函数，尝试执行获取结果
                        try {
                            value = value();
                        } catch (e) {
                            value = 0;
                        }
                    }

                    const formattedValue = formatNumber(value);

                    // 检查是否为饼图，如果是则添加百分比
                    if (param.percent !== undefined) {
                        const percent = formatNumber(param.percent);
                        result += `${param.marker} ${param.seriesName}: ${formattedValue} (${percent}%)<br/>`;
                    } else {
                        result += `${param.marker} ${param.seriesName}: ${formattedValue}<br/>`;
                    }
                });
                return result;
            } else {
                // 单系列数据
                let value = params.value;

                // 确保获取正确的数值，避免显示公式
                if (typeof value === 'object' && value !== null) {
                    if (value.value !== undefined) {
                        value = value.value;
                    } else if (Array.isArray(value) && value.length > 0) {
                        value = value[value.length - 1]; // 取最后一个值
                    }
                } else if (typeof value === 'function') {
                    try {
                        value = value();
                    } catch (e) {
                        value = 0;
                    }
                }

                const formattedValue = formatNumber(value);

                // 检查是否为饼图，如果是则添加百分比
                if (params.percent !== undefined) {
                    const percent = formatNumber(params.percent);
                    return `${params.name}<br/>${params.marker} ${params.seriesName}: ${formattedValue} (${percent}%)`;
                } else {
                    return `${params.name}<br/>${params.marker} ${params.seriesName}: ${formattedValue}`;
                }
            }
        } catch (error) {
            console.error('Tooltip formatter error:', error);
            // 如果有原始formatter，尝试使用它
            if (originalFormatter && typeof originalFormatter === 'function') {
                try {
                    return originalFormatter(params);
                } catch (e) {
                    console.error('Original formatter also failed:', e);
                }
            }
            // 最后的备选方案：显示基本信息
            if (Array.isArray(params)) {
                return params[0].name + '<br/>' + params.map(p => `${p.seriesName}: ${p.value}`).join('<br/>');
            } else {
                return `${params.name}<br/>${params.seriesName}: ${params.value}`;
            }
        }
    };

    chartTemplate.tooltip = {
        trigger: 'axis',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
            color: '#333'
        },
        ...chartTemplate.tooltip,
        // 确保formatter不被覆盖
        formatter: optimizedFormatter
    };

    // 5. 优化坐标轴标签（传入系列信息以便智能优化）
    if (chartTemplate.xAxis) {
        if (Array.isArray(chartTemplate.xAxis)) {
            chartTemplate.xAxis.forEach(axis => optimizeXAxis(axis, chartTemplate.series));
        } else {
            optimizeXAxis(chartTemplate.xAxis, chartTemplate.series);
        }
    }

    if (chartTemplate.yAxis) {
        if (Array.isArray(chartTemplate.yAxis)) {
            chartTemplate.yAxis.forEach(axis => optimizeYAxis(axis));
        } else {
            optimizeYAxis(chartTemplate.yAxis);
        }
    }

    // 6. 优化数值标签显示
    if (chartTemplate.series) {
        if (Array.isArray(chartTemplate.series)) {
            chartTemplate.series.forEach(series => optimizeSeriesLabels(series));
        } else {
            optimizeSeriesLabels(chartTemplate.series);
        }
    }
}

// 优化X轴配置 - 支持智能标签显示和旋转
function optimizeXAxis(xAxis, series = null) {
    if (!xAxis.axisLabel) {
        xAxis.axisLabel = {};
    }

    // 根据数据点数量和图表类型智能调整X轴标签显示
    let labelConfig = {
        show: true, // 确保标签显示
        fontSize: 11,
        color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
        margin: 8,
        interval: 0, // 默认显示全部标签
        rotate: 0, // 默认不旋转
        ...xAxis.axisLabel,
        // 确保关键属性不被覆盖
        show: true,
        color: '#000'
    };

    // 如果有数据且数据点较多，调整标签显示策略
    if (xAxis.data && Array.isArray(xAxis.data)) {
        const dataLength = xAxis.data.length;

        // 检查是否为折线图或面积图
        let isLineOrArea = false;
        if (series) {
            const seriesArray = Array.isArray(series) ? series : [series];
            isLineOrArea = seriesArray.some(s =>
                s.type === 'line' ||
                (s.type === 'line' && s.areaStyle) ||
                s.areaStyle
            );
        }

        if (showAllXAxisLabels) {
            // 显示全部标签模式
            if (dataLength > 20) {
                labelConfig.rotate = 45;
                labelConfig.interval = 0; // 显示全部标签
            } else if (dataLength > 12) {
                labelConfig.rotate = isLineOrArea ? 35 : 30;
                labelConfig.interval = 0; // 显示全部标签
            } else if (dataLength > 8) {
                labelConfig.rotate = 25;
                labelConfig.interval = 0; // 显示全部标签
            }
        } else {
            // 智能隐藏模式
            if (dataLength > 20) {
                labelConfig.rotate = 45;
                labelConfig.interval = Math.ceil(dataLength / 15); // 显示约15个标签
            } else if (dataLength > 12) {
                labelConfig.rotate = isLineOrArea ? 35 : 30;
                labelConfig.interval = Math.ceil(dataLength / 8); // 显示约8个标签
            } else if (dataLength > 8) {
                labelConfig.rotate = 25;
                labelConfig.interval = 'auto'; // 自动间隔
            }
        }

        // 如果标签文字较长，也需要旋转
        const hasLongLabels = xAxis.data.some(label =>
            String(label).length > 8
        );
        if (hasLongLabels) {
            if (dataLength > 10) {
                labelConfig.rotate = Math.max(labelConfig.rotate, 45);
            } else if (dataLength > 5) {
                labelConfig.rotate = Math.max(labelConfig.rotate, 30);
            }
        }

        // 对于折线图和面积图，在数据点较多时稍微增加旋转角度以提高可读性
        if (isLineOrArea && dataLength > 15) {
            labelConfig.rotate = Math.max(labelConfig.rotate, 40);
        }
    }

    xAxis.axisLabel = labelConfig;

    if (!xAxis.axisLine) {
        xAxis.axisLine = {};
    }
    xAxis.axisLine = {
        lineStyle: {
            color: '#ccc' // 优化轴线颜色，从#ddd改为#ccc，稍微深一点
        },
        ...xAxis.axisLine
    };

    if (!xAxis.axisTick) {
        xAxis.axisTick = {};
    }
    xAxis.axisTick = {
        alignWithLabel: true,
        ...xAxis.axisTick
    };
}

// 优化Y轴配置
function optimizeYAxis(yAxis) {
    if (!yAxis.axisLabel) {
        yAxis.axisLabel = {};
    }

    // 检查是否为横向柱状图的Y轴（类型为category）
    const isHorizontalBarYAxis = yAxis.type === 'category';

    if (isHorizontalBarYAxis) {
        // 横向柱状图的Y轴标签配置
        yAxis.axisLabel = {
            show: true, // 确保标签显示
            fontSize: 11,
            color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
            margin: 8,
            // 确保标签不被截断
            overflow: 'truncate',
            width: 80, // 限制标签宽度
            ...yAxis.axisLabel,
            // 确保关键属性不被覆盖
            show: true,
            color: '#000'
        };
    } else {
        // 普通Y轴配置
        yAxis.axisLabel = {
            show: true, // 确保标签显示
            fontSize: 11,
            color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
            ...yAxis.axisLabel,
            // 确保关键属性不被覆盖
            show: true,
            color: '#000'
        };
    }

    if (!yAxis.axisLine) {
        yAxis.axisLine = {};
    }
    yAxis.axisLine = {
        lineStyle: {
            color: '#ccc' // 优化轴线颜色，从#ddd改为#ccc，稍微深一点
        },
        ...yAxis.axisLine
    };

    if (!yAxis.splitLine) {
        yAxis.splitLine = {};
    }

    if (isHorizontalBarYAxis) {
        // 横向柱状图不显示Y轴分割线
        yAxis.splitLine = {
            show: false,
            ...yAxis.splitLine
        };
    } else {
        // 普通图表显示Y轴分割线
        yAxis.splitLine = {
            lineStyle: {
                color: '#f0f0f0',
                type: 'dashed'
            },
            ...yAxis.splitLine
        };
    }
}

// 优化系列标签配置
function optimizeSeriesLabels(series) {
    if (!series.label) {
        series.label = {};
    }

    // 根据图表类型设置不同的标签配置
    if (series.type === 'pie' || series.type === 'doughnut') {
        series.label = {
            show: true,
            formatter: function(params) {
                const value = formatNumber(params.value);
                const percent = formatNumber(params.percent);
                return `${params.name}: ${value} (${percent}%)`;
            },
            fontSize: 10,
            color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
            ...series.label,
            // 确保颜色不被覆盖
            color: '#000'
        };
    } else {
        // 检查是否为横向柱状图（通过标签位置判断）
        const isHorizontalBar = series.label.position === 'right';

        if (isHorizontalBar) {
            // 横向柱状图的特殊标签配置
            series.label = {
                show: typeof showLabels !== 'undefined' ? showLabels : true,
                position: 'right', // 显示在柱子右侧
                fontSize: 10,
                color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
                fontWeight: 'normal',
                formatter: function(params) {
                    return formatNumber(params.value);
                },
                distance: 8, // 增加与柱子的距离
                // 确保标签不会被截断
                overflow: 'none',
                ...series.label,
                // 确保关键配置不被覆盖
                position: 'right',
                distance: 8,
                color: '#000' // 再次确保颜色不被覆盖
            };
        } else {
            // 对于普通柱状图、折线图等
            series.label = {
                show: typeof showLabels !== 'undefined' ? showLabels : true,
                position: 'top',
                fontSize: 10,
                color: '#000', // 优化颜色，改为纯黑色，确保最佳可读性
                formatter: function(params) {
                    return formatNumber(params.value);
                },
                // 添加标签间距和避免重叠的配置
                distance: 5, // 标签与图形的距离
                rotate: 0, // 标签旋转角度
                // 使用 ECharts 的智能标签避让功能
                emphasis: {
                    show: true
                },
                ...series.label,
                // 确保颜色不被覆盖
                color: '#000'
            };
        }
    }

    // 优化柱状图样式
    if (series.type === 'bar' && !series.itemStyle) {
        series.itemStyle = {
            borderRadius: [2, 2, 0, 0]
        };
    }

    // 优化折线图样式
    if (series.type === 'line') {
        if (!series.lineStyle) {
            series.lineStyle = {
                width: 2
            };
        }
        if (!series.symbol) {
            series.symbol = 'circle';
        }
        if (!series.symbolSize) {
            series.symbolSize = 6;
        }
    }
}
