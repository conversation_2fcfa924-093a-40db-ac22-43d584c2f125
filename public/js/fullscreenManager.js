// 全屏管理模块 - 处理全屏功能的显示和管理

// 全屏功能实现
function enterFullscreen() {
    const overlay = document.getElementById('fullscreen-overlay');
    const isDataView = document.getElementById('data-display').classList.contains('active');

    isFullscreen = true;
    overlay.classList.add('active');

    if (isDataView) {
        // 显示数据预览的全屏模式
        showFullscreenDataView();
    } else {
        // 显示图表的全屏模式
        showFullscreenChartView();
    }
}

function exitFullscreen() {
    const overlay = document.getElementById('fullscreen-overlay');
    isFullscreen = false;
    overlay.classList.remove('active');

    // 如果有全屏图表，销毁它
    if (fullscreenChart) {
        fullscreenChart.dispose();
        fullscreenChart = null;
    }

    // 重新调整原图表大小
    if (myChart) {
        setTimeout(() => {
            myChart.resize();
        }, 100);
    }
}

function showFullscreenDataView() {
    const content = document.getElementById('fullscreen-content');
    const previewContent = document.getElementById('preview-content');

    // 更新全屏标题
    document.getElementById('fullscreen-icon').className = 'bi bi-table';
    document.getElementById('fullscreen-text').textContent = '数据预览';
    document.getElementById('fullscreen-toggle-btn').innerHTML = '<i class="bi bi-bar-chart-fill"></i><span>切换到图表</span>';

    // 复制数据预览内容到全屏
    content.innerHTML = `<div class="fullscreen-table-container">${previewContent.innerHTML}</div>`;

    // 重新绑定全屏模式下的工作表选择器事件
    const fullscreenSheetSelector = content.querySelector('#sheet-selector');
    if (fullscreenSheetSelector) {
        fullscreenSheetSelector.addEventListener('change', function() {
            const selectedSheet = this.value;
            if (selectedSheet !== currentSheetName) {
                currentSheetName = selectedSheet;
                processAndDisplaySheet(selectedSheet);
                // 重新显示全屏数据视图以更新内容
                setTimeout(() => {
                    showFullscreenDataView();
                }, 100);
            }
        });
    }
}

function showFullscreenChartView() {
    const content = document.getElementById('fullscreen-content');

    // 更新全屏标题
    document.getElementById('fullscreen-icon').className = 'bi bi-bar-chart-fill';
    document.getElementById('fullscreen-text').textContent = '图表展示';
    document.getElementById('fullscreen-toggle-btn').innerHTML = '<i class="bi bi-table"></i><span>切换到数据</span>';

    // 创建全屏图表容器
    content.innerHTML = '<div class="fullscreen-chart-container" id="fullscreen-chart-container"></div>';

    // 初始化全屏图表
    const chartContainer = document.getElementById('fullscreen-chart-container');
    if (chartContainer && myChart) {
        // 使用与主图表相同的自定义主题
        fullscreenChart = echarts.init(chartContainer, 'customDark');
        // 复制原图表的配置
        const option = myChart.getOption();

        // 为全屏模式优化布局
        if (option && option.grid) {
            // 全屏模式下可以使用更大的绘图区域
            option.grid = {
                ...option.grid,
                left: '8%',
                right: '8%',
                top: option.title ? '12%' : '8%',
                bottom: option.legend ? '12%' : '8%'
            };
        }

        fullscreenChart.setOption(option);
    }
}
