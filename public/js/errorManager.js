// 错误处理模块 - 管理错误显示和加载状态

// 显示错误信息
function showError(message) {
    const errorElement = document.getElementById('error');
    errorElement.textContent = message;
    errorElement.style.display = 'block';
}

// 隐藏错误信息
function hideError() {
    document.getElementById('error').style.display = 'none';
}

// 显示弹出式Loading
function showLoadingModal(text = '正在分析数据并生成图表，请稍候...') {
    const modal = document.getElementById('loading-modal');
    const textElement = modal.querySelector('.loading-text');
    textElement.textContent = text;
    modal.classList.add('active');
}

// 隐藏弹出式Loading
function hideLoadingModal() {
    const modal = document.getElementById('loading-modal');
    modal.classList.remove('active');
}
