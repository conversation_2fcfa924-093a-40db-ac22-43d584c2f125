// 图表类型和模板模块 - 定义图表类型模板和相关函数

// 预定义图表类型模板
const chartTypeTemplates = {
    original: {
        name: '原图',
        icon: 'bi-star-fill',
        template: null // 原图不需要模板，直接使用保存的原始配置
    },
    bar: {
        name: '柱状图',
        icon: 'bi-bar-chart-fill',
        template: {
            type: 'bar',
            label: {
                show: true,
                position: 'top'
            }
        }
    },
    line: {
        name: '折线图',
        icon: 'bi-graph-up',
        template: {
            type: 'line',
            smooth: true,
            label: {
                show: true,
                position: 'top'
            },
            lineStyle: {
                width: 2
            },
            symbol: 'circle',
            symbolSize: 6
        }
    },
    pie: {
        name: '饼图',
        icon: 'bi-pie-chart-fill',
        template: {
            type: 'pie',
            radius: ['20%', '70%'],
            center: ['50%', '50%'],
            label: {
                show: true,
                formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
    },
    'horizontal-bar': {
        name: '横向柱状图',
        icon: 'bi-bar-chart',
        template: {
            type: 'bar',
            label: {
                show: true,
                position: 'right'
            }
        }
    },
    area: {
        name: '面积图',
        icon: 'bi-graph-up-arrow',
        template: {
            type: 'line',
            smooth: true,
            areaStyle: {
                opacity: 0.6
            },
            label: {
                show: false
            },
            lineStyle: {
                width: 2
            }
        }
    }
};

// 检测图表类型
function detectChartType(chartConfig) {
    if (!chartConfig || !chartConfig.series) {
        return 'bar'; // 默认返回柱状图
    }

    // 获取第一个系列的类型
    let firstSeries;
    if (Array.isArray(chartConfig.series)) {
        firstSeries = chartConfig.series[0];
    } else {
        firstSeries = chartConfig.series;
    }

    if (!firstSeries || !firstSeries.type) {
        return 'bar'; // 默认返回柱状图
    }

    const seriesType = firstSeries.type.toLowerCase();

    // 根据系列类型映射到我们的图表类型
    switch (seriesType) {
        case 'bar':
            return 'bar';
        case 'line':
            // 检查是否有面积样式
            if (firstSeries.areaStyle) {
                return 'area';
            }
            return 'line';
        case 'pie':
            return 'pie';
        case 'scatter':
            return 'horizontal-bar';
        default:
            return 'bar'; // 默认返回柱状图
    }
}

// 更新图表类型按钮图标
function updateChartTypeButtonIcon(chartType) {
    const chartTypeBtn = document.getElementById('chart-type-btn');
    if (!chartTypeBtn) return;

    const template = chartTypeTemplates[chartType];
    if (template && template.icon) {
        const iconElement = chartTypeBtn.querySelector('i');
        if (iconElement) {
            // 清除所有可能的图标类
            iconElement.className = '';
            // 添加新的图标类
            iconElement.className = `bi ${template.icon}`;
        }
    }
}
