// 工具函数模块 - 提供通用的工具函数

// 数值格式化函数
function formatNumber(value) {
    console.log(value)
    if (value === null || value === undefined || isNaN(value)) {
        return value;
    }

    const num = Number(value);

    // 如果是整数，直接返回
    if (Number.isInteger(num)) {
        return num.toString();
    }

    // 获取小数部分
    const decimalPart = num.toString().split('.')[1];
    if (!decimalPart) {
        return num.toString();
    }

    const decimalLength = decimalPart.length;

    // 根据小数位数决定保留的位数
    if (decimalLength > 4) {
        // 大于4位小数，保留4位
        return num.toFixed(4);
    } else if (decimalLength > 2) {
        // 大于2位小于等于4位小数，保留2位
        return num.toFixed(2);
    } else {
        // 小于等于2位小数，保持原样
        return num.toString();
    }
}

// 检测Excel表格中Header占多少行的函数
function detectHeaderRows(data) {
    if (!data || data.length < 2) {
        return 1; // 默认至少有1行表头
    }

    let headerRows = 1; // 至少有1行表头

    // 检查前几行的数据特征
    for (let rowIndex = 1; rowIndex < Math.min(data.length, 5); rowIndex++) {
        const currentRow = data[rowIndex];
        const previousRow = data[rowIndex - 1];

        // 统计当前行的数据类型
        let numericCount = 0;
        let textCount = 0;
        let emptyCount = 0;
        let totalCells = currentRow.length;

        for (let colIndex = 0; colIndex < currentRow.length; colIndex++) {
            const cellValue = currentRow[colIndex];

            if (cellValue === null || cellValue === undefined || cellValue === '' ||
                (typeof cellValue === 'string' && cellValue.trim() === '')) {
                emptyCount++;
            } else if (!isNaN(Number(cellValue)) && cellValue !== '') {
                // 是数值
                numericCount++;
            } else {
                // 是文本
                textCount++;
            }
        }

        // 计算比例
        const numericRatio = numericCount / totalCells;
        const textRatio = textCount / totalCells;
        const emptyRatio = emptyCount / totalCells;

        // 判断是否为数据行的条件：
        // 1. 数值比例较高（>30%）且文本比例不太高（<70%）
        // 2. 或者空值比例不太高（<50%）且有一定数值
        const isDataRow = (numericRatio > 0.3 && textRatio < 0.7) ||
                         (emptyRatio < 0.5 && numericRatio > 0.2);

        // 额外检查：如果当前行与上一行的数据类型分布差异很大，可能是表头结束
        let typeDistributionChange = false;
        if (rowIndex > 1) {
            const prevRowStats = analyzeRowDataTypes(previousRow);
            const currRowStats = analyzeRowDataTypes(currentRow);

            // 如果数值比例变化超过40%，认为是表头结束
            typeDistributionChange = Math.abs(currRowStats.numericRatio - prevRowStats.numericRatio) > 0.4;
        }

        // 如果当前行看起来像数据行，或者数据类型分布发生显著变化，则表头结束
        if (isDataRow || typeDistributionChange) {
            break;
        }

        headerRows++;
    }

    // 最多不超过数据总行数的一半，且不超过5行
    return Math.min(headerRows, Math.floor(data.length / 2), 5);
}

// 辅助函数：分析行的数据类型分布
function analyzeRowDataTypes(row) {
    let numericCount = 0;
    let textCount = 0;
    let emptyCount = 0;
    let totalCells = row.length;

    for (let i = 0; i < row.length; i++) {
        const cellValue = row[i];

        if (cellValue === null || cellValue === undefined || cellValue === '' ||
            (typeof cellValue === 'string' && cellValue.trim() === '')) {
            emptyCount++;
        } else if (!isNaN(Number(cellValue)) && cellValue !== '') {
            numericCount++;
        } else {
            textCount++;
        }
    }

    return {
        numericCount,
        textCount,
        emptyCount,
        numericRatio: numericCount / totalCells,
        textRatio: textCount / totalCells,
        emptyRatio: emptyCount / totalCells
    };
}
