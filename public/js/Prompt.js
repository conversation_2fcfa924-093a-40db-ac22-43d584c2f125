/**
 * 数据可视化 Prompt 模板
 * 包含用于生成 ECharts 图表的提示词模板
 */

/**
 * 第一版 Prompt - 详细的数据可视化专家提示词
 * @param {string} dataString - 格式化的数据字符串
 * @param {string} question - 用户问题
 * @returns {string} 完整的 prompt 字符串
 */
function getPrompt(dataString, question) {
    return `
你是一位数据可视化专家，请基于以下Excel表格示例数据（仅展示前几行）和用户问题，生成ECharts图表模板和数据处理逻辑。

# Excel数据样本 (Tab分隔，仅前几行):
\`\`\`
${dataString}
\`\`\`

# 用户问题:
${question}

# 任务:
1. 分析Excel数据的结构和内容，包括数据类型、分布特征和数据间关系
2. 理解用户的问题，确定最合适的图表类型或多图表组合
3. 设计数据处理逻辑，指定如何从原始数据中提取和处理图表所需的数据
4. 创建一个符合用户需求的ECharts配置对象模板
5. 确保配置严格遵循ECharts官方文档规范

# 返回格式要求:
返回一个JSON对象，包含两部分：
1. "dataProcessor": 包含从原始数据中提取、转换、聚合等操作的处理方法
2. "chartTemplate": 图表配置对象模板，其中的series.data等数据字段使用占位符
3. "dataProcessor"和"chartTemplate"中都不能包含任何注释
4. "dataProcessor"中只能使用"data"变量和前面步骤定义的变量名，不能使用{{placeholder}}格式的占位符
5. "chartTemplate"中必须使用"{{placeholder}}"格式的占位符引用dataProcessor中定义的数据
6. 必须严格遵循JSON格式规范，不要使用JavaScript对象字面量中的简写形式
7. 所有键名必须使用双引号包裹，不允许使用单引号
8. 不要在JSON中包含任何注释或多余的逗号
9. 确保所有ECharts配置项名称准确无误，遵循官方文档规范
10. 避免使用未定义或自定义的配置项
11. 不要在JSON外添加任何Markdown格式的文本说明或代码块标记

示例：
{
  "dataProcessor": {
    "xAxisData": "data.map(row => row[0])",
    "seriesData": "data.map(row => row[1])",
    "totalValue": "seriesData.reduce((sum, val) => sum + Number(val), 0)",
    "averageValue": "totalValue / seriesData.length"
  },
  "chartTemplate": {
    "title": { "text": "图表标题" },
    "xAxis": { "data": "{{xAxisData}}" },
    "yAxis": {},
    "series": [{
      "type": "bar",
      "data": "{{seriesData}}",
      "label": { "show": true, "position": "top" },
      "markLine": {
        "data": [{ "type": "average", "name": "平均值" }]
      }
    }]
  }
}

# 数据处理增强:
- dataProcessor中的每个字段定义一个数据提取和处理逻辑
- 第一步处理只能使用data变量，后续步骤可以直接引用之前定义的变量名（而非使用{{}}占位符）
- 使用Number()、parseInt()或parseFloat()进行明确的数值类型转换
- 处理可能的null值、undefined和NaN，替换为适当的默认值
- 对极端值/异常值进行检测和处理(如设置阈值、使用中位数替代等)
- 对日期时间数据使用Date对象进行标准化处理
- 考虑数据归一化或标准化，使不同量级的数据可比
- 使用适当的排序、分组、聚合方法处理多维数据

# 复杂数据处理示例:

示例 - 正确的数据处理方式:
\`\`\`json
"dataProcessor": {
  "departments": "Array.from(new Set(data.map(item => item[2])))",
  "departmentScores": "departments.map(department => { const deptData = data.filter(item => item[2] === department); return { name: department, score: deptData.reduce((sum, item) => sum + Number(item[3]), 0) / deptData.length }; })",
  "sortedDepts": "departmentScores.sort((a, b) => b.score - a.score)",
  "deptNames": "sortedDepts.map(item => item.name)",
  "deptScores": "sortedDepts.map(item => item.score.toFixed(2))"
}
\`\`\`

示例 - 错误的数据处理方式:
\`\`\`json
"dataProcessor": {
  "departments": "Array.from(new Set(data.map(item => item[2])))",
  "departmentScores": "{{departments}}.map(department => { ... })",
  "deptNames": "{{departmentScores}}.map(item => item.name)"
}
\`\`\`

# 图表类型智能选择:
- 基于数据特征(离散/连续、时序/分类等)推荐合适的图表类型
- 对不同分布类型选择合适的图表:
  * 分类数据比较: 柱状图、饼图
  * 时间序列: 折线图、面积图
  * 多维数据: 散点图、热力图、雷达图
  * 层次关系: 树图、桑基图
  * 地理数据: 地图
- 当有多个变量时，考虑组合图表(如折线+柱状图)或使用多个坐标系

# 视觉美化指导:
- 使用视觉上和谐的主题配色方案(如monochromatic、analogous、complementary)
- 确保足够的颜色对比度，提高可访问性
- 优化图表的宽高比，避免视觉失真
- 适当使用强调色突出关键数据点
- 确保所有文本标签清晰可读，避免重叠

# 交互性增强:
- 添加合适的tooltip格式化，显示完整的数据上下文
- 考虑添加dataZoom组件实现数据范围筛选
- 为大型数据集添加图例筛选功能
- 必要时添加点击事件回调占位符
- 考虑使用视觉映射组件(visualMap)处理数据范围着色

# 响应性与适配性:
- 使用grid组件灵活控制图表布局
- 考虑在不同屏幕尺寸下的组件布局调整
- 设置适当的文字大小和图例位置，适应不同设备
- 对于复杂图表，考虑移动设备的简化版本

# 图表模板要求:
- 使用"{{placeholder}}"格式的占位符引用dataProcessor中定义的数据
- 确保图表设置合适的颜色和样式，具有良好的可读性
- 所有图表必须默认显示数值标签
- 对于柱状图、折线图等，需要在series中添加label: {show: true, position: 'top'}
- 对于饼图，需要在series中添加label: {show: true, formatter: '{b}: {c} ({d}%)'}
- 添加适当的标题、图例和提示信息，使图表更易理解
- 考虑加入markPoint或markLine标记重要数据点(如最大值、最小值、平均值)

# 数据标签优化:
- 避免在图表中使用过于突出的数值标签，如气泡或大号字体
- 对于折线图的数据点，使用小型标签或仅在关键点显示标签
- 设置适当的label.fontSize，通常不超过12px
- 使用label.formatter简化数值显示，如千分位数据使用'k'后缀
- 当数据点过多时，使用label.interval或label.formatter控制标签显示频率
- 可以使用label.backgroundColor设置为'rgba(255,255,255,0.7)'增加可读性而不突兀
- 始终确保tooltip提供完整信息，而标签仅提供简明提示
- 对于双Y轴或多系列图表，避免所有系列同时显示标签，造成视觉混乱
`;
}

/**
 * 第二版 Prompt - 优化的数据处理和 ECharts 可视化专家提示词
 * @param {string} dataString - 格式化的数据字符串
 * @param {string} question - 用户问题
 * @param {number} headerRows - 表头占用的行数，默认为1
 * @returns {string} 完整的 prompt2 字符串
 */
function getPrompt2(dataString, question, headerRows = 1) {
    return `
你是一名"数据处理 + ECharts 可视化"专家，精通 JavaScript 数据管道和官方 ECharts API。
请基于 **Excel 数据样本** 和 **用户问题** 返回一个 **纯 JSON 对象**，包含：

**重要说明：数据表头占前${headerRows}行，第${headerRows + 1}行开始是实际数据。**

1. \`"dataProcessor"\`：若干以 **箭头函数字符串** 表述的步骤，每一步只能使用 \`data\` 变量或已定义变量，**严禁** \`{{placeholder}}\`。
2. \`"chartTemplate"\`：合法的 ECharts 配置模板，凡引用 \`dataProcessor\` 中变量，必须用 **\`"{{变量名}}"\`** 占位符。

> **返回内容必须满足：**
> - 仅输出单一 JSON（无 Markdown 代码块、无其他文字）
> - 完全符合下方 **JSON Schema**；若不符合，立即自我修正后再输出
> - 所有键用双引号；无尾逗号；无任何注释

---

### 输入

- **Excel 数据样本（包含表头、前5行和后5行数据，Tab 分隔）**
${dataString}

> **数据说明**：以上数据包含表头行、前5行数据和后5行数据。如果数据总量较大，中间部分数据未显示。请基于这些样本数据推断完整数据的结构和特征。

- **用户问题**
${question}

---

### 生成步骤

1. **数据洞察与清理**
 - 基于提供的表头、前5行和后5行样本数据，推断完整数据集的字段含义、数据类型（数值/日期/分类）和整体特征
 - **强制空值检测与过滤**：识别并处理 null、undefined、空字符串、纯空格字符串
 - **数据完整性验证**：过滤掉完全为空的行，确保每行至少有一个有效值
 - 若含日期/时间字段，统一转 \`new Date(...)\`；对数值使用 \`utils.toNumber()\` 安全转换
 - 异常值检测：识别超出合理范围的数值，视情况过滤或替换
 - **数据规模推断**：基于样本数据推断完整数据集的规模和分布特征

2. **图表策略**
 - 依据数据特征 → 选择最契合的图表或组合
 - 必要时使用双坐标系、堆叠或多系列并确保配色清晰

3. **构建 \`dataProcessor\`**
 - **重要：数据表头占前${headerRows}行，实际数据从第${headerRows + 1}行开始，处理时需要跳过表头行**
 - 第一步只能引用 \`data\`，注意 \`data\` 已经是去除表头的纯数据行
 - 后续步骤直接引用已定义变量
 - 用 \`reduce / map / filter / sort\` 等原生数组方法；保证结果可直接被模板消费
 - **强制空值过滤**：使用 \`filter(item => item !== null && item !== undefined && item !== '')\` 过滤空值
 - **安全数值转换**：使用 \`utils.toNumber(val)\` 替代 \`Number(val)\` 进行安全转换
 - **数值格式化要求**：所有数值必须使用 \`utils.formatNumber(num)\` 进行格式化，避免过长小数位
   * 该函数会自动根据小数位数应用格式化规则
   * 大于4位小数时保留4位，大于2位小于等于4位时保留2位，小于等于2位保持原样
 - **字符串安全处理**：使用 \`utils.toString(val)\` 进行安全字符串转换
 - 处理空值：\`?? 默认值\`；处理 NaN：\`isNaN(x) ? 0 : x\`
 - 如需统计量（平均、最大值 …），提前计算并保存变量
 示例如下：
 {
  "dataProcessor": {
    "filteredData": "data.filter(r => r[0] !== null && r[0] !== undefined && r[0] !== '')",
    "xAxisData": "filteredData.map(r => utils.toString(r[0]))",
    "yData": "filteredData.map(r => utils.formatNumber(utils.toNumber(r[1])))",
    "avgY": "yData.length > 0 ? utils.formatNumber(yData.reduce((s,v)=>s+v,0) / yData.length) : 0"
  },
  "chartTemplate": {
    "title": { "text": "示例图表" },
    "tooltip": { "trigger": "axis" },
    "xAxis": { "data": "{{xAxisData}}" },
    "yAxis": {},
    "series": [{
      "type": "bar",
      "data": "{{yData}}",
      "label": { "show": true, "position": "top" },
      "markLine": { "data": [{ "type": "average", "name": "平均值" }] }
    }]
  }
}

4. **构建 \`chartTemplate\`**
 - chartTemplate中必须包含 \`"title": { "text": "图表标题必须包含" }\`
 - 严格遵循 ECharts 官方文档字段；无自定义字段
 - **必须包含优化的布局配置**：
   * \`grid\`: 设置图表绘图网格，为标题、图例、坐标轴标签留出充足空间
   * \`title\`: 标题居中显示，避免与其他元素重叠
   * \`legend\`: 图例位置和样式优化，设置合适的间距
   * \`tooltip\`: 提示框配置优化
 - **坐标轴优化**：
   * X轴标签旋转角度设置（当标签过长时）
   * 标签间距和字体大小调整
   * 轴线和刻度线样式优化
 - **数值标签优化**：
   * 对柱/折/散点等系列添加 \`label: { "show": true, "position": "top", "fontSize": 10, "color": "#666" }\`
   * 对饼图 \`label.formatter\` 使用 \`'{b}: {c} ({d}%)'\`
   * 避免标签重叠的智能显示策略
 - 在 \`series[i].data\`、\`xAxis.data\` 等处使用 \`"{{变量名}}"\` 占位符
 - **当用户要求X轴Y轴标签**：
   * \`xAxis\`: {
      name: {{xAxisName}},
      nameTextStyle: {
        color: '#000', 
        fontSize: 18,
      },
      nameGap: 80,
      nameLocation: "middle",
      ...
    }
   * \`yAxis\`: {
      name: {{yAxisName}},
      nameTextStyle: {
        color: '#000', 
        fontSize: 18,
      },
      nameGap: 80,
      nameRotate: 90,
      nameLocation: "middle",
      ...
    }

### 图表布局优化要求

**必须包含的优化配置模板：**

\`\`\`json
{
  "dataProcessor": {
    "filteredData": "data.filter(r => r[0] !== null && r[0] !== undefined && r[0] !== '')",
    "xAxisData": "filteredData.map(r => utils.toString(r[0]))",
    "yData": "filteredData.map(r => utils.formatNumber(utils.toNumber(r[1])))",
    "avgY": "yData.length > 0 ? utils.formatNumber(yData.reduce((s,v)=>s+v,0) / yData.length) : 0"
  },
  "chartTemplate": {
    "title": {
      "text": "图表标题必须包含",
      "left": "center",
      "top": "10",
      "textStyle": { "fontSize": 16, "fontWeight": "bold", "color": "#333" }
    },
    "grid": {
      "left": "5%",
      "right": "5%",
      "top": "10%",
      "bottom": "10%",
      "containLabel": true
    },
    "legend": {
      "type": "scroll",
      "orient": "horizontal",
      "left": "center",
      "top": "bottom",
      "bottom": "5%",
      "itemGap": 20,
      "textStyle": { "fontSize": 12, "color": "#000" }
    },
    "tooltip": {
      "trigger": "axis",
      "backgroundColor": "rgba(255,255,255,0.9)",
      "borderColor": "#ccc",
      "borderWidth": 1,
      "textStyle": { "color": "#333" }
    },
    "xAxis": {
      "type": "category",
      "data": "{{xAxisData}}",
      "axisLabel": {
        "show": true,
        "rotate": 0,
        "interval": "auto",
        "fontSize": 11,
        "color": "#000",
        "margin": 8
      },
      "axisLine": { "lineStyle": { "color": "#ccc" } },
      "axisTick": { "alignWithLabel": true }
    },
    "yAxis": {
      "type": "value",
      "axisLabel": { "show": true, "fontSize": 11, "color": "#000" },
      "axisLine": { "lineStyle": { "color": "#ccc" } },
      "splitLine": { "lineStyle": { "color": "#f0f0f0", "type": "dashed" } }
    },
    "series": [{
      "type": "bar",
      "data": "{{yData}}",
      "label": {
        "show": true,
        "position": "top",
        "fontSize": 10,
        "color": "#000",
        "formatter": "{c}"
      },
      "itemStyle": { "borderRadius": [2, 2, 0, 0] },
      "markLine": { "data": [{ "type": "average", "name": "平均值" }] }
    }]
  }
}
\`\`\`

---

### 空值处理最佳实践

**必须遵循的空值处理模式：**

1. **数据过滤阶段**：
   \`\`\`javascript
   "validData": "data.filter(row => row[0] !== null && row[0] !== undefined && row[0] !== '')"
   \`\`\`

2. **数值转换阶段**：
   \`\`\`javascript
   "numericData": "validData.map(row => utils.formatNumber(utils.toNumber(row[1])))"
   \`\`\`

3. **字符串处理阶段**：
   \`\`\`javascript
   "labels": "validData.map(row => utils.toString(row[0]))"
   \`\`\`

4. **聚合计算阶段**：
   \`\`\`javascript
   "average": "numericData.length > 0 ? utils.formatNumber(numericData.reduce((s,v)=>s+v,0) / numericData.length) : 0"
   \`\`\`

### JSON Schema（校验约束）

\`\`\`json
{
"type": "object",
"required": ["dataProcessor", "chartTemplate"],
"properties": {
  "dataProcessor": {
    "type": "object",
    "minProperties": 1,
    "additionalProperties": { "type": "string" }
  },
  "chartTemplate": { "type": "object" }
},
"additionalProperties": false
}
\`\`\`
`;
}

/**
 * 获取可视化图表推荐的 Prompt 提示词
 * @param {string} dataString - 格式化的数据字符串（包含表头字段和前几行数据）
 * @returns {string} 图表推荐的 prompt 字符串
 */
function getVisualizationRecommendationPrompt(dataString) {
    return `你是一位专业数据分析师。用户已上传Excel表格，请根据**表头字段和前几行数据**执行：
1. **识别关键字段**：
   - 标记数值型字段（工资、销售额等）
   - 标记分类型字段（部门、职位等）
   - 标记时间型字段（日期、月份等）

2. **生成3个最相关的可视化方案**：
   每个方案输出为字符串，格式为：\`"[分析标题][图表类型]"\`
   - 分析标题：20字内的直白描述（如"部门平均工资排名"）
   - 图表类型：从以下选择一种（柱状图/折线图/饼图/横向柱状图/面积图）

3. **优先级规则**：
   - 必含1个数值字段聚合分析（平均/最大/分布）
   - 存在分类字段时必含分组对比
   - 存在时间字段时必含趋势分析
   - 优先选择业务价值最高的分析（如薪资分析 > 年龄分析）

4. **输出格式要求**：
   仅返回JSON数组，包含3个字符串元素：
   [
     "各部门平均工资对比柱状图",
     "工资与年龄关系饼图",
     "月度入职趋势折线图"
   ]

表头字段和前几行数据如下：
${dataString}`;
}

/**
 * 构建下一步操作建议的 Prompt 提示词
 * @param {string} rawDataInput - 原始数据输入
 * @param {Object} currentConfig - 当前图表配置
 * @param {string} chartType - 当前图表类型
 * @returns {string} 下一步建议的 prompt 字符串
 */
function getSuggestionsPrompt(rawDataInput, currentConfig, chartType) {
    return `基于当前图表配置，请提供3-5个有用的操作建议：

当前图表类型：${chartType}
用户输入的原始数据：
\`\`\`
${rawDataInput}
\`\`\`
当前配置：${JSON.stringify(currentConfig, null, 2)}

请返回JSON格式的建议数组参考：
[
    {
        "text": "切换为折线图",
        "type": "chart-type"
    },
    {
        "text": "修改标题为XXX",
        "type": "config-option"
    },
    {
        "text": "调整颜色",
        "type": "config-option"
    },
    {
        "text": "显示数据标签",
        "type": "config-option"
    },
    {
        "text": "添加副标题为XXX",
        "type": "config-option"
    }
]

注意：
1. type只能是"chart-type"或"config-option"
2. 建议要实用且相关，建议尽量是具体的
3. 只返回JSON数组，不要其他文字`;
}
