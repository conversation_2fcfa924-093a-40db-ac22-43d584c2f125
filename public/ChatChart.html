<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据可视化助手</title>
    <script src="js/echarts.min.js"></script>
    <script src="js/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/ChatChart.css">
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Excel数据可视化助手</h1>
            <p>上传Excel文件，提出问题，让AI为您生成数据图表</p>
            <!-- <button class="settings-btn" id="settings-btn">
                <i class="bi bi-gear-fill"></i>
            </button> -->
        </div>

        <div class="main-content">
            <div class="content-wrapper">
                <!-- 左侧面板 - 对话聊天框 -->
                <div class="left-panel" data-tour="viz-chat-panel">
                    <div class="panel-section chat-section">
                        <div class="section-header">
                            <h3><i class="bi bi-chat-dots"></i> AI对话助手</h3>
                            <div class="chat-header-controls">
                                <button class="chat-control-btn" id="history-btn" title="历史记录" style="display: none;">
                                    <i class="bi bi-clock-history"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 聊天消息区域 -->
                        <div class="chat-messages" id="chat-messages">
                            <div class="welcome-message">
                                <div class="message bot-message">
                                    <div class="message-avatar">
                                        <i class="bi bi-robot"></i>
                                    </div>
                                    <div class="message-content">
                                        <p>您好！我是您的数据可视化助手。</p>
                                        <p>请先上传Excel文件，然后告诉我您想要创建什么样的图表，我会为您生成相应的可视化图表。</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 输入区域 -->
                        <div class="chat-input-area">
                            <div class="input-group">
                                <input type="text" id="question" placeholder="请输入您的问题，例如：'请基于数据创建一个销售趋势柱状图'">
                                <button id="generate-btn">发送</button>
                            </div>


                            <div class="error" id="error"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧面板 -->
                <div class="right-panel">
                    <div class="panel-section display-section">
                        <div class="section-header">
                            <h3 id="display-title">
                                <i class="bi bi-table" id="display-icon"></i>
                                <span id="display-text">数据预览</span>
                            </h3>
                            <div class="header-controls" data-tour="viz-chart-controls">
                                <div class="control-checkbox" id="xaxis-labels-control" title="X轴标签显示控制"
                                    style="display: none;">
                                    <input type="checkbox" id="show-all-xaxis-labels" checked>
                                    <label for="show-all-xaxis-labels">全部X轴标签</label>
                                </div>
                                <button class="control-btn" id="chart-type-btn" title="切换图表类型" style="display: none;">
                                    <i class="bi bi-graph-up"></i>
                                </button>
                                <button class="control-btn" id="toggle-labels-btn" title="切换数值显示"
                                    style="display: none;">
                                    <i class="bi bi-tag"></i>
                                </button>
                                <button class="control-btn" id="toggle-view-btn" title="切换视图" style="display: none;">
                                    <i class="bi bi-bar-chart-fill"></i>
                                </button>
                                <button class="control-btn" id="fullscreen-btn" title="全屏显示" style="display: none;">
                                    <i class="bi bi-arrows-fullscreen"></i>
                                </button>
                                <button class="control-btn" id="download-btn" title="下载图表" style="display: none;">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="control-btn" id="reupload-file-btn" title="重新上传文件"
                                    style="display: none;">
                                    <i class="bi bi-arrow-repeat"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div id="upload-display" class="display-content active">
                            <div id="upload-area" class="file-upload-center" data-tour="viz-upload-area">
                                <i class="bi bi-file-earmark-excel file-upload-icon"></i>
                                <p class="upload-text">上传Excel文件以开始分析</p>

                                <!-- 模型选择下拉框 -->
                                <div class="model-selection" data-tour="viz-model-selection">
                                    <label for="model-type" class="model-label">选择处理模型：</label>
                                    <select id="model-type" class="model-select">
                                        <option value="local">本地模型（隐私数据）</option>
                                        <option value="external">外部模型（非隐私数据）</option>
                                    </select>
                                </div>

                                <label for="excel-file" class="file-upload-label">选择文件</label>
                                <input type="file" id="excel-file" accept=".xlsx, .xls">
                                <span class="file-name" id="file-name" hidden>未选择文件</span>

                                <!-- 隐私提示（默认隐藏） -->
                                <div class="privacy-notice" id="privacy-notice" style="display: none;">
                                    <i class="bi bi-shield-exclamation"></i>
                                    <span>切勿上传隐私或敏感数据</span>
                                </div>
                            </div>
                        </div>

                        <!-- 数据预览区域 -->
                        <div id="data-display" class="display-content" data-tour="viz-data-preview">
                            <div class="preview-content" id="preview-content">
                                <div class="no-data-preview">
                                    <i class="bi bi-table"></i>
                                    <p>上传文件后数据将显示在这里</p>
                                </div>
                            </div>
                        </div>

                        <!-- 图表展示区域 -->
                        <div id="chart-display" class="display-content" data-tour="viz-chart-display">
                            <div id="chart-container">
                                <!-- <div class="no-data" id="chart-no-data">
                                    <i class="bi bi-bar-chart-fill"></i>
                                    <p>生成的图表将显示在这里</p>
                                    <small>上传数据并提出问题，AI将为您生成可视化图表</small>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="footer">
                Excel数据可视化助手 | 支持多种LLM模型
            </div> -->
        </div>
    </div>

    <!-- API密钥配置模态窗口 -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>LLM模型配置</h3>
                <button class="close-modal" id="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="api-url">API地址</label>
                    <input type="text" id="api-url" placeholder="请输入API地址，例如：https://api.openai.com/v1/chat/completions"
                        style="width: 100%; margin-top: 5px;">
                </div>

                <div class="form-group">
                    <label for="api-key">API密钥</label>
                    <input type="text" id="api-key" placeholder="请输入您的API密钥" style="width: 100%; margin-top: 5px;">
                </div>

                <div class="form-group">
                    <label for="api-model">模型名称</label>
                    <input type="text" id="api-model" placeholder="请输入模型名称，例如：gpt-4o-mini"
                        style="width: 100%; margin-top: 5px;">
                </div>

                <div class="hint" style="margin-top: 15px; font-size: 13px; color: #777;">
                    所有配置信息仅在本地使用，不会发送到任何第三方服务器。支持OpenAI、Claude、Gemini等兼容的API接口。
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancel-settings">取消</button>
                <button class="btn-primary" id="save-settings">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 全屏覆盖层 -->
    <div class="fullscreen-overlay" id="fullscreen-overlay">
        <div class="fullscreen-header">
            <h3 class="fullscreen-title" id="fullscreen-title">
                <i class="bi bi-table" id="fullscreen-icon"></i>
                <span id="fullscreen-text">数据预览</span>
            </h3>
            <div class="fullscreen-controls">
                <button class="fullscreen-control-btn" id="fullscreen-download-btn" title="下载图表">
                    <i class="bi bi-download"></i>
                    <span>下载图表</span>
                </button>
                <button class="fullscreen-control-btn" id="fullscreen-toggle-btn" title="切换视图">
                    <i class="bi bi-bar-chart-fill"></i>
                    <span>切换到图表</span>
                </button>
                <button class="fullscreen-control-btn" id="exit-fullscreen-btn" title="退出全屏">
                    <i class="bi bi-arrows-angle-contract"></i>
                    <span>退出全屏</span>
                </button>
            </div>
        </div>
        <div class="fullscreen-content" id="fullscreen-content">
            <!-- 全屏内容将在这里动态插入 -->
        </div>
    </div>

    <!-- 弹出式Loading模态框 -->
    <div class="loading-modal" id="loading-modal">
        <div class="loading-modal-content">
            <div class="loading-spinner">
                <!-- 项目Logo图标 -->
                <div class="loading-robot-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <rect x="3" y="11" width="18" height="10" rx="2"></rect>
                        <circle cx="12" cy="5" r="2"></circle>
                        <path d="M12 7v4"></path>
                        <line x1="8" y1="16" x2="8" y2="16"></line>
                        <line x1="16" y1="16" x2="16" y2="16"></line>
                    </svg>
                </div>
                <!-- 现代化Loading动画 -->
                <div class="modern-spinner"></div>
            </div>
            <div class="loading-text">正在分析数据并生成图表，请稍候...</div>
        </div>
    </div>

    <!-- 图表类型切换模态窗口 -->
    <div class="modal" id="chart-type-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>切换图表类型</h3>
                <button class="close-modal" id="close-chart-type-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="chart-type-grid">
                    <div class="chart-type-item" data-type="original">
                        <i class="bi bi-star-fill"></i>
                        <span>原图</span>
                    </div>
                    <div class="chart-type-item" data-type="bar">
                        <i class="bi bi-bar-chart-fill"></i>
                        <span>柱状图</span>
                    </div>
                    <div class="chart-type-item" data-type="line">
                        <i class="bi bi-graph-up"></i>
                        <span>折线图</span>
                    </div>
                    <div class="chart-type-item" data-type="pie">
                        <i class="bi bi-pie-chart-fill"></i>
                        <span>饼图</span>
                    </div>
                    <div class="chart-type-item" data-type="horizontal-bar">
                        <i class="bi bi-bar-chart"></i>
                        <span>横向柱状图</span>
                    </div>
                    <div class="chart-type-item" data-type="area">
                        <i class="bi bi-graph-up-arrow"></i>
                        <span>面积图</span>
                    </div>
                </div>
                <div class="hint" style="margin-top: 15px; font-size: 13px; color: #777;">
                    选择图表类型后，系统将使用当前数据自动生成对应的图表，无需重新调用AI接口。
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态窗口 -->
    <div class="modal" id="history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史生成记录</h3>
                <button class="close-modal" id="close-history-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="history-list" id="history-list">
                    <div class="no-history">
                        <i class="bi bi-clock-history"></i>
                        <p>暂无历史记录</p>
                        <small>生成图表后，历史记录将显示在这里</small>
                    </div>
                </div>
                <div class="history-actions">
                    <button class="btn-secondary" id="clear-history-btn">清空历史记录</button>
                </div>
                <div class="hint" style="margin-top: 15px; font-size: 13px; color: #777;">
                    点击历史记录可以快速恢复之前生成的图表配置，避免重复分析。最多保存20条记录。
                </div>
            </div>
        </div>
    </div>

    <!-- 先加载 Prompt 函数 -->
    <script src="js/Prompt.js"></script>

    <!-- 然后按正确顺序加载拆分后的模块 -->
    <!-- 1. 全局状态变量 -->
    <script src="js/stateManager.js"></script>

    <!-- 2. 工具函数 -->
    <script src="js/utils.js"></script>

    <!-- 3. 错误处理 -->
    <script src="js/errorManager.js"></script>

    <!-- 4. 图表类型定义 -->
    <script src="js/chartTypes.js"></script>

    <!-- 5. 图表优化 -->
    <script src="js/chartOptimization.js"></script>

    <!-- 6. 图表创建 -->
    <script src="js/chartCreation.js"></script>

    <!-- 7. UI管理 -->
    <script src="js/uiManager.js"></script>

    <!-- 8. Excel处理 -->
    <script src="js/excelProcessor.js"></script>

    <!-- 9. API管理 -->
    <script src="js/apiManager.js"></script>

    <!-- 10. 全屏功能 -->
    <script src="js/fullscreenManager.js"></script>

    <!-- 11. 下载功能 -->
    <script src="js/downloadManager.js"></script>

    <!-- 12. 事件处理（最后加载） -->
    <script src="js/eventHandlers.js"></script>

    <!-- 13. 引导模拟操作处理 -->
    <script>
        // 监听来自父窗口的消息，用于引导模拟操作
        window.addEventListener('message', function (event) {
            if (event.data && event.data.type) {
                switch (event.data.type) {
                    case 'simulateFileUpload':
                        // 模拟文件上传
                        setTimeout(() => {
                            const uploadDisplay = document.getElementById('upload-display');
                            const dataDisplay = document.getElementById('data-display');
                            const previewContent = document.getElementById('preview-content');

                            if (uploadDisplay && dataDisplay && previewContent) {
                                uploadDisplay.classList.remove('active');
                                dataDisplay.classList.add('active');

                                // 更新标题
                                const displayText = document.getElementById('display-text');
                                const displayIcon = document.getElementById('display-icon');
                                if (displayText) displayText.textContent = '数据预览';
                                if (displayIcon) displayIcon.className = 'bi bi-table';

                                // 使用传递过来的mock数据或默认数据
                                const mockData = event.data.data.mockData || [
                                    { '月份': '1月', '销售额': 120000, '订单数': 450, '客户数': 280 },
                                    { '月份': '2月', '销售额': 135000, '订单数': 520, '客户数': 310 },
                                    { '月份': '3月', '销售额': 148000, '订单数': 580, '客户数': 340 },
                                    { '月份': '4月', '销售额': 162000, '订单数': 620, '客户数': 380 },
                                    { '月份': '5月', '销售额': 175000, '订单数': 680, '客户数': 420 },
                                    { '月份': '6月', '销售额': 188000, '订单数': 720, '客户数': 450 }
                                ];

                                // 动态生成表格
                                if (mockData.length > 0) {
                                    const headers = Object.keys(mockData[0]);
                                    const headerRow = headers.map(header => `<th>${header}</th>`).join('');
                                    const dataRows = mockData.map(row =>
                                        `<tr>${headers.map(header =>
                                            `<td>${typeof row[header] === 'number' ? row[header].toLocaleString() : row[header]}</td>`
                                        ).join('')}</tr>`
                                    ).join('');

                                    previewContent.innerHTML = `
                                        <div class="table-container">
                                            <div class="file-info">
                                                <i class="bi bi-file-earmark-excel"></i>
                                                <span>已上传文件: ${event.data.data.fileName || 'demo-data.xlsx'}</span>
                                                <span class="data-count">(${mockData.length} 行数据)</span>
                                            </div>
                                            <table class="data-table">
                                                <thead>
                                                    <tr>${headerRow}</tr>
                                                </thead>
                                                <tbody>
                                                    ${dataRows}
                                                </tbody>
                                            </table>
                                        </div>
                                    `;
                                }
                            }
                        }, 500);
                        break;

                    case 'simulateChartGeneration':
                        // 模拟图表生成
                        setTimeout(() => {
                            const question = event.data.data.question;
                            const questionInput = document.getElementById('question');
                            const generateBtn = document.getElementById('generate-btn');

                            if (questionInput && generateBtn) {
                                questionInput.value = question;
                                // 不实际点击，避免真实API调用
                            }

                            // 切换到图表显示状态并生成mock图表
                            setTimeout(() => {
                                const dataDisplay = document.getElementById('data-display');
                                const chartDisplay = document.getElementById('chart-display');
                                const chartContainer = document.getElementById('chart-container');
                                const chartControls = document.querySelector('[data-tour="viz-chart-controls"]');

                                if (dataDisplay && chartDisplay && chartContainer) {
                                    dataDisplay.classList.remove('active');
                                    chartDisplay.classList.add('active');

                                    // 更新标题
                                    const displayText = document.getElementById('display-text');
                                    const displayIcon = document.getElementById('display-icon');
                                    if (displayText) displayText.textContent = '图表展示';
                                    if (displayIcon) displayIcon.className = 'bi bi-bar-chart-fill';

                                    // 确保容器有足够的尺寸
                                    chartContainer.style.width = '100%';
                                    chartContainer.style.height = '400px';

                                    // 创建mock图表
                                    if (typeof echarts !== 'undefined') {
                                        const chart = echarts.init(chartContainer);
                                        const option = {
                                            title: {
                                                text: '销售数据柱状图',
                                                left: 'center',
                                                top: '10px',
                                                textStyle: {
                                                    fontSize: 16,
                                                    fontWeight: 'bold'
                                                }
                                            },
                                            tooltip: {
                                                trigger: 'axis',
                                                formatter: function (params) {
                                                    return params[0].name + '<br/>' +
                                                        params[0].seriesName + ': ' +
                                                        params[0].value.toLocaleString();
                                                }
                                            },
                                            grid: {
                                                left: '3%',
                                                right: '4%',
                                                top: '50px',
                                                bottom: '3%',
                                                containLabel: true
                                            },
                                            xAxis: {
                                                type: 'category',
                                                data: ['1月', '2月', '3月', '4月', '5月', '6月'],
                                                axisLabel: {
                                                    interval: 0,
                                                    rotate: 0
                                                }
                                            },
                                            yAxis: {
                                                type: 'value',
                                                axisLabel: {
                                                    formatter: function (value) {
                                                        return (value / 1000) + 'K';
                                                    }
                                                }
                                            },
                                            series: [{
                                                name: '销售额',
                                                type: 'bar',
                                                data: [120000, 135000, 148000, 162000, 175000, 188000],
                                                itemStyle: {
                                                    color: '#5470c6'
                                                },
                                                label: {
                                                    show: true,
                                                    position: 'top',
                                                    formatter: function (params) {
                                                        return (params.value / 1000) + 'K';
                                                    }
                                                },
                                                barWidth: '60%'
                                            }]
                                        };
                                        chart.setOption(option);

                                        // 确保图表正确渲染
                                        setTimeout(() => {
                                            chart.resize();
                                        }, 100);
                                    } else {
                                        console.error('ECharts is not loaded');
                                        // 如果ECharts没有加载，显示一个简单的占位符
                                        chartContainer.innerHTML = `
                                            <div style="display: flex; align-items: center; justify-content: center; height: 400px; background: #f5f5f5; border: 2px dashed #ccc;">
                                                <div style="text-align: center;">
                                                    <div style="font-size: 48px; color: #5470c6; margin-bottom: 10px;">📊</div>
                                                    <div style="font-size: 16px; color: #666;">销售数据柱状图</div>
                                                    <div style="font-size: 14px; color: #999; margin-top: 5px;">图表加载中...</div>
                                                </div>
                                            </div>
                                        `;
                                    }

                                    // 显示图表控制按钮
                                    if (chartControls) {
                                        const controlBtns = chartControls.querySelectorAll('.control-btn');
                                        controlBtns.forEach(btn => {
                                            btn.style.display = 'inline-block';
                                        });
                                    }

                                    // 触发第六步：完成引导
                                    setTimeout(() => {
                                        window.postMessage({
                                            type: 'simulateStep6'
                                        }, '*');
                                    }, 1000);
                                }
                            }, 1000);
                        }, 500);
                        break;

                    case 'simulateBarChart':
                        // 模拟生成柱状图
                        setTimeout(() => {
                            // 切换到图表显示状态并生成柱状图
                            const dataDisplay = document.getElementById('data-display');
                            const chartDisplay = document.getElementById('chart-display');
                            const chartContainer = document.getElementById('chart-container');
                            const chartControls = document.querySelector('[data-tour="viz-chart-controls"]');

                            if (dataDisplay && chartDisplay && chartContainer) {
                                dataDisplay.classList.remove('active');
                                chartDisplay.classList.add('active');

                                // 更新标题
                                const displayText = document.getElementById('display-text');
                                const displayIcon = document.getElementById('display-icon');
                                if (displayText) displayText.textContent = '图表展示';
                                if (displayIcon) displayIcon.className = 'bi bi-bar-chart-fill';

                                // 确保容器有足够的尺寸
                                chartContainer.style.width = '100%';
                                chartContainer.style.height = '400px';

                                // 创建图表
                                if (typeof echarts !== 'undefined') {
                                    const chart = echarts.init(chartContainer);
                                    const chartData = event.data.data || {};

                                    console.log('Chart data received:', chartData); // 调试信息

                                    const option = {
                                        title: {
                                            text: chartData.title || '销售数据柱状图',
                                            left: 'center',
                                            top: '10px',
                                            textStyle: {
                                                fontSize: 16,
                                                fontWeight: 'bold'
                                            }
                                        },
                                        tooltip: {
                                            trigger: 'axis',
                                            formatter: function (params) {
                                                return params[0].name + '<br/>' +
                                                    params[0].seriesName + ': ' +
                                                    params[0].value.toLocaleString();
                                            }
                                        },
                                        grid: {
                                            left: '3%',
                                            right: '4%',
                                            top: '50px',
                                            bottom: '3%',
                                            containLabel: true
                                        },
                                        xAxis: {
                                            type: 'category',
                                            data: chartData.data ? chartData.data.map(item => item.name) : ['1月', '2月', '3月', '4月', '5月', '6月'],
                                            axisLabel: {
                                                interval: 0,
                                                rotate: 0
                                            }
                                        },
                                        yAxis: {
                                            type: 'value',
                                            axisLabel: {
                                                formatter: function (value) {
                                                    return (value / 1000) + 'K';
                                                }
                                            }
                                        },
                                        series: [{
                                            name: '销售额',
                                            type: 'bar',
                                            data: chartData.data ? chartData.data.map(item => item.value) : [120000, 135000, 148000, 162000, 175000, 188000],
                                            itemStyle: {
                                                color: '#5470c6'
                                            },
                                            label: {
                                                show: true,
                                                position: 'top',
                                                formatter: function (params) {
                                                    return (params.value / 1000) + 'K';
                                                }
                                            },
                                            barWidth: '60%'
                                        }]
                                    };

                                    chart.setOption(option);

                                    // 确保图表正确渲染
                                    setTimeout(() => {
                                        chart.resize();
                                    }, 100);
                                } else {
                                    console.error('ECharts is not loaded');
                                    // 如果ECharts没有加载，显示一个简单的占位符
                                    chartContainer.innerHTML = `
                                        <div style="display: flex; align-items: center; justify-content: center; height: 400px; background: #f5f5f5; border: 2px dashed #ccc;">
                                            <div style="text-align: center;">
                                                <div style="font-size: 48px; color: #5470c6; margin-bottom: 10px;">📊</div>
                                                <div style="font-size: 16px; color: #666;">销售数据柱状图</div>
                                                <div style="font-size: 14px; color: #999; margin-top: 5px;">图表加载中...</div>
                                            </div>
                                        </div>
                                    `;
                                }

                                // 显示图表控制按钮
                                if (chartControls) {
                                    const controlBtns = chartControls.querySelectorAll('.control-btn');
                                    controlBtns.forEach(btn => {
                                        btn.style.display = 'inline-block';
                                    });
                                }

                                // 触发第六步：完成引导
                                setTimeout(() => {
                                    window.postMessage({
                                        type: 'simulateStep6'
                                    }, '*');
                                }, 1000);
                            }
                        }, 500);
                        break;

                    case 'resetToUpload':
                        // 恢复到初始上传页面
                        setTimeout(() => {
                            const uploadDisplay = document.getElementById('upload-display');
                            const dataDisplay = document.getElementById('data-display');
                            const chartDisplay = document.getElementById('chart-display');
                            const chartControls = document.querySelector('[data-tour="viz-chart-controls"]');

                            if (uploadDisplay && dataDisplay && chartDisplay) {
                                // 隐藏数据预览和图表显示
                                dataDisplay.classList.remove('active');
                                chartDisplay.classList.remove('active');

                                // 显示上传页面
                                uploadDisplay.classList.add('active');

                                // 更新标题
                                const displayText = document.getElementById('display-text');
                                const displayIcon = document.getElementById('display-icon');
                                if (displayText) displayText.textContent = '数据预览';
                                if (displayIcon) displayIcon.className = 'bi bi-table';

                                // 隐藏图表控制按钮
                                if (chartControls) {
                                    const controlBtns = chartControls.querySelectorAll('.control-btn');
                                    controlBtns.forEach(btn => {
                                        btn.style.display = 'none';
                                    });
                                }

                                // 清空图表容器
                                const chartContainer = document.getElementById('chart-container');
                                if (chartContainer) {
                                    chartContainer.innerHTML = '';
                                }

                                // 重置文件输入
                                const fileInput = document.getElementById('excel-file');
                                const fileName = document.getElementById('file-name');
                                if (fileInput) fileInput.value = '';
                                if (fileName) {
                                    fileName.textContent = '未选择文件';
                                    fileName.hidden = true;
                                }

                                // 清空问题输入框
                                const questionInput = document.getElementById('question');
                                if (questionInput) questionInput.value = '';

                                console.log('Reset to upload page completed');
                            }
                        }, 1000);
                        break;

                    case 'simulateStep6':
                        // 第六步：显示完成状态，3秒后自动恢复
                        setTimeout(() => {
                            console.log('Step 6: Guide completed, will reset in 3 seconds...');

                            // 3秒后自动恢复到初始状态
                            setTimeout(() => {
                                window.postMessage({
                                    type: 'resetToUpload'
                                }, '*');
                            }, 3000);
                        }, 500);
                        break;

                    case 'completeGuide':
                        // 第六步：点击完成按钮后恢复到初始状态
                        setTimeout(() => {
                            window.postMessage({
                                type: 'resetToUpload'
                            }, '*');
                        }, 500);
                        break;
                }
            }
        });

        // 添加一个测试函数，用于直接测试图表生成
        window.testChartGeneration = function () {
            const dataDisplay = document.getElementById('data-display');
            const chartDisplay = document.getElementById('chart-display');
            const chartContainer = document.getElementById('chart-container');
            const chartControls = document.querySelector('[data-tour="viz-chart-controls"]');

            if (dataDisplay && chartDisplay && chartContainer) {
                dataDisplay.classList.remove('active');
                chartDisplay.classList.add('active');

                // 更新标题
                const displayText = document.getElementById('display-text');
                const displayIcon = document.getElementById('display-icon');
                if (displayText) displayText.textContent = '图表展示';
                if (displayIcon) displayIcon.className = 'bi bi-bar-chart-fill';

                // 确保容器有足够的尺寸
                chartContainer.style.width = '100%';
                chartContainer.style.height = '400px';

                // 创建图表
                if (typeof echarts !== 'undefined') {
                    const chart = echarts.init(chartContainer);
                    const option = {
                        title: {
                            text: '销售数据柱状图',
                            left: 'center',
                            top: '10px',
                            textStyle: {
                                fontSize: 16,
                                fontWeight: 'bold'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: function (params) {
                                return params[0].name + '<br/>' +
                                    params[0].seriesName + ': ' +
                                    params[0].value.toLocaleString();
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            top: '50px',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            axisLabel: {
                                interval: 0,
                                rotate: 0
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: function (value) {
                                    return (value / 1000) + 'K';
                                }
                            }
                        },
                        series: [{
                            name: '销售额',
                            type: 'bar',
                            data: [120000, 135000, 148000, 162000, 175000, 188000],
                            itemStyle: {
                                color: '#5470c6'
                            },
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function (params) {
                                    return (params.value / 1000) + 'K';
                                }
                            },
                            barWidth: '60%'
                        }]
                    };

                    chart.setOption(option);

                    // 确保图表正确渲染
                    setTimeout(() => {
                        chart.resize();
                    }, 100);

                    console.log('Chart created successfully');
                } else {
                    console.error('ECharts is not loaded');
                    // 如果ECharts没有加载，显示一个简单的占位符
                    chartContainer.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 400px; background: #f5f5f5; border: 2px dashed #ccc;">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; color: #5470c6; margin-bottom: 10px;">📊</div>
                                <div style="font-size: 16px; color: #666;">销售数据柱状图</div>
                                <div style="font-size: 14px; color: #999; margin-top: 5px;">图表加载中...</div>
                            </div>
                        </div>
                    `;
                }

                // 显示图表控制按钮
                if (chartControls) {
                    const controlBtns = chartControls.querySelectorAll('.control-btn');
                    controlBtns.forEach(btn => {
                        btn.style.display = 'inline-block';
                    });
                }

                // 触发第六步：完成引导（测试时）
                setTimeout(() => {
                    window.postMessage({
                        type: 'simulateStep6'
                    }, '*');
                }, 1000);
            }
        };

        // 添加完成引导的函数
        window.completeGuide = function () {
            window.postMessage({
                type: 'completeGuide'
            }, '*');
        };

        // 添加第六步模拟函数
        window.simulateStep6 = function () {
            window.postMessage({
                type: 'simulateStep6'
            }, '*');
        };

        // 页面加载完成后，可以通过控制台调用相关函数来测试
        console.log('Guide functions loaded:');
        console.log('- Call testChartGeneration() to test chart generation');
        console.log('- Call simulateStep6() to simulate step 6 (auto reset after 3s)');
        console.log('- Call completeGuide() to complete guide and reset immediately');
    </script>
</body>

</html>