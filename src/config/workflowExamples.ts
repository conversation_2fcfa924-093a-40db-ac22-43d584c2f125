import { IconName } from './iconMap';

export interface WorkflowExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const workflowExamples: WorkflowExample[] = [
  {
    id: "pending-workflow",
    title: "待办流程查询",
    description: "查询需要处理的待办流程",
    message: "/待办流程",
    icon: "FileText",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "completed-workflow",
    title: "已办流程查询",
    description: "查询已经处理完成的流程",
    message: "/已办流程",
    icon: "FileSpreadsheet",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  }
];

export default workflowExamples; 