import React from 'react';
import {
  User,
  FileText,
  Search,
  Volume2,
  Bar<PERSON>hart3,
  Share2,
  MessageSquare,
  Image,
  Bot,
  Palette,
  Code,
  Sparkles,
  Video,
  PenTool,
  UserCheck,
  Music,
  Languages,
  FileArchive,
  FileImage,
  FileCode,
  FileSpreadsheet,
  Car,
  Building,
  MapPin,
  Mountain,
  Smile,
  Briefcase
} from 'lucide-react';

// Define available icon names as a type
export type IconName =
  | 'User'
  | 'FileText'
  | 'Search'
  | 'Volume2'
  | 'BarChart3'
  | 'Share2'
  | 'MessageSquare'
  | 'Image'
  | 'Bot'
  | 'Palette'
  | 'Code'
  | 'Sparkles'
  | 'Video'
  | 'PenTool'
  | 'UserCheck'
  | 'Music'
  | 'Languages'
  | 'FileArchive'
  | 'FileImage'
  | 'FileCode'
  | 'FileSpreadsheet'
  | 'Car'
  | 'Building'
  | 'MapPin'
  | 'Mountain'
  | 'Smile'
  | 'Briefcase';

// Icon name to component mapping
const iconComponents: Record<IconName, React.ComponentType<any>> = {
  User,
  FileText,
  Search,
  Volume2,
  BarChart3,
  Share2,
  MessageSquare,
  Image,
  Bot,
  Palette,
  <PERSON>,
  Sparkles,
  Video,
  PenTool,
  UserCheck,
  Music,
  Languages,
  FileArchive,
  FileImage,
  FileCode,
  FileSpreadsheet,
  Car,
  Building,
  MapPin,
  Mountain,
  Smile,
  Briefcase
};

/**
 * Returns the corresponding icon component for a given icon name
 * @param iconName - The name of the icon to get
 * @returns The icon component or undefined if not found
 */
export function getIconByName(iconName: IconName): React.ComponentType<any> {
  return iconComponents[iconName];
}

/**
 * Renders an icon based on its name with given properties
 * @param iconName - The name of the icon to render
 * @param props - Props to pass to the icon component
 */
export function renderIcon(iconName: IconName, props: any = {}): React.ReactNode {
  const IconComponent = getIconByName(iconName);

  if (!IconComponent) {
    console.warn(`Icon '${iconName}' not found`);
    return null;
  }

  return <IconComponent {...props} />;
}