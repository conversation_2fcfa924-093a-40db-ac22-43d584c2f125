// File processing buttons shown when a file is uploaded
import { MessageSquare, FileText, Sparkles } from 'lucide-react';
import React from 'react';

export interface FileFunctionButton {
  id: string;
  name: string;
  prefix: string;
  icon: React.ReactNode;
  description?: string;
}

const fileFunctionButtons: FileFunctionButton[] = [
  {
    id: "data-qa",
    name: "数据问答",
    prefix: "/文件问答 ",
    icon: React.createElement(MessageSquare, { className: "h-4 w-4 mr-1" }),
    description: "请根据文档回答我下面的问题：\n"
  },
  {
    id: "doc-summary",
    name: "文档摘要",
    prefix: "/文档摘要 ",
    icon: React.createElement(FileText, { className: "h-4 w-4 mr-1" }),
    description: "请总结这个文档的主要内容"
  },
  {
    id: "content-extraction",
    name: "内容提取",
    prefix: "/内容提取 ",
    icon: React.createElement(Sparkles, { className: "h-4 w-4 mr-1" }),
    description: "请从这个文档中提取以下信息：\n"
  }
];

export default fileFunctionButtons;
