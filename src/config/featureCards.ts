import { IconName } from './iconMap';
import mermaidExamples from './mermaidExamples';
import translationExamples from './translationExamples';
import knowledgeExamples from './knowledgeExamples';
import newsExamples from './newsExamples';
import workflowExamples from './workflowExamples';
import imageExamples from './imageExamples';
import premiumExamples from './premiumExamples';
import imageGenerationExamples from './imageGenerationExamples';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
  subExamples?: FeatureCard[];
}

const featureCards: FeatureCard[] = [
  {
    id: "plants",
    title: "公司新闻查询",
    description: "获取OA内网有关于领导讲话的公司新闻",
    message: "/新闻 领导讲话",
    icon: "User",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500",
    subExamples: newsExamples
  },
  {
    id: "onboarding",
    title: "流程查询",
    description: "查询OA系统内的流程信息",
    message: "/待办流程",
    icon: "FileText",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500",
    subExamples: workflowExamples
  },
  {
    id: "tech-market",
    title: "图片识别与分析",
    description: "智能识别和分析图像中的内容",
    message: "请识别这张图片中的文字",
    icon: "Image",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500",
    subExamples: imageExamples
  },
  {
    id: "travel-vlog",
    title: "知识库查询",
    description: "查询公司内部知识库信息",
    message: "/知识库 介绍四川省分公司",
    icon: "Volume2",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500",
    subExamples: knowledgeExamples
  },
  {
    id: "echarts",
    title: "Mermaid图表生成",
    description: "基于Mermaid语法快速生成可视化图表",
    message: "画一个饼图，用于描述5W1H中各项占比",
    icon: "BarChart3",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500",
    subExamples: mermaidExamples
  },
  {
    id: "translation",
    title: "文本翻译",
    description: "提供中英文互译功能",
    message: "/中译英 今天天气很不错，可以去公园散步",
    icon: "Languages",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500",
    subExamples: translationExamples
  },
  {
    id: "premium-query",
    title: "每日快报保费查询",
    description: "查询各类保费收入统计信息",
    message: "/保费查询 今天险类保费",
    icon: "FileSpreadsheet",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500",
    subExamples: premiumExamples
  },
  {
    id: "image-generation",
    title: "任意图像内容生成",
    description: "基于文字描述生成各类图像内容",
    message: "/图像生成 一幅宁静的湖泊风景，有山峦倒影和蓝天白云",
    icon: "Image",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500",
    subExamples: imageGenerationExamples
  }
];

export default featureCards; 