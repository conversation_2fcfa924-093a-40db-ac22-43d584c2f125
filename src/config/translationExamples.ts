import { IconName } from './iconMap';

export interface TranslationExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const translationExamples: TranslationExample[] = [
  {
    id: "zh-to-en",
    title: "中译英",
    description: "将中文翻译为英文",
    message: "/中译英 今天天气很不错，可以去公园散步",
    icon: "Share2",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "en-to-zh",
    title: "英译中",
    description: "将英文翻译为中文",
    message: "/英译中 The weather is nice today, we can go for a walk in the park",
    icon: "Languages",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  }
];

export default translationExamples; 