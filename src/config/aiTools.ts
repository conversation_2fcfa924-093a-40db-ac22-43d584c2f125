export interface AITool {
  id: string;
  title: string;
  description: string;
  url: string;
  iconUrl?: string; // 可选的内置图标
  colorClass: string;
  iconUrlColorClass: string;
  category: string; // 工具分类
  hasCheckIn?: boolean; // 是否支持签到领积分
  isFree?: boolean; // 是否完全免费
  isLimitedFree?: boolean; // 是否限时免费
}

// 定义工具分类
export interface ToolCategory {
  id: string;
  name: string;
  iconUrl: string;
  colorClass: string;
  iconUrlColorClass: string;
}

// 工具分类数据
export const toolCategories: ToolCategory[] = [
  {
    id: 'all',
    name: '全部工具',
    iconUrl: 'Sparkles',
    colorClass: 'bg-slate-50 dark:bg-slate-900/20',
    iconUrlColorClass: 'text-slate-500'
  },
  {
    id: 'chat',
    name: 'AI对话聊天',
    iconUrl: 'MessageSquare',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconUrlColorClass: 'text-green-500'
  },
  {
    id: 'image',
    name: 'AI图像工具',
    iconUrl: 'Image',
    colorClass: 'bg-red-50 dark:bg-red-900/20',
    iconUrlColorClass: 'text-red-500'
  },
  {
    id: 'search',
    name: 'AI搜索引擎',
    iconUrl: 'Search',
    colorClass: 'bg-blue-50 dark:bg-blue-900/20',
    iconUrlColorClass: 'text-blue-500'
  },
  {
    id: 'office',
    name: 'AI办公工具',
    iconUrl: 'FileText',
    colorClass: 'bg-amber-50 dark:bg-amber-900/20',
    iconUrlColorClass: 'text-amber-500'
  },
  {
    id: 'video',
    name: 'AI视频工具',
    iconUrl: 'Video',
    colorClass: 'bg-stone-50 dark:bg-stone-900/20',
    iconUrlColorClass: 'text-stone-500'
  }
]

// Sample AI tools data
const aiTools: AITool[] = [
  // -------------------- 对话 --------------------
  {
    "id": "deepseek",
    "title": "DeepSeek",
    "description": "支持本地部署的智能助手，涵盖中英文对话、代码生成和音乐创作",
    "url": "https://www.deepseek.com",
    "iconUrl": "https://cdn.deepseek.com/logo.png",
    "colorClass": "bg-gray-50 dark:bg-gray-900/20",
    "iconUrlColorClass": "text-gray-500",
    "category": "chat",
    "isFree": true
  },
  {
    "id": "yuanbao",
    "title": "元宝",
    "description": "腾讯元宝是基于腾讯混元大模型的AI应用，可以帮你写作绘画文案翻译编程搜索阅读总结的全能助手",
    "url": "https://yuanbao.tencent.com/",
    "iconUrl": "https://cdn-bot.hunyuan.tencent.com/logo.png",
    "colorClass": "bg-gray-50 dark:bg-gray-900/20",
    "iconUrlColorClass": "text-gray-500",
    "category": "chat",
    "isFree": true
  },
  {
    "id": "kimi",
    "title": "Kimi",
    "description": "Kimi是一款学生和职场人的新质生产力工具。帮你解读论文，写代码查BUG，策划方案，创作小说，多语言翻译。有问题问Kimi，一键解决你的所有难题",
    "url": "https://kimi.moonshot.cn",
    "iconUrl": "https://kimi.moonshot.cn/favicon.ico",
    "colorClass": "bg-blue-50 dark:bg-blue-900/20",
    "iconUrlColorClass": "text-blue-500",
    "category": "chat,office",
    "isFree": true
  },
  {
    "id": "doubao",
    "title": "豆包",
    "description": "豆包是你的 AI 聊天智能对话问答助手，写作文案翻译情感陪伴编程全能工具。豆包为你答疑解惑，提供灵感，辅助创作，也可以和你畅聊任何你感兴趣的话题。",
    "url": "https://www.doubao.com",
    "iconUrl": "https://lf-flow-web-cdn.doubao.com/obj/flow-doubao/samantha/logo-icon-white-bg.png",
    "colorClass": "bg-purple-50 dark:bg-purple-900/20",
    "iconUrlColorClass": "text-purple-500",
    "category": "chat,image",
    "isFree": true
  },
  {
    "id": "wenxin",
    "title": "文心一言",
    "description": "文心一言既是你的智能伙伴，可以陪你聊天、回答问题、画图识图；也是你的AI助手，可以提供灵感、撰写文案、阅读文档、智能翻译，帮你高效完成工作和学习任务。",
    "url": "https://yiyan.baidu.com",
    "iconUrl": "https://nlp-eb.cdn.bcebos.com/logo/favicon.ico",
    "colorClass": "bg-red-50 dark:bg-red-900/20",
    "iconUrlColorClass": "text-red-500",
    "category": "chat",
    "isFree": true
  },
  {
    "id": "tongyi",
    "title": "通义千问",
    "description": "通义是一个通情、达义的国产AI模型，可以帮你解答问题、文档阅读、联网搜索并写作总结，最多支持1000万字的文档速读。通义_你的全能AI助手",
    "url": "https://tongyi.aliyun.com",
    "iconUrl": "https://img.alicdn.com/imgextra/i4/O1CN01EfJVFQ1uZPd7W4W6i_!!6000000006051-2-tps-112-112.png",
    "colorClass": "bg-orange-50 dark:bg-orange-900/20",
    "iconUrlColorClass": "text-orange-500",
    "category": "chat",
    "hasCheckIn": true
  },
  {
    "id": "hunyuan",
    "title": "腾讯混元",
    "description": "腾讯混元大模型是由腾讯研发的大语言模型，具备跨领域知识和自然语言理解能力，实现基于人机自然语言对话的方式，理解用户指令并执行任务，帮助用户实现人获取信息，知识和灵感。",
    "url": "https://hunyuan.tencent.com/",
    "iconUrl": "https://cdn-portal.hunyuan.tencent.com/public/static/logo/favicon.png",
    "colorClass": "bg-orange-50 dark:bg-orange-900/20",
    "iconUrlColorClass": "text-orange-500",
    "category": "chat",
    "isFree": true
  },
  // -------------------- 搜索 --------------------
  {
    "id": "kuake",
    "title": "夸克",
    "description": "夸克AI超级框全新升级，以深度思考能力重新定义智能搜索！无论是经验总结、攻略制定还是复杂难题解析，只需输入指令即可触发多维度搜索与逻辑推理，快速输出精准答案。支持文字、图文、视频、表格等多形式输入，AI全能助手能精准识别内容核心，自动整合碎片信息生成结构化解决方案。从学术研究到职场办公，从生活技巧到创意灵感，跨场景覆盖用户需求。告别传统搜索的机械式反馈，让AI成为你的思维延伸，立即体验智慧生活的无限可能！",
    "url": "https://ai.quark.cn/",
    "iconUrl": "https://gw.alicdn.com/imgextra/i3/O1CN018r2tKf28YP7ev0fPF_!!6000000007944-2-tps-48-48.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "search,office",
    "isFree": true
  },
  {
    "id": "baidu",
    "title": "百度AI",
    "description": "百度AI搜索，一站式解决复杂问题，激发PC端超级生产力！独有「灵感探索」功能深入剖析问题核心，智能文字创作、图片创作、AI阅读、智能体海量应用启迪无限创意，开启高效智能学习办公新篇章！",
    "url": "https://chat.baidu.com/search",
    "iconUrl": "https://www.baidu.com/favicon.ico",
    "colorClass": "bg-blue-50 dark:bg-blue-900/20",
    "iconUrlColorClass": "text-blue-500",
    "category": "search",
    "isFree": true
  },
  {
    "id": "meta",
    "title": "秘塔AI",
    "description": "专业领域AI搜索引擎，秘塔AI搜索，没有广告，直达结果",
    "url": "https://metaso.cn",
    "iconUrl": "https://metaso.cn/apple-touch-icon.png",
    "colorClass": "bg-purple-50 dark:bg-purple-900/20",
    "iconUrlColorClass": "text-purple-500",
    "category": "search",
    "isFree": true
  },
  {
    "id": "nami",
    "title": "纳米AI",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "description": "纳米AI搜索开创全新问答方式，没有套路，直接给答案，让搜索变得简单直观！拍照问、语音搜、听答案，让搜索随心所欲，智慧触手可得。",
    "url": "https://bot.n.cn/?src=AIsearch",
    "iconUrl": "https://n.cn/favicon.ico",
    "category": "search",
    "isFree": true
  },
  {
    "id": "tiangong",
    "title": "天工超级智能体",
    "description": "天工Skywork是一款具备超强DeepResearch能力的全新AI Office智能体，通过3个专家agent和1个通用agent，让AI深度研究，一键生成AI文档、AI PPT、AI表格，高效应对各类办公、学习场景；也支持网页html、图像、视频、有声书、绘本等多种形式的创意内容创作，激发无限灵感。",
    "url": "https://www.tiangong.cn",
    "iconUrl": "https://static.tiangong.cn/prod-skywork-web-bundle/favicon.ico",
    "colorClass": "bg-red-50 dark:bg-red-900/20",
    "iconUrlColorClass": "text-red-500",
    "category": "search,office",
    "hasCheckIn": true
  },
  {
    "id": "bocha",
    "title": "博查AI",
    "description": "博查是一个给AI用的搜索引擎，它可以让你的AI应用连接世界知识，获得干净、准确、高质量的搜索结果。",
    "url": "https://bochaai.com",
    "iconUrl": "https://bochaai.com/icon.ico",
    "colorClass": "bg-yellow-50 dark:bg-yellow-900/20",
    "iconUrlColorClass": "text-yellow-500",
    "category": "search",
    "isFree": true
  },
  {
    "id": "xinliu",
    "title": "心流AI",
    "description": "心流是一款AI助手，帮助你高效获取知识，无论是日常娱乐生活百科还是专业学术论文知识，都可以轻松解答，让你快速进入心流状态，让知识随心流动！",
    "url": "https://iflow.cn/",
    "iconUrl": "https://img.alicdn.com/imgextra/i4/O1CN01yBfg3x1iNi4YggwIt_!!6000000004401-2-tps-72-72.png",
    "colorClass": "bg-indigo-50 dark:bg-indigo-900/20",
    "iconUrlColorClass": "text-indigo-500",
    "category": "search",
    "isFree": true
  },
  // -------------------- 图像 --------------------
  {
    "id": "digitalperson",
    "title": "安盟数字人",
    "description": "安盟数字人是一款AI创作平台，可激发艺术创意、提升绘画和视频创作体验。您可以利用AI智能，将想象变为现实。数字人支持文字绘图、文字生成视频和图片生成视频，并提供创作灵感。让数字人开启您的AI生成艺术之旅，探索创造的无限可能！",
    "url": "https://onlinesales-prod.groupama-sdig.com/login?redirect=/home/<USER>/material",
    "iconUrl": "https://www.groupama-sdig.com/static/public/images/favicon.ico",
    "colorClass": "bg-purple-50 dark:bg-purple-900/20",
    "iconUrlColorClass": "text-purple-500",
    "category": "video",
    "isFree": true
  },
  {
    "id": "jimeng",
    "title": "即梦AI",
    "description": "即梦AI是一个AI创作平台，可激发艺术创意、提升绘画和视频创作体验。您可以利用AI智能，将想象变为现实。即梦AI支持文字绘图、文字生成视频和图片生成视频，并提供创作灵感。让即梦AI开启您的AI生成艺术之旅，探索创造的无限可能！",
    "url": "https://jimeng.jianying.com/ai-tool/home",
    "iconUrl": "https://lf3-lv-buz.vlabstatic.com/obj/image-lvweb-buz/common/images/dreamina-v5.ico",
    "colorClass": "bg-purple-50 dark:bg-purple-900/20",
    "iconUrlColorClass": "text-purple-500",
    "category": "video,image",
    "hasCheckIn": true
  },
  {
    "id": "qiyu",
    "title": "奇域AI",
    "description": "奇域,探索新中式美学的AI绘画社区。利用人工智能生成精美的画作,展现东方美学的魅力。无论是艺术爱好者还是专业艺术设计师,都可以在奇域找到灵感。加入奇域,一起探索现代科技与中式审美的完美结合。",
    "url": "https://qiyuai.net",
    "iconUrl": "https://cdn.qiyuai.net/fe_shequ/v_web_material/D6D564930099E711090584EE73D0C905.png?imageMogr2/thumbnail/!30p/format/webp",
    "colorClass": "bg-red-50 dark:bg-red-900/20",
    "iconUrlColorClass": "text-red-500",
    "category": "image",
    "hasCheckIn": true
  },
  {
    "id": "keling",
    "title": "可灵AI",
    "description": "可灵AI，用于创建富有想象力的图像和视频的工具，基于最先进的生成AI方法。",
    "url": "https://app.klingai.com/cn/",
    "iconUrl": "https://p2-kling.klingai.com/kcdn/cdn-kcdn112452/kling-web-aio-prod_aio/favicon.ico",
    "colorClass": "bg-blue-50 dark:bg-blue-900/20",
    "iconUrlColorClass": "text-blue-500",
    "category": "video,image",
    "hasCheckIn": true
  },
  // {
  //   "id": "mermaid",
  //   "title": "Mermaid AI",
  //   "description": "简化文档并避免使用重型工具。开源Visio替代方案。常用于解释你的代码!美人鱼是一种简单的类似markdown的脚本语言，用于通过javascript从文本生成图表。",
  //   "url": "https://mermaid.live",
  //   "iconUrl": "https://mermaid.live/favicon.svg",
  //   "colorClass": "bg-orange-50 dark:bg-orange-900/20",
  //   "iconUrlColorClass": "text-orange-500",
  //   "category": "image"
  // },
  // -------------------- 办公 --------------------
  {
    "id": "wps",
    "title": "WPS灵犀",
    "description": "WPS灵犀是金山办公推出的一款强大的人工智能办公助手，旨在提升用户的办公效率与创作体验。它通过深度集成在WPS Office软件中，运用自然语言处理、机器学习等先进技术，为用户提供全方位的智能辅助服务。灵犀一指功能允许用户快速搜索、读文档、生成PPT等，且完全免费使用",
    "url": "https://copilot.wps.cn/",
    "iconUrl": "https://qn.cache.wpscdn.cn/copilot/copilot/favicon1.ico",
    "colorClass": "bg-blue-50 dark:bg-blue-900/20",
    "iconUrlColorClass": "text-blue-500",
    "category": "office",
    "isFree": true
  },
  {
    "id": "aippt",
    "title": "AiPPT",
    "description": "AiPPT结合最新AI技术，为用户提供一键生成高质量PPT的解决方案。无论是职场展示、教育课件还是销售报告，AiPPT均能快速生成符合需求的专业PPT，简化设计流程，提升工作效率。",
    "url": "https://www.aippt.cn",
    "iconUrl": "https://aippt.cn/favicon.ico",
    "colorClass": "bg-red-50 dark:bg-red-900/20",
    "iconUrlColorClass": "text-red-500",
    "category": "office"
  },
  {
    "id": "qingyan",
    "title": "清言PPT",
    "description": "清言PPT 1分钟搞定内容生成、PPT制作、生成演讲稿的全流程，让你的PPT作业不再痛苦~",
    "url": "https://chatglm.cn/main/alltoolsdetail?lang=zh",
    "iconUrl": "https://sfile.chatglm.cn/img2text/f4b41d6e-d496-4efc-ad00-2b9c3adc0b93.jpeg",
    "colorClass": "bg-purple-50 dark:bg-purple-900/20",
    "iconUrlColorClass": "text-purple-500",
    "category": "office",
    "isLimitedFree": true
  },
  {
    "id": "gaoding",
    "title": "稿定PPT",
    "description": "稿定PPT插件是一款辅助PPT制作的软件工具，支持Windows与Mac系统。上千款模板选择，海量素材持续更新，用更少的时间完成专业PPT设计",
    "url": "https://ppt.gaoding.com/",
    "iconUrl": "https://cdn.dancf.com/gaodingx/favicon.ico",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "office"
  },
  {
    "id": "tongyiwanxiang",
    "title": "通义万象",
    "description": "通义万相是阿里云通义旗下的AI创意作画平台，可提供AI艺术创作，可支持文生图、图生图、涂鸦作画、虚拟模特、个人写真等多场景的图片创作能力。",
    "url": "https://tongyi.aliyun.com/wanxiang/",
    "iconUrl": "https://img.alicdn.com/imgextra/i3/O1CN01O4Zouy1qaXx8mPVnp_!!6000000005512-2-tps-134-133.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video,image",
    "hasCheckIn": true
  },
  {
    "id": "hailuo",
    "title": "海螺AI",
    "description": "海螺视频工具 - 创新的AI视频生成器和提示词工具，可以将您的想法转化为精美的AI视频。只需一段文字，即可借助尖端的AI技术，在短时间内创作出引人入胜的视觉作品。现在就用海螺视频释放您的创造力吧。",
    "url": "https://hailuoai.com/video",
    "iconUrl": "https://cdn.hailuoai.com/hailuo-video-web/public_assets/favicon.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video,image",
    "hasCheckIn": true
  },
  {
    "id": "jieyueai",
    "title": "阶跃AI",
    "description": "阶跃AI是一个聪明可靠的个人效率助手，可以帮你获取知识、查询信息、学习语言、创意写作、编写代码，在工作、学习、生活等各种场景下帮你解决问题。阶跃AI，带你发现和理解世界~",
    "url": "https://www.stepfun.com/chats/new",
    "iconUrl": "https://yuewen.cn/svg/favicon.svg",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "image",
    "isFree": true
  },
  {
    "id": "zhipuqingying",
    "title": "智谱清影",
    "description": "清影是智谱清言打造的一个 AI 视频生成的智能体，依托于智谱大模型团队的视频生成大模型 CogVideo， 目前已经面向全体用户开放。 除了常规的「文生视频」以及「图生视频」的功能外，还可以对生成的视频「添加背景音乐」等。",
    "url": "https://chatglm.cn/main/alltoolsdetail?redirect=/video?lang=zh&lang=zh",
    "iconUrl": "https://chatglm.cn/img/icons/favicon.svg",
    "colorClass": "bg-rose-50 dark:bg-rose-900/20",
    "iconUrlColorClass": "text-rose-500",
    "category": "video",
    "isLimitedFree": true
  },
  {
    "id": "vidu",
    "title": "Vidu",
    "description": "Vidu AI 作为国内首个纯自研的AI视频生成模型，专注于将文字和图像转化为高质量的动态视频的同时，保持主体一致性。需3步即可生成创意视频，带您开启人工智能视频创作之旅。",
    "url": "https://www.vidu.cn/",
    "iconUrl": "https://www.vidu.cn/logo.svg",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video",
    "hasCheckIn": true
  },
  {
    "id": "jieyuevideo",
    "title": "阶跃视频",
    "description": "阶跃AI是一个聪明可靠的个人效率助手，可以帮你获取知识、查询信息、学习语言、创意写作、编写代码，在工作、学习、生活等各种场景下帮你解决问题。阶跃AI，带你发现和理解世界~",
    "url": "https://yuewen.cn/videos",
    "iconUrl": "https://yuewen.cn/svg/favicon.svg",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video",
    "isLimitedFree": true
  },
  {
    "id": "tingwu",
    "title": "通义听悟",
    "description": "阿里云通义听悟是聚焦音视频内容的工作学习AI助手，依托大模型，帮助用户记录、整理和分析音视频内容，体验用大模型做音视频笔记、整理会议记录。",
    "url": "https://tingwu.aliyun.com/home",
    "iconUrl": "https://img.alicdn.com/imgextra/i3/O1CN01eAuHNG205zmNf0mh4_!!6000000006799-2-tps-600-600.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "office",
    "hasCheckIn": true
  },
  {
    "id": "xuediansha",
    "title": "今天学点啥",
    "description": "「今天学点啥？」是由秘塔科技推出的一款AI活化知识的应用，以“让天下没有难学的知识”为使命，将任何文档转化为沉浸式视频课程，搭配定制化虚拟老师和实时互动功能。",
    "url": "https://metaso.cn/study#fromHistory",
    "iconUrl": "https://metaso.cn/apple-touch-icon.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "office",
    "isLimitedFree": true
  },
  {
    "id": "xingliu",
    "title": "星流",
    "description": "星流, AI绘画, AI生图, AI图片, AI绘图,免费,风格转换,图片衍生,局部重绘,拼贴重绘,高清放大,超清放大,去除背景,AI去除背景,智能去除背景,智能擦除",
    "url": "https://www.xingliu.art",
    "iconUrl": "https://liblibai-web-static.liblib.cloud/xingliu_online/community/static/_next/static/media/xingliu-logo.2491dd50.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video,image",
    "hasCheckIn": true
  },
  {
    "id": "huixiang",
    "title": "绘想",
    "description": "绘想-百度旗下AI视频创作平台",
    "url": "https://huixiang.baidu.com/",
    "iconUrl": "https://huixiang.baidu.com/img/logo2.279fdc40.png",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "video,image",
    "isLimitedFree": true
  },
  {
    "id": "zhipu-zai",
    "title": "智谱-Z-AI",
    "description": "智谱旗下AI对话平台，与人工智能助理开始免费聊天。告诉 Z.ai 您的需求--令人惊叹的演示文稿、专业级的写作或复杂的代码脚本--就能立即得到结果。",
    "url": "https://chat.z.ai/",
    "iconUrl": "https://chat.z.ai/static/logoLight.svg",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "office",
    "isLimitedFree": true
  },
  {
    "id": "meituan-roboneo",
    "title": "RoboNeo",
    "description": "美团旗下-RoboNeo - 专注影像与设计的AI助手。一句话修图、搞设计、做视频，更多创意玩法等你来挖掘。",
    "url": "https://www.roboneo.com/home",
    "iconUrl": "https://roboneo-public.meitudata.com/public/145b032b37/_next/static/media/favicon.067de052.ico",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "image,video",
    "isLimitedFree": true
  },
  {
    "id": "xiaohuanxiong",
    "title": "办公小浣熊",
    "description": "办公小浣熊是一款将AI大模型与文档编辑、数据分析场景深度结合的工具型产品，一站式创作平台和知识管理空间。",
    "url": "https://office.xiaohuanxiong.com/home",
    "iconUrl": "https://office.xiaohuanxiong.com/favicon.svg",
    "colorClass": "bg-green-50 dark:bg-green-900/20",
    "iconUrlColorClass": "text-green-500",
    "category": "office",
    "isLimitedFree": true
  },
];

export default aiTools;
