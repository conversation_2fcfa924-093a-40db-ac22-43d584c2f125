import { IconName } from './iconMap';

export interface ImageGenerationExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const imageGenerationExamples: ImageGenerationExample[] = [
  {
    id: "nature-landscape",
    title: "自然风景生成",
    description: "生成美丽的自然风景图片",
    message: "/图像生成 一幅宁静的湖泊风景，有山峦倒影和蓝天白云",
    icon: "Mountain",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "cartoon-character",
    title: "卡通角色生成",
    description: "创建可爱的卡通人物形象",
    message: "/图像生成 一个友善的机器人卡通角色，有大眼睛和微笑表情",
    icon: "Smile",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "abstract-art",
    title: "抽象艺术生成",
    description: "创作现代抽象艺术作品",
    message: "/图像生成 一幅色彩丰富的抽象画，包含几何图形和渐变色彩",
    icon: "Palette",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "business-scene",
    title: "商务场景生成",
    description: "生成专业的商务办公场景",
    message: "/图像生成 现代化办公室会议室，有会议桌椅和落地窗",
    icon: "Briefcase",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  }
];

export default imageGenerationExamples;
