import { IconName } from './iconMap';

export interface NewsExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const newsExamples: NewsExample[] = [
  {
    id: "leadership-speech",
    title: "领导讲话",
    description: "查询领导重要讲话内容",
    message: "/新闻 领导讲话",
    icon: "User",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "company-events",
    title: "公司活动",
    description: "查询公司近期重要活动报道",
    message: "/新闻 公司近期活动",
    icon: "FileText",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "industry-trends",
    title: "行业动态",
    description: "了解行业最新发展趋势",
    message: "/新闻 行业最新动态",
    icon: "BarChart3",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "business-achievements",
    title: "业务成果",
    description: "查询公司业务成就与项目进展",
    message: "/新闻 业务成果",
    icon: "FileSpreadsheet",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "employee-highlights",
    title: "员工风采",
    description: "展示公司优秀员工事迹",
    message: "/新闻 员工风采",
    icon: "UserCheck",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "policy-updates",
    title: "政策更新",
    description: "了解公司最新政策调整",
    message: "/新闻 政策更新",
    icon: "FileCode",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  }
];

export default newsExamples; 