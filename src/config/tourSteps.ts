import { TourStep } from '@/components/tour/TourGuide'

// 主要功能引导步骤
export const mainTourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: '欢迎使用 MengChat 办公助手！',
    content: '我将为您介绍系统的主要功能，帮助您快速上手。让我们开始吧！',
    target: '[data-tour="main"]',
    position: 'center',
    offset: { x: 0, y: 0 }
  },
  {
    id: 'sidebar',
    title: '侧边栏导航',
    content: '这里是功能导航区域。您可以在这里切换不同的功能模块，管理会话历史，以及访问各种工具。',
    target: '[data-tour="sidebar"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'new-conversation',
    title: '新建对话',
    content: '点击这里可以开始新的对话。每个对话都会被自动保存，您可以随时切换回之前的对话。',
    target: '[data-tour="new-conversation"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'ai-chats',
    title: 'AI 聊天对话',
    content: '这里提供了AI聊天对话功能，可通过会话列表查看历史会话，也可通过输入框与AI进行对话。',
    target: '[data-tour="ai-chats"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'ai-tools',
    title: 'AI 工具推荐',
    content: '这里汇集了各种实用的AI工具，按类别分组，帮助您发现和使用更多AI应用。',
    target: '[data-tour="ai-tools"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'doc-processing',
    title: '文档处理',
    content: '强大的文档处理功能，支持Word、Excel、PDF等格式的翻译、总结、格式转换等操作。',
    target: '[data-tour="doc-processing"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'visualization',
    title: '数据可视化',
    content: '上传Excel文件，AI将帮您生成各种图表，让数据分析变得简单直观。',
    target: '[data-tour="visualization"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'chat-input',
    title: '聊天输入区',
    content: '在这里输入您的问题或需求。支持文本输入、文件上传，还有快捷功能按钮帮您快速开始。',
    target: '[data-tour="chat-input"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'function-buttons',
    title: '快捷功能按钮',
    content: '这些按钮提供常用功能的快速访问，如新闻查询、通讯录搜索、知识库检索等。',
    target: '[data-tour="function-buttons"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'file-upload',
    title: '文件上传',
    content: '点击这里可以上传文件，支持图片、文档、表格等多种格式。AI会智能分析文件内容。',
    target: '[data-tour="file-upload"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'image-upload',
    title: '图片上传功能',
    content: '点击这个按钮可以专门上传图片文件。AI可以识别图片内容、提取文字、分析图表，还能回答关于图片的问题。',
    target: '[data-tour="image-upload"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'main-start-using',
    title: '开始使用',
    content: '恭喜！您已经了解了系统的主要功能。现在可以开始使用MengChat办公助手，体验AI带来的高效工作体验。祝您使用愉快！',
    target: 'main',
    position: 'center',
    offset: { x: 0, y: 0 }
  }
]

// 聊天功能引导步骤
export const chatTourSteps: TourStep[] = [
  {
    id: 'chat-welcome',
    title: '聊天功能介绍',
    content: '这里是AI聊天的主要区域，您可以与AI进行自然对话，获取各种帮助和信息。整个右侧面板就是您的智能对话空间。',
    target: '[data-tour="chat-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  },
  {
    id: 'new-conversation',
    title: '开启新对话',
    content: '点击"开启新对话"可以开始全新的对话主题，之前的对话会自动保存在侧边栏中，方便您随时回顾。',
    target: '[data-tour="new-conversation"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'function-buttons',
    title: '快捷功能按钮',
    content: '这些按钮提供常用功能的快速访问，如新闻查询、通讯录搜索、知识库检索等，点击即可快速开始相关对话。',
    target: '[data-tour="function-buttons"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'file-upload',
    title: '文件上传功能',
    content: '点击这个按钮可以上传文档、表格、PDF等各种文件。AI会智能分析文件内容，为您提供相关的帮助和解答。',
    target: '[data-tour="file-upload"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'image-upload',
    title: '图片上传功能',
    content: '点击这个按钮可以专门上传图片文件。AI可以识别图片内容、提取文字、分析图表，还能回答关于图片的问题。',
    target: '[data-tour="image-upload"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'chat-input-area',
    title: '聊天输入区域',
    content: '在这里输入您的问题或需求，支持文本输入。您也可以拖拽文件到这个区域进行上传，非常方便。',
    target: '[data-tour="chat-input"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'feature-cards-intro',
    title: '快速开始卡片',
    content: '当您开始新对话时，聊天区域会显示功能卡片，展示系统的主要能力。点击任意卡片可以快速开始相应的对话主题。',
    target: '[data-tour="feature-cards"]',
    position: 'bottom',
    offset: { x: 0, y: 10 },
    condition: 'hasFeatureCards'
  },
  {
    id: 'chat-features',
    title: '消息操作功能',
    content: '在已有对话中，每条AI回复都会有丰富的操作选项：点赞/点踩反馈、重新生成答案、复制内容、分享消息、语音朗读等，让您的聊天体验更加便捷。',
    target: '[data-tour="last-ai-message"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    condition: 'hasMessages'
  },
  {
    id: 'chat-start-using',
    title: '开始使用',
    content: '现在您已经了解了聊天功能的完整使用方法。可以开始与AI对话，体验智能助手的强大功能。祝您使用愉快！',
    target: '[data-tour="chat-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  }
]


// AI工具引导步骤
export const aiToolsTourSteps: TourStep[] = [
  {
    id: 'tools-welcome',
    title: '欢迎来到 AI 工具推荐！',
    content: '这里汇集了各种优秀的AI工具，涵盖对话、写作、图像、视频、音频、开发等多个领域，帮助您提高工作效率。让我为您介绍如何使用这个页面。',
    target: '[data-tour="ai-tools-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  },
  {
    id: 'tools-search',
    title: '智能搜索功能',
    content: '在这里输入关键词可以快速找到您需要的AI工具。支持按工具名称、功能描述进行搜索，让您快速定位到合适的工具。',
    target: '[data-tour="tools-search"]',
    position: 'bottom',
    offset: { x: 0, y: 10 }
  },
  {
    id: 'tools-categories',
    title: '工具分类浏览',
    content: '工具按功能精心分类：对话聊天、写作助手、图像处理、视频制作、音频编辑、开发工具等。点击不同分类可以筛选相应的工具。',
    target: '[data-tour="tools-categories"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'tool-card',
    title: '工具卡片详情',
    content: '每个工具卡片显示：工具名称、功能描述、特色标签（如免费、限时免费等）。点击卡片即可访问对应的AI工具网站。',
    target: '[data-tour="tool-card"]:first-child',
    position: 'top',
    offset: { x: 0, y: -10 }
  },
  {
    id: 'tools-start-using',
    title: '开始使用',
    content: '现在您已经了解了AI工具推荐的主要功能。可以开始搜索、浏览和使用各种AI工具来提高您的工作效率。祝您使用愉快！',
    target: '[data-tour="ai-tools-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  }
]

// 文档处理引导步骤
export const docProcessingTourSteps: TourStep[] = [
  {
    id: 'doc-welcome',
    title: '欢迎使用文档处理功能！',
    content: '这里提供强大的文档处理工具，支持Word、Excel、PDF、PowerPoint等多种格式的翻译、总结、格式转换等操作。让我为您介绍如何使用。',
    target: '[data-tour="doc-processing-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  },
  {
    id: 'doc-categories',
    title: '文档类型选择',
    content: '首先选择您要处理的文档类型：Word文档、Excel表格、PDF文件或PowerPoint演示文稿。不同类型支持不同的处理功能。',
    target: '[data-tour="doc-categories"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'doc-feature-selection',
    title: '功能选择',
    content: '选择文档类型后，这里会显示该类型支持的所有功能，如翻译、总结、格式转换等。点击相应的功能卡片开始处理。',
    target: '[data-tour="doc-feature-selection"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  },
  {
    id: 'doc-upload-area',
    title: '文件上传区域',
    content: '选择功能后，您可以通过拖拽或点击的方式上传文档文件。系统会自动识别文件格式并进行相应处理。',
    target: '[data-tour="doc-upload"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 模拟点击第一个功能卡片，显示上传界面
      const firstCard = document.querySelector('[data-feature-card]') as HTMLElement
      if (firstCard) {
        firstCard.click()
      }
    }
  },
  {
    id: 'doc-settings',
    title: '处理参数设置',
    content: '上传文件后，根据需要调整处理参数，如翻译的目标语言、总结的详细程度、输出格式等。这些设置会影响最终的处理结果。',
    target: '[data-tour="doc-settings"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 模拟文件上传完成，显示参数设置界面
      // 这需要触发文档处理组件的状态变化
      const event = new CustomEvent('simulateFileUpload', {
        detail: { fileName: 'demo.docx', fileType: 'docx' }
      })
      window.dispatchEvent(event)
    }
  },
  {
    id: 'doc-progress',
    title: '处理进度',
    content: '点击开始处理后，系统会显示处理进度。您可以实时查看处理状态和进度百分比。',
    target: '[data-tour="doc-progress"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 模拟开始处理
      const event = new CustomEvent('simulateStartProcessing')
      window.dispatchEvent(event)
    }
  },
  {
    id: 'doc-result',
    title: '处理结果',
    content: '处理完成后，这里会显示处理结果和下载链接。您可以预览结果、下载处理后的文件，或查看处理历史。',
    target: '[data-tour="doc-result"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 模拟处理完成
      const event = new CustomEvent('simulateProcessComplete')
      window.dispatchEvent(event)
    }
  },
  {
    id: 'doc-start-using',
    title: '开始使用',
    content: '现在您已经了解了文档处理的完整流程。可以开始上传您的文档，体验强大的AI文档处理功能。祝您使用愉快！',
    target: '[data-tour="doc-processing-panel"]',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 恢复到初始状态
      const event = new CustomEvent('resetDocProcessing')
      window.dispatchEvent(event)
    }
  }
]

// 数据可视化引导步骤
export const visualizationTourSteps: TourStep[] = [
  {
    id: 'viz-welcome',
    title: '欢迎使用数据可视化助手！',
    content: '这是一个强大的Excel数据可视化工具。您只需上传Excel文件，然后通过自然语言对话，AI就能为您生成各种精美的图表。让我为您介绍使用方法。',
    target: '[data-tour="visualization-panel"]',
    position: 'center'
  },


  {
    id: 'viz-data-preview',
    title: '数据预览',
    content: '这里会显示Excel数据的预览表格，让您确认数据是否正确读取。让我们加载一些示例数据来演示。',
    target: '[data-tour="visualization-panel"] iframe',
    position: 'left',
    offset: { x: -20, y: 0 },
    simulateAction: () => {
      // 模拟文件上传以显示数据预览
      const iframe = document.querySelector('iframe[title="Excel数据可视化助手"]') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          type: 'simulateFileUpload',
          data: {
            fileName: 'sales-data.xlsx',
            mockData: [
              { '月份': '1月', '销售额': 120000, '订单数': 450, '客户数': 280 },
              { '月份': '2月', '销售额': 135000, '订单数': 520, '客户数': 310 },
              { '月份': '3月', '销售额': 148000, '订单数': 580, '客户数': 340 },
              { '月份': '4月', '销售额': 162000, '订单数': 620, '客户数': 380 },
              { '月份': '5月', '销售额': 175000, '订单数': 680, '客户数': 420 },
              { '月份': '6月', '销售额': 188000, '订单数': 720, '客户数': 450 }
            ]
          }
        }, '*')
      }
    }
  },
  {
    id: 'viz-chat-panel',
    title: '左侧消息面板',
    content: '这是AI对话区域，您可以在这里与AI助手交流。AI会根据您的需求生成相应的图表，并在对话中提供建议和解释。',
    target: '[data-tour="visualization-panel"] iframe',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'viz-chart-generation',
    title: '右侧生成图表',
    content: '在左侧输入您的图表需求，例如"生成销售数据的柱状图"，AI会在这个区域为您生成相应的可视化图表。',
    target: '[data-tour="visualization-panel"] iframe',
    position: 'left',
    offset: { x: -10, y: 0 },
    simulateAction: () => {
      // 模拟生成柱状图
      const iframe = document.querySelector('iframe[title="Excel数据可视化助手"]') as HTMLIFrameElement
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({
          type: 'simulateBarChart',
          data: {
            chartType: 'bar',
            title: '销售数据柱状图',
            data: [
              { name: '1月', value: 120000 },
              { name: '2月', value: 135000 },
              { name: '3月', value: 148000 },
              { name: '4月', value: 162000 },
              { name: '5月', value: 175000 },
              { name: '6月', value: 188000 }
            ]
          }
        }, '*')
      }
    }
  },
  {
    id: 'viz-chart-controls',
    title: '图表功能按钮',
    content: '图表生成后，右上角会出现各种功能按钮：切换图表类型、显示/隐藏数值标签、全屏显示、下载图表等，让您可以自定义图表的显示效果。',
    target: '[data-tour="visualization-panel"] iframe',
    position: 'bottom',
    offset: { x: 0, y: 10 }
  },
  {
    id: 'viz-start-using',
    title: '开始使用',
    content: '现在您已经了解了数据可视化的完整流程。可以开始上传您的Excel文件，与AI对话生成精美的数据图表。祝您使用愉快！',
    target: '[data-tour="visualization-panel"]',
    position: 'center'
  }
]

// 测试引导步骤
export const testTourSteps: TourStep[] = [
  {
    id: 'test-welcome',
    title: '欢迎使用引导系统！',
    content: '这是一个测试引导，将为您介绍页面的各个功能区域。',
    target: '[data-tour="test-title"]',
    position: 'bottom',
    offset: { x: 0, y: 10 }
  },
  {
    id: 'test-controls',
    title: '引导控制区域',
    content: '在这里您可以控制引导的开始、重置等操作。',
    target: '[data-tour="test-controls"]',
    position: 'bottom',
    offset: { x: 0, y: 10 }
  },
  {
    id: 'test-feature-1',
    title: '功能区域 1',
    content: '这是第一个功能区域，展示了引导系统如何精确定位到特定元素。',
    target: '[data-tour="test-feature-1"]',
    position: 'right',
    offset: { x: 10, y: 0 }
  },
  {
    id: 'test-feature-2',
    title: '功能区域 2',
    content: '这是第二个功能区域，测试引导系统在不同位置的显示效果。',
    target: '[data-tour="test-feature-2"]',
    position: 'left',
    offset: { x: -10, y: 0 }
  },
  {
    id: 'test-actions',
    title: '操作区域',
    content: '最后，这里是操作区域，您可以在这里进行各种操作。引导系统测试完成！',
    target: '[data-tour="test-actions"]',
    position: 'top',
    offset: { x: 0, y: -10 }
  }
]

// 根据功能模块获取对应的引导步骤
export function getTourSteps(module: 'main' | 'chat' | 'ai-tools' | 'doc-processing' | 'visualization' | 'test'): TourStep[] {
  switch (module) {
    case 'main':
      return mainTourSteps
    case 'chat':
      return chatTourSteps
    case 'ai-tools':
      return aiToolsTourSteps
    case 'doc-processing':
      return docProcessingTourSteps
    case 'visualization':
      return visualizationTourSteps
    case 'test':
      return testTourSteps
    default:
      return mainTourSteps
  }
}

// 引导配置
export interface TourConfig {
  autoStart: boolean // 是否自动开始引导
  showOnFirstVisit: boolean // 是否在首次访问时显示
  storageKey: string // 本地存储键名
}

export const defaultTourConfig: TourConfig = {
  autoStart: false,
  showOnFirstVisit: true,
  storageKey: 'mengchat_tour_completed'
}
