// OCR and image processing buttons shown when an image is uploaded
import { FileText, FileImage, Search, Sparkles } from 'lucide-react';
import React from 'react';

export interface OcrFunctionButton {
  id: string;
  name: string;
  prefix: string;
  icon: React.ReactNode;
  description: string;
}

const ocrFunctionButtons: OcrFunctionButton[] = [
  {
    id: "ocr-recognition",
    name: "OCR 文字识别",
    prefix: "/ocr ",
    icon: React.createElement(FileText, { className: "h-4 w-4 mr-1" }),
    description: "请识别这张图片中的文字"
  },
  {
    id: "image-summary",
    name: "图片内容总结",
    prefix: "/ocr ",
    icon: React.createElement(FileImage, { className: "h-4 w-4 mr-1" }),
    description: "请总结这张图片的主要内容"
  },
  {
    id: "image-analysis",
    name: "图片内容分析",
    prefix: "/ocr ",
    icon: React.createElement(Search, { className: "h-4 w-4 mr-1" }),
    description: "请详细分析这张图片中的内容"
  },
  {
    id: "image-extraction",
    name: "图片内容抽取",
    prefix: "/ocr ",
    icon: React.createElement(Sparkles, { className: "h-4 w-4 mr-1" }),
    description: "请从这张图片中抽取表格数据"
  }
];

export default ocrFunctionButtons; 