// 版本更新内容配置
export interface VersionUpdate {
  version: string;
  content: string;
  details?: string; // 可选的详细描述
}

// 从环境变量中获取版本更新内容，如果不存在则使用默认值
export function getVersionUpdates(): VersionUpdate[] {
  try {
    // 尝试从环境变量中解析版本更新内容
    if (process.env.NEXT_PUBLIC_VERSION_UPDATES) {
      return JSON.parse(process.env.NEXT_PUBLIC_VERSION_UPDATES);
    }
  } catch (error) {
    console.error('解析版本更新内容失败:', error);
  }

  // 默认的版本更新内容 - 只保留最新版本
  return [
    {
      version: "v1.5.0",
      content: "1. 新增图表可视化功能，支持柱状图、折线图、饼图等多种图表类型\n2. 优化文件上传和图片处理功能，支持更大文件上传，提高图片显示质量\n3. 新增代码高亮和格式化功能，支持多种编程语言\n4. 支持Markdown格式的富文本编辑\n5. 新增文档处理和分析功能\n6. 优化用户界面，提升整体用户体验\n7. 新增Excel可视化功能，支持上传Excel文件并生成图表\n8. 优化聊天界面，提升消息显示效果和交互体验"
    }
  ];
}