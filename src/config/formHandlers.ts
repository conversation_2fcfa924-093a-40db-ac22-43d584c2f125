// 表单处理配置文件
// 用于定义不同表单的处理逻辑

export interface FormHandlerConfig {
  // 匹配条件
  matcher: {
    formId?: string | string[]           // 表单ID匹配
    submitValue?: string | string[]      // 提交按钮值匹配（支持部分匹配）
    className?: string | string[]        // 表单class匹配
    customMatcher?: (formElement: HTMLFormElement, formData: FormData) => boolean // 自定义匹配函数
  }
  
  // 处理配置
  handler: {
    type: 'message' | 'api' | 'custom'   // 处理类型
    messageTemplate?: string             // 消息模板
    apiEndpoint?: string                 // API端点
    customHandler?: (formData: FormData, formElement: HTMLFormElement, onSendMessage?: (content: string) => void) => void
    successMessage?: string              // 成功提示消息
    errorMessage?: string                // 错误提示消息
  }
  
  // 优先级（数字越大优先级越高）
  priority?: number
}

// 表单处理配置
export const formHandlerConfigs: FormHandlerConfig[] = [
  // 图表生成表单
  {
    matcher: {
      formId: ['reminderForm', 'chartForm'],
      submitValue: ['生成数据图表', '生成图表', '创建图表']
    },
    handler: {
      type: 'message',
      messageTemplate: '/生成图表',
      successMessage: '图表生成请求已提交！AI正在为您生成数据图表...'
    },
    priority: 10
  },
  
  // 查询表单
  {
    matcher: {
      submitValue: ['查询', '搜索', '检索', 'search', 'query']
    },
    handler: {
      type: 'message',
      messageTemplate: '查询请求：{formData}',
      successMessage: '查询请求已提交！'
    },
    priority: 8
  },
  
  // 数据分析表单
  {
    matcher: {
      submitValue: ['分析', '统计', '报告', 'analyze']
    },
    handler: {
      type: 'message',
      messageTemplate: '数据分析请求：\n{formData}\n\n请对以上数据进行分析并生成报告。',
      successMessage: '数据分析请求已提交！'
    },
    priority: 8
  },
  
  // 文件处理表单
  {
    matcher: {
      submitValue: ['上传', '处理文件', '文件分析']
    },
    handler: {
      type: 'message',
      messageTemplate: '文件处理请求：{formData}',
      successMessage: '文件处理请求已提交！'
    },
    priority: 7
  },
  
  // 自定义API调用示例
  {
    matcher: {
      formId: 'apiForm',
      submitValue: ['调用API', 'API请求']
    },
    handler: {
      type: 'custom',
      customHandler: (formData, formElement, onSendMessage) => {
        // 自定义处理逻辑
        console.log('执行自定义API调用:', formData)
        
        // 可以在这里调用外部API
        // fetch('/api/custom-endpoint', { ... })
        
        if (onSendMessage) {
          const dataEntries = Array.from(formData.entries())
          const apiRequest = dataEntries
            .map(([key, value]) => `${key}: ${value}`)
            .join('\n')
          
          onSendMessage(`API调用请求：\n${apiRequest}`)
        }
      },
      successMessage: 'API调用已执行！'
    },
    priority: 9
  },
  
  // 默认处理（优先级最低）
  {
    matcher: {
      customMatcher: () => true // 匹配所有表单
    },
    handler: {
      type: 'message',
      messageTemplate: '表单提交数据：\n{formData}',
      successMessage: '表单已提交！'
    },
    priority: 1
  }
]

// 表单数据格式化函数
export const formatFormData = (formData: FormData, template: string): string => {
  const dataEntries = Array.from(formData.entries())
  
  if (template.includes('{formData}')) {
    const formDataText = dataEntries
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n')
    
    return template.replace('{formData}', formDataText)
  }
  
  return template
}

// 匹配表单处理配置
export const matchFormHandler = (
  formElement: HTMLFormElement, 
  formData: FormData
): FormHandlerConfig | null => {
  const formId = formElement.id || formElement.getAttribute('id') || ''
  const submitButton = formElement.querySelector('input[type="submit"], button[type="submit"]') as HTMLInputElement | HTMLButtonElement
  const submitValue = submitButton?.value || submitButton?.textContent || ''
  const className = formElement.className || ''
  
  // 按优先级排序
  const sortedConfigs = [...formHandlerConfigs].sort((a, b) => (b.priority || 0) - (a.priority || 0))
  
  for (const config of sortedConfigs) {
    const { matcher } = config
    let isMatch = false
    
    // 检查表单ID匹配
    if (matcher.formId) {
      const formIds = Array.isArray(matcher.formId) ? matcher.formId : [matcher.formId]
      if (formIds.some(id => formId === id)) {
        isMatch = true
      }
    }
    
    // 检查提交按钮值匹配
    if (!isMatch && matcher.submitValue) {
      const submitValues = Array.isArray(matcher.submitValue) ? matcher.submitValue : [matcher.submitValue]
      if (submitValues.some(value => submitValue.includes(value))) {
        isMatch = true
      }
    }
    
    // 检查class匹配
    if (!isMatch && matcher.className) {
      const classNames = Array.isArray(matcher.className) ? matcher.className : [matcher.className]
      if (classNames.some(cls => className.includes(cls))) {
        isMatch = true
      }
    }
    
    // 检查自定义匹配函数
    if (!isMatch && matcher.customMatcher) {
      if (matcher.customMatcher(formElement, formData)) {
        isMatch = true
      }
    }
    
    if (isMatch) {
      return config
    }
  }
  
  return null
}
