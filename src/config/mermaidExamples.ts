import { IconName } from './iconMap';

export interface MermaidExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const mermaidExamples: MermaidExample[] = [
  {
    id: "xychart",
    title: "画柱状图",
    description: "描述不同类别之间的对比关系",
    message: "画一个柱状图，a产品10个，b产品20个，c产品30个",
    icon: "FileSpreadsheet",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "pie",
    title: "画饼图",
    description: "显示比例和百分比的数据可视化",
    message: "画一个饼图，用于描述5W1H中各项占比",
    icon: "BarChart3",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "flowchart",
    title: "画流程图",
    description: "创建简洁明了的流程图，展示过程和决策",
    message: "画一个流程图，展示用户注册流程，包含注册、验证、登录步骤",
    icon: "FileCode",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "sequence",
    title: "画序列图",
    description: "展示对象之间交互和消息传递的时序",
    message: "画一个序列图，表示用户、服务端和数据库之间的登录交互流程",
    icon: "Code",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "gantt",
    title: "画甘特图",
    description: "创建项目进度计划和时间安排可视化",
    message: "画一个甘特图，计划一个为期两个月的软件开发项目",
    icon: "FileText",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "mindmap",
    title: "画思维导图",
    description: "创建视觉化的思维导图，展示概念间关系",
    message: "画一个思维导图，关于项目管理的主要组成部分",
    icon: "Sparkles",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  }
];

export default mermaidExamples; 