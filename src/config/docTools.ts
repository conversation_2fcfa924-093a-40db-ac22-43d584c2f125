import { IconName } from './iconMap';

export interface DocTool {
  id: string;
  title: string;
  description: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
  category: string; // 工具分类
}

// 定义工具分类
export interface DocCategory {
  id: string;
  name: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

// 文档处理分类数据
export const docCategories: DocCategory[] = [
  {
    id: 'word',
    name: 'Word文档处理',
    icon: 'FileText',
    colorClass: 'bg-blue-50 dark:bg-blue-900/20',
    iconColorClass: 'text-blue-500'
  },
  {
    id: 'ppt',
    name: 'PPT文档处理',
    icon: 'FileImage',
    colorClass: 'bg-red-50 dark:bg-red-900/20',
    iconColorClass: 'text-red-500'
  },
  {
    id: 'pdf',
    name: 'PDF文档处理',
    icon: 'FileCode',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconColorClass: 'text-green-500'
  },
  {
    id: 'excel',
    name: 'Excel文档处理',
    icon: 'FileSpreadsheet',
    colorClass: 'bg-amber-50 dark:bg-amber-900/20',
    iconColorClass: 'text-amber-500'
  }
];

// 文档处理工具数据
const docTools: DocTool[] = [
  {
    id: 'word-translate',
    title: 'Word文档翻译',
    description: '将Word文档翻译成其他语言，支持多种语言互译',
    icon: 'Languages',
    colorClass: 'bg-blue-50 dark:bg-blue-900/20',
    iconColorClass: 'text-blue-500',
    category: 'word'
  },
  {
    id: 'word-summary',
    title: 'Word文档摘要',
    description: '自动生成Word文档的摘要内容，提取关键信息',
    icon: 'FileText',
    colorClass: 'bg-blue-50 dark:bg-blue-900/20',
    iconColorClass: 'text-blue-500',
    category: 'word'
  },
  {
    id: 'word-to-pdf',
    title: 'Word转PDF',
    description: '将Word文档转换为PDF格式，保持原有格式和布局',
    icon: 'FileCode',
    colorClass: 'bg-blue-50 dark:bg-blue-900/20',
    iconColorClass: 'text-blue-500',
    category: 'word'
  },
  {
    id: 'ppt-translate',
    title: 'PPT文档翻译',
    description: '将PPT文档翻译成其他语言，支持多种语言互译',
    icon: 'Languages',
    colorClass: 'bg-red-50 dark:bg-red-900/20',
    iconColorClass: 'text-red-500',
    category: 'ppt'
  },
  {
    id: 'ppt-summary',
    title: 'PPT文档摘要',
    description: '自动生成PPT文档的摘要内容，提取关键信息',
    icon: 'FileText',
    colorClass: 'bg-red-50 dark:bg-red-900/20',
    iconColorClass: 'text-red-500',
    category: 'ppt'
  },
  {
    id: 'pdf-translate',
    title: 'PDF文档翻译',
    description: '将PDF文档翻译成其他语言，支持多种语言互译',
    icon: 'Languages',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconColorClass: 'text-green-500',
    category: 'pdf'
  },
  {
    id: 'pdf-summary',
    title: 'PDF文档摘要',
    description: '自动生成PDF文档的摘要内容，提取关键信息',
    icon: 'FileText',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconColorClass: 'text-green-500',
    category: 'pdf'
  },
  {
    id: 'excel-translate',
    title: 'Excel文档翻译',
    description: '将Excel文档翻译成其他语言，支持多种语言互译',
    icon: 'Languages',
    colorClass: 'bg-amber-50 dark:bg-amber-900/20',
    iconColorClass: 'text-amber-500',
    category: 'excel'
  },
  {
    id: 'pdf-to-word',
    title: 'PDF转Word',
    description: '将PDF文档转换为可编辑的Word文档格式',
    icon: 'FileText',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconColorClass: 'text-green-500',
    category: 'pdf'
  },
  {
    id: 'pdf24-tools',
    title: 'PDF24 Tools',
    description: 'PDF24 Tools 是一款免费的 PDF 工具，支持 PDF 编辑、PDF 合并、PDF 分割、PDF 压缩等功能，无任何限制地免费使用所有PDF24工具。',
    icon: 'FileCode',
    colorClass: 'bg-green-50 dark:bg-green-900/20',
    iconColorClass: 'text-green-500',
    category: 'pdf'
  },
];

export default docTools;
