import { IconName } from './iconMap';

export interface PremiumExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const premiumExamples: PremiumExample[] = [
  {
    id: "daily-premium-type",
    title: "查询今天险类保费",
    description: "查询今日各险种保费收入情况",
    message: "/保费快报 今天险类保费",
    icon: "FileSpreadsheet",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "daily-institution-premium",
    title: "查询今天机构保费",
    description: "查询今日各机构保费收入统计",
    message: "/保费快报 今天机构保费",
    icon: "Building",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "daily-auto-insurance-premium",
    title: "查询当日机构车险保费",
    description: "查询当日各机构车险保费收入明细",
    message: "/保费快报 当日机构车险保费",
    icon: "Car",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "daily-branch-premium",
    title: "查询当日中支公司保费",
    description: "查询当日中支公司保费收入情况",
    message: "/保费快报 当日中支公司保费",
    icon: "MapPin",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  }
];

export default premiumExamples;
