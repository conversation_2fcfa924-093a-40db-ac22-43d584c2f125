import { IconName } from './iconMap';

export interface ImageExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const imageExamples: ImageExample[] = [
  {
    id: "ocr-recognition",
    title: "OCR 文字识别",
    description: "从图像中识别并提取文字内容",
    message: "/ocr 请识别这张图片中的文字",
    icon: "FileText",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "image-summary",
    title: "图片内容总结",
    description: "智能分析并总结图像的主要内容",
    message: "/ocr 请总结这张图片的主要内容",
    icon: "FileImage",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "image-analysis",
    title: "图片内容分析",
    description: "详细分析图像中的元素和含义",
    message: "/ocr 请详细分析这张图片中的内容",
    icon: "Search",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  },
  {
    id: "image-extraction",
    title: "图片内容抽取",
    description: "从图像中抽取特定信息或元素",
    message: "/ocr 请从这张图片中抽取表格数据",
    icon: "Sparkles",
    colorClass: "bg-green-50 dark:bg-green-900/20",
    iconColorClass: "text-green-500"
  }
];

export default imageExamples; 