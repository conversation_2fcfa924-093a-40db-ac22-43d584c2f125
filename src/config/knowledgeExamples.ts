import { IconName } from './iconMap';

export interface KnowledgeExample {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
}

const knowledgeExamples: KnowledgeExample[] = [
  {
    id: "company-intro",
    title: "公司概况介绍",
    description: "查询公司简介、历史及发展情况",
    message: "/知识库 安盟保险简介和发展历程",
    icon: "FileText",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "branch-info",
    title: "分公司信息查询",
    description: "查询各分公司的基本情况",
    message: "/知识库 介绍四川省分公司",
    icon: "Search",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "business-scope",
    title: "业务范围查询",
    description: "了解公司主要业务和服务",
    message: "/知识库 安盟保险主要业务范围有哪些",
    icon: "FileSpreadsheet",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "tech-docs",
    title: "办公地址查询",
    description: "查询办公地址",
    message: "/知识库 安盟保险黑龙江分公司办公地址",
    icon: "FileCode",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "faq",
    title: "业务系统地址查询",
    description: "查询业务系统地址",
    message: "/知识库 新OA系统访问地址",
    icon: "MessageSquare",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  },
  {
    id: "employee-directory",
    title: "员工通讯录查询",
    description: "查询公司内部员工通讯录信息",
    message: "/通讯录 桂庆刚",
    icon: "User",
    colorClass: "bg-blue-50 dark:bg-blue-900/20",
    iconColorClass: "text-blue-500"
  }
];

export default knowledgeExamples; 