import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Toaster } from "sonner";
import ClientBody from "./ClientBody";
import WechatWorkAuthWrapper from "@/components/auth/WechatWorkAuthWrapper";

// 企业微信配置
const isWechatWorkEnabled = process.env.NEXT_PUBLIC_WECHAT_WORK_ENABLED === 'true';
const wechatWorkConfig = {
  corpId: process.env.NEXT_PUBLIC_WECHAT_WORK_CORPID || "",
  agentId: process.env.NEXT_PUBLIC_WECHAT_WORK_AGENTID || "",
  redirectUri: process.env.NEXT_PUBLIC_WECHAT_WORK_REDIRECT_URI,
};

// 优化字体配置，提高兼容性和可见性
const inter = Inter({
  subsets: ["latin"],
  display: "fallback", // 改为fallback确保文字立即可见
  fallback: [
    "system-ui",
    "-apple-system",
    "BlinkMacSystemFont",
    "Segoe UI",
    "Roboto",
    "Helvetica Neue",
    "Arial",
    "sans-serif"
  ], // 扩展后备字体列表
  preload: true, // 启用预加载提高性能
  weight: ["400", "500", "600", "700"],
  variable: "--font-inter", // 使用CSS变量
});

export const metadata: Metadata = {
  title: "MengChat办公助手",
  description: "支持会话管理、Markdown语法、图表展示和代码展示功能的聊天界面",
};



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        {/* iOS 16 兼容性 polyfills */}
        <script src="/js/polyfills.js" defer></script>
      </head>
      <body className={cn("min-h-screen bg-background", inter.variable)}>
        <ClientBody>
          {isWechatWorkEnabled ? (
            <WechatWorkAuthWrapper
              config={wechatWorkConfig}
              loadingComponent={
                <div className="flex items-center justify-center min-h-screen">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg">正在加载企业微信认证...</p>
                  </div>
                </div>
              }
            >
              {/* 使用客户端组件包装企业微信相关功能 */}
              {children}
            </WechatWorkAuthWrapper>
          ) : (
            /* 跳过企业微信认证，直接渲染子组件 */
            children
          )}
        </ClientBody>
        <Toaster position="top-right" />
      </body>
    </html>
  );
}
