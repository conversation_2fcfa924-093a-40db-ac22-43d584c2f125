"use client"

import ChatInterface from "@/components/chat/ChatInterface"
import WechatWorkUserInfoWrapper from "@/components/auth/WechatWorkUserInfoWrapper"
import { Bot, HelpCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import useTourGuide from "@/hooks/useTourGuide"
import TourGuide from "@/components/tour/TourGuide"

export default function Home() {
  const {
    isOpen: isTourOpen,
    steps: tourSteps,
    startTour,
    closeTour,
    completeTour
  } = useTourGuide()

  // 智能引导触发函数
  const handleTourTrigger = () => {
    // 通过检查DOM元素来判断当前显示的页面
    const aiToolsPanel = document.querySelector('[data-tour="ai-tools-panel"]') as HTMLElement
    const docProcessingPanel = document.querySelector('[data-tour="doc-processing-panel"]') as HTMLElement
    const visualizationPanel = document.querySelector('[data-tour="visualization-panel"]') as HTMLElement
    const chatMessages = document.querySelector('[data-tour="chat-messages"]') as HTMLElement
    const featureCards = document.querySelector('[data-tour="feature-cards"]') as HTMLElement

    if (aiToolsPanel && aiToolsPanel.offsetParent !== null) {
      // AI工具页面可见
      startTour('ai-tools')
    } else if (docProcessingPanel && docProcessingPanel.offsetParent !== null) {
      // 文档处理页面可见
      startTour('doc-processing')
    } else if (visualizationPanel && visualizationPanel.offsetParent !== null) {
      // 数据可视化页面可见
      startTour('visualization')
    } else if (featureCards && featureCards.offsetParent !== null) {
      // 首页功能卡片可见（空消息状态）- 也使用聊天引导
      startTour('chat')
    } else if (chatMessages && chatMessages.offsetParent !== null) {
      // 聊天消息可见（有消息状态）
      startTour('chat')
    } else {
      // 默认聊天引导（因为这是聊天应用的主要功能）
      startTour('chat')
    }
  }

  return (
    <main className="flex flex-col h-screen max-h-screen mx-auto overflow-hidden" data-tour="main">
      <header className="flex items-center p-2 md:p-4 border-b bg-background/80 backdrop-blur sticky top-0 z-10">
        <div className="hidden md:block w-40"></div>

        <h1 className="text-lg md:text-xl font-bold mx-auto md:flex-1 md:text-center flex items-center justify-center gap-2">
          <Bot className="h-6 w-6 md:h-7 md:w-7 text-blue-500" />
          <span className="gradient-text-safe gradient-text-fallback">
            MengChat 办公助手
          </span>
        </h1>

        <div className="hidden md:flex items-center gap-3 w-40 justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTourTrigger}
            className="flex items-center gap-2"
            id="header-tour-button"
          >
            <HelpCircle className="h-4 w-4" />
            <span className="hidden lg:inline">使用引导</span>
          </Button>
          <WechatWorkUserInfoWrapper />
        </div>

        {/* 移动端右侧 */}
        <div className="md:hidden flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTourTrigger}
            className="flex items-center gap-1"
            id="header-tour-button-mobile"
          >
            <HelpCircle className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <ChatInterface />

      {/* 引导组件 */}
      <TourGuide
        steps={tourSteps}
        isOpen={isTourOpen}
        onClose={closeTour}
        onComplete={completeTour}
      />
    </main>
  )
}
