@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 248 90% 66%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 248 90% 66%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 248 90% 66%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 248 90% 66%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    font-size: 14px;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
}

/* 渐变文字兼容性处理 */
@layer utilities {
  /* 安全的渐变文字类，带有后备颜色 */
  .gradient-text-safe {
    /* 后备颜色 - 在不支持渐变的浏览器中显示 */
    color: hsl(var(--primary));
    /* 渐变效果 - 在支持的浏览器中覆盖后备颜色 */
    background: linear-gradient(to right, #2563eb, #9333ea);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    /* 确保在某些情况下文字仍然可见 */
    text-shadow: none;
  }

  /* 针对不支持background-clip: text的浏览器 */
  @supports not (background-clip: text) or not (-webkit-background-clip: text) {
    .gradient-text-safe {
      color: #2563eb !important;
      background: none !important;
      -webkit-text-fill-color: initial !important;
    }
  }

  /* 针对企业微信等特殊环境的兼容性 */
  .gradient-text-fallback {
    color: #2563eb;
    font-weight: 600;
  }

  /* 针对可能的字体加载失败情况 */
  @font-face {
    font-family: 'Inter-fallback';
    src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont');
    font-display: block;
  }

  /* 确保在任何情况下文字都可见 */
  .gradient-text-safe.gradient-text-fallback {
    /* 双重保险：如果渐变失效，确保有可见的颜色 */
    color: #2563eb !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
}

/* 自定义滚动条样式 */
@layer utilities {
  /* 针对 Firefox */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  /* 针对 Chrome, Edge, and Safari */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 20px;
    border: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.5);
  }

  /* 代码块滚动条自定义样式 */
  .scrollbar-code {
    scrollbar-width: thin;
    scrollbar-color: #4d4d4d transparent;
  }

  .scrollbar-code::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-code::-webkit-scrollbar-track {
    background: #282c34;
  }

  .scrollbar-code::-webkit-scrollbar-thumb {
    background-color: #4d4d4d;
    border-radius: 20px;
    border: 2px solid #282c34;
  }

  .scrollbar-code::-webkit-scrollbar-thumb:hover {
    background-color: #6d6d6d;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

/* 自定义链接样式，确保在蓝色背景下链接清晰可见 */
.bg-blue-100 a, .dark .bg-blue-900 a {
  @apply text-blue-700 dark:text-blue-300 font-medium;
}

.bg-blue-100 a:hover, .dark .bg-blue-900 a:hover {
  @apply text-blue-800 dark:text-blue-200;
}
