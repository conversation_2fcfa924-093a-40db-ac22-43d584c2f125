import { NextRequest, NextResponse } from "next/server";

/**
 * 获取聊天历史记录
 */
export async function GET(req: NextRequest) {
  try {
    // 获取查询参数
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const sessionId = searchParams.get('session_id');
    const limit = searchParams.get('limit') || '50';

    // 验证必要参数
    if (!userId) {
      return NextResponse.json(
        { error: "缺少用户ID参数" },
        { status: 400 }
      );
    }

    // 调用后端API获取历史记录
    const historyApiUrl = process.env.CHAT_HISTORY_API_URL;

    // 验证API URL是否配置
    if (!historyApiUrl) {
      console.error('未配置CHAT_HISTORY_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    // 构建查询参数
    const queryParams = new URLSearchParams({
      user_id: userId,
      limit: limit
    });

    if (sessionId) {
      queryParams.append('session_id', sessionId);
    }

    const response = await fetch(`${historyApiUrl}?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CHAT_HISTORY_API_KEY || ''}`,
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端历史记录API错误:', errorText);
      return NextResponse.json(
        { error: '获取历史记录失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('处理历史记录请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
