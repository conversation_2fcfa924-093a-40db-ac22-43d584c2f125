import { NextRequest, NextResponse } from "next/server";

/**
 * 会话管理API - 创建和获取会话
 */

// GET - 获取用户所有会话列表
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const limit = searchParams.get('limit') || '50';

    // 验证必要参数
    if (!userId) {
      return NextResponse.json(
        { error: "缺少用户ID参数" },
        { status: 400 }
      );
    }

    // 调用后端API获取会话列表
    const conversationsApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATIONS_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!conversationsApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATIONS_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const queryParams = new URLSearchParams({
      user_id: userId,
      limit: limit
    });

    const response = await fetch(`${conversationsApiUrl}/conversations?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端会话API错误:', errorText);
      return NextResponse.json(
        { error: '获取会话列表失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data || []
    });

  } catch (error) {
    console.error('处理会话列表请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST - 创建新会话
export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const {
      user_id,
      title,
      session_id
    } = data;

    // 验证必要参数
    if (!user_id || !title || !session_id) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API创建会话
    const conversationsApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATIONS_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!conversationsApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATIONS_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const response = await fetch(`${conversationsApiUrl}/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      },
      body: JSON.stringify({
        user_id,
        title,
        session_id
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端创建会话API错误:', errorText);
      return NextResponse.json(
        { error: '创建会话失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('处理创建会话请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}