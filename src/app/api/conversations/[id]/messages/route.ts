import { NextRequest, NextResponse } from "next/server";

/**
 * 会话消息管理API - 获取和添加消息
 */

// GET - 获取特定会话的消息历史
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const { id: conversationId } = await params;
    const limit = searchParams.get('limit') || '50';
    const offset = searchParams.get('offset') || '0';

    // 验证必要参数
    if (!userId || !conversationId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API获取消息历史
    const messagesApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATION_MESSAGES_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!messagesApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATION_MESSAGES_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const queryParams = new URLSearchParams({
      user_id: userId,
      limit: limit,
      offset: offset
    });

    const response = await fetch(`${messagesApiUrl}/conversations/${conversationId}/messages?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端获取消息API错误:', errorText);
      return NextResponse.json(
        { error: '获取消息历史失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data || []
    });

  } catch (error) {
    console.error('处理获取消息历史请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST - 添加消息到会话
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const data = await req.json();
    const {
      user_id,
      role,
      content,
      thinking,
      process_time,
      attachments,
      timestamp
    } = data;
    const { id: conversationId } = await params;

    // 验证必要参数
    if (!user_id || !conversationId || !role || !content) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API添加消息
    const messagesApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATION_MESSAGES_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!messagesApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATION_MESSAGES_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const messageData = {
      user_id,
      role,
      content,
      thinking: thinking || null,
      process_time: process_time || 0,
      attachments: attachments || null,
      timestamp: timestamp || Date.now()
    };

    const response = await fetch(`${messagesApiUrl}/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      },
      body: JSON.stringify(messageData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端添加消息API错误:', errorText);
      return NextResponse.json(
        { error: '添加消息失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('处理添加消息请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}