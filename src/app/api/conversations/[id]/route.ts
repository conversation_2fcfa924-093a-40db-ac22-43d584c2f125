import { NextRequest, NextResponse } from "next/server";

/**
 * 单个会话管理API - 更新和删除会话
 */

// GET - 获取特定会话的详细信息
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const { id: conversationId } = await params;

    // 验证必要参数
    if (!userId || !conversationId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API获取会话详情
    const conversationsApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATIONS_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!conversationsApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATIONS_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const queryParams = new URLSearchParams({
      user_id: userId
    });

    const response = await fetch(`${conversationsApiUrl}/conversations/${conversationId}?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端获取会话详情API错误:', errorText);
      return NextResponse.json(
        { error: '获取会话详情失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('处理获取会话详情请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT - 更新会话信息（重命名、更新时间）
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const data = await req.json();
    const {
      user_id,
      title
    } = data;
    const { id: conversationId } = await params;

    // 验证必要参数
    if (!user_id || !conversationId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API更新会话
    const conversationsApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATIONS_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!conversationsApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATIONS_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const updateData: Record<string, unknown> = {
      user_id
    };

    if (title !== undefined) {
      updateData.title = title;
    }

    const response = await fetch(`${conversationsApiUrl}/conversations/${conversationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端更新会话API错误:', errorText);
      return NextResponse.json(
        { error: '更新会话失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('处理更新会话请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE - 删除会话
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('user_id');
    const { id: conversationId } = await params;

    // 验证必要参数
    if (!userId || !conversationId) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API删除会话
    const conversationsApiUrl = process.env.NEXT_PUBLIC_API_URL || process.env.CONVERSATIONS_API_URL;
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || process.env.CONVERSATIONS_API_KEY;

    if (!conversationsApiUrl) {
      console.error('未配置NEXT_PUBLIC_API_URL或CONVERSATIONS_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const queryParams = new URLSearchParams({
      user_id: userId
    });

    const response = await fetch(`${conversationsApiUrl}/conversations/${conversationId}?${queryParams.toString()}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey || ''}`,
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端删除会话API错误:', errorText);
      return NextResponse.json(
        { error: '删除会话失败' },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      message: '会话删除成功'
    });

  } catch (error) {
    console.error('处理删除会话请求异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}