import { NextRequest, NextResponse } from "next/server";

// 服务器端处理文件上传
export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const systemCode = formData.get('systemCode') as string;

    if (!file) {
      return NextResponse.json({ error: '没有提供文件' }, { status: 400 });
    }

    // 封装文件上传到外部服务器
    const uploadUrl = process.env.FILE_UPLOAD_URL;

    // 验证API URL是否配置
    if (!uploadUrl) {
      console.error('未配置FILE_UPLOAD_URL环境变量');
      return NextResponse.json({ error: '服务器配置错误' }, { status: 500 });
    }

    // 创建新的FormData对象发送到外部服务器
    const serverFormData = new FormData();
    serverFormData.append('file', file);
    serverFormData.append('systemCode', systemCode || 'AI');

    // 发送到外部服务器
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: serverFormData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('外部文件服务器错误:', errorText);
      return NextResponse.json({ error: '文件服务器错误' }, { status: 500 });
    }

    // 解析外部服务器响应
    const responseData = await response.json();

    // 返回结果给客户端
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('处理文件上传异常:', error);
    return NextResponse.json({ error: '处理上传文件时出错' }, { status: 500 });
  }
}

// 限制上传文件大小
export const config = {
  api: {
    bodyParser: false, // 禁用内置的bodyParser以支持文件上传
  },
};