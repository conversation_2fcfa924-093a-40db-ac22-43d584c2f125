import { NextRequest, NextResponse } from 'next/server';

// 获取API URL和API路径
const toolClickPath = process.env.TOOL_CLICK_API_URL || '';
const toolClickKey = process.env.TOOL_CLICK_API_KEY || '';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { user_id, tool_name, tool_type, click_time } = data;

    // 参数校验
    if (!user_id || !tool_name || !tool_type) {
      return NextResponse.json(
        { success: false, message: '参数不完整' },
        { status: 400 }
      );
    }

    // 调用后端API
    const response = await fetch(toolClickPath, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${toolClickKey}`
      },
      body: JSON.stringify({
        user_id,
        tool_name,
        tool_type,
        click_time: click_time || new Date().toISOString()
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('记录工具点击失败:', errorText);
      return NextResponse.json(
        { success: false, message: `记录工具点击失败: ${response.status}` },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error('记录工具点击异常:', error);
    return NextResponse.json(
      { success: false, message: `记录工具点击失败: ${error}` },
      { status: 500 }
    );
  }
}