import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const {
      user_id,
      message_content,
      response_content,
      message_type,
      status,
      error_message,
      process_time,
      environment,
      session_id,
      session_name
    } = data;

    // 验证必要参数
    if (!user_id) {
      return NextResponse.json(
        { error: "缺少用户ID参数" },
        { status: 400 }
      );
    }

    // 确保消息内容有值（即使是空消息也给一个默认值）
    // 优先使用前端传递的内容，即使它可能已经是URL地址了
    const safeMessageContent = message_content || "[空消息]";

    // 调用后端API保存对话记录
    const logApiUrl = process.env.CONVERSATION_LOG_API_URL;

    // 验证API URL是否配置
    if (!logApiUrl) {
      console.error('未配置CONVERSATION_LOG_API_URL环境变量');
      // 即使日志保存失败，也返回成功以避免影响主流程
      return NextResponse.json({
        success: false,
        message: "日志记录失败，但不影响主流程"
      });
    }

    const response = await fetch(logApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CONVERSATION_LOG_API_KEY || ''}`,
      },
      body: JSON.stringify({
        user_id,
        message_content: safeMessageContent,
        response_content,
        message_type: message_type || 'text',
        status: status === undefined ? 1 : status,
        error_message: error_message || '',
        process_time: process_time || 0,
        environment: environment || 'prd',
        session_id: session_id || '',
        session_name: session_name || '',
        timestamp: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端日志API错误:', errorText);
      // 即使日志保存失败，也返回成功以避免影响主流程
      return NextResponse.json({
        success: false,
        message: "日志记录失败，但不影响主流程"
      });
    }

    const result = await response.json();
    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('处理对话日志异常:', error);
    // 即使出现异常，也返回成功以避免影响主流程
    return NextResponse.json({
      success: false,
      message: "日志处理异常，但不影响主流程"
    });
  }
}