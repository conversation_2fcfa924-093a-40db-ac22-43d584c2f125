import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    const { userId, userQuestion, assistantAnswer } = data;

    if (!userId || !userQuestion || !assistantAnswer) {
      return NextResponse.json(
        { error: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 调用后端API发送点赞反馈
    const feedbackUrl = process.env.FEEDBACK_API_URL;

    // 验证API URL是否配置
    if (!feedbackUrl) {
      console.error('未配置FEEDBACK_API_URL环境变量');
      return NextResponse.json(
        { error: '服务器配置错误' },
        { status: 500 }
      );
    }

    const response = await fetch(`${feedbackUrl}/like`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.FEEDBACK_API_KEY || ''}`,
      },
      body: JSON.stringify({
        userId,
        userQuestion,
        assistantAnswer,
        timestamp: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('后端点赞API错误:', errorText);
      return NextResponse.json(
        { error: '点赞请求失败' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);

  } catch (error) {
    console.error('处理点赞异常:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}