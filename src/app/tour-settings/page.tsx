"use client"

import React from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import TourManager from '@/components/tour/TourManager'

export default function TourSettingsPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-background">
      {/* 头部导航 */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              返回
            </Button>
            <h1 className="text-2xl font-bold">引导功能设置</h1>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* 引导管理 */}
          <TourManager />

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">自动引导</h4>
                <p className="text-sm text-muted-foreground">
                  系统会在您首次访问时自动开始主要功能引导，帮助您快速了解系统的核心功能。
                  当您访问不同的功能模块（如AI工具、文档处理等）时，也会自动触发相应的引导。
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">手动引导</h4>
                <p className="text-sm text-muted-foreground">
                  您可以随时点击页面上的"功能引导"或"使用引导"按钮来重新开始引导。
                  也可以在上面的引导管理面板中选择特定的引导模块。
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">引导控制</h4>
                <p className="text-sm text-muted-foreground">
                  在引导过程中，您可以：
                </p>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1 ml-4">
                  <li>• 点击"下一步"继续引导</li>
                  <li>• 点击"上一步"回到前一个步骤</li>
                  <li>• 点击"跳过"跳过当前引导</li>
                  <li>• 点击"×"关闭引导</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">引导状态</h4>
                <p className="text-sm text-muted-foreground">
                  系统会自动记住您的引导进度，已完成的引导不会重复显示。
                  如果需要重新查看引导，可以使用"重置引导"功能。
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 技术信息 */}
          <Card>
            <CardHeader>
              <CardTitle>技术特性</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">响应式设计</h4>
                  <p className="text-sm text-muted-foreground">
                    引导系统完全适配桌面和移动设备，在不同屏幕尺寸下都能提供良好的体验。
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">智能定位</h4>
                  <p className="text-sm text-muted-foreground">
                    自动识别目标元素位置，智能调整提示框位置，确保引导内容始终可见。
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">状态持久化</h4>
                  <p className="text-sm text-muted-foreground">
                    引导进度保存在本地存储中，支持多用户独立状态管理。
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">模块化设计</h4>
                  <p className="text-sm text-muted-foreground">
                    支持不同功能模块的独立引导，可以根据用户当前使用的功能提供针对性指导。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
