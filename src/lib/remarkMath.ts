import { Plugin } from 'unified'
import { visit } from 'unist-util-visit'
import { Node } from 'unist'

interface TextNode extends Node {
  type: 'text'
  value: string
}

interface MathNode extends Node {
  type: 'math'
  value: string
  data?: {
    hName: string
    hProperties: {
      className: string[]
      'data-math': string
      'data-inline': string
    }
  }
}

// 数学公式标记的正则表达式
const INLINE_MATH_REGEX = /\$([^$\n]+?)\$/g  // 行内数学公式 $...$
const BLOCK_MATH_REGEX = /\$\$([\s\S]*?)\$\$/g // 块级数学公式 $$...$$ (支持多行)

const remarkMath: Plugin = () => {
  return (tree) => {
    visit(tree, 'text', (node: TextNode, index, parent) => {
      if (!node.value) return
      
      const { value } = node
      const newNodes: (TextNode | MathNode)[] = []
      let lastIndex = 0

      // 处理块级数学公式 $$...$$（优先处理，避免与行内公式冲突）
      const blockMatches = Array.from(value.matchAll(BLOCK_MATH_REGEX))
      
      // 只有在没有块级公式重叠的区域才处理行内公式
      const inlineMatches = Array.from(value.matchAll(INLINE_MATH_REGEX)).filter(inlineMatch => {
        const inlineStart = inlineMatch.index || 0
        const inlineEnd = inlineStart + inlineMatch[0].length
        
        return !blockMatches.some(blockMatch => {
          const blockStart = blockMatch.index || 0
          const blockEnd = blockStart + blockMatch[0].length
          return inlineStart >= blockStart && inlineEnd <= blockEnd
        })
      })
      
      // 合并所有匹配并按位置排序
      const allMatches = [
        ...blockMatches.map(match => ({ ...match, type: 'block' as const })),
        ...inlineMatches.map(match => ({ ...match, type: 'inline' as const }))
      ].sort((a, b) => (a.index || 0) - (b.index || 0))

      // 直接使用所有匹配（已经过滤过重叠）
      for (const match of allMatches) {
        const matchStart = match.index || 0
        const matchEnd = matchStart + match[0].length
        
        // 添加匹配前的文本
        if (matchStart > lastIndex) {
          const beforeText = value.slice(lastIndex, matchStart)
          if (beforeText) {
            newNodes.push({
              type: 'text',
              value: beforeText
            })
          }
        }

        // 添加数学节点
        const mathContent = match[1] // 获取括号内的内容
        newNodes.push({
          type: 'math',
          value: mathContent,
          data: {
            hName: 'span',
            hProperties: {
              className: ['math-node'],
              'data-math': mathContent,
              'data-inline': match.type === 'inline' ? 'true' : 'false'
            }
          }
        } as MathNode)

        lastIndex = matchEnd
      }

      // 添加剩余的文本
      if (lastIndex < value.length) {
        const remainingText = value.slice(lastIndex)
        if (remainingText) {
          newNodes.push({
            type: 'text',
            value: remainingText
          })
        }
      }

      // 如果有数学公式，替换原节点
      if (newNodes.length > 1 || (newNodes.length === 1 && newNodes[0].type === 'math')) {
        if (parent && typeof index === 'number' && 'children' in parent) {
          (parent as any).children.splice(index, 1, ...newNodes)
          return index + newNodes.length - 1
        }
      }
    })
  }
}

export default remarkMath