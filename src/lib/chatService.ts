/**
 * 聊天服务API - 处理聊天相关的API调用
 */

/**
 * 保存对话记录
 * @param userId 用户ID
 * @param messageContent 用户消息内容
 * @param responseContent 助手回答内容
 * @param messageType 消息类型(text/image/file)
 * @param status 处理状态(1成功/0失败)
 * @param errorMessage 错误信息
 * @param processTime 处理耗时(毫秒)
 * @param environment 环境(dev/prd)
 * @param sessionId 会话窗口ID
 * @param sessionName 会话名称
 * @param attachmentUrl 附件链接(仅在image/file类型时使用)
 * @returns 保存结果
 */
export async function saveConversationLog(
  userId: string,
  messageContent: string,
  responseContent: string,
  messageType: 'text' | 'image' | 'file' = 'text',
  status: 0 | 1 = 1,
  errorMessage: string = '',
  processTime: number = 0,
  environment: 'dev' | 'prd' = 'prd',
  sessionId: string = '',
  sessionName: string = '',
  attachmentUrl: string = ''
): Promise<boolean> {
  try {
    const response = await fetch('/api/chat/log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        message_content: messageContent,
        response_content: responseContent,
        message_type: messageType,
        status,
        error_message: errorMessage,
        process_time: processTime,
        environment,
        session_id: sessionId,
        session_name: sessionName,
        ...(attachmentUrl && (messageType === 'image' || messageType === 'file') && { attachment_url: attachmentUrl })
      }),
    });
    
    if (!response.ok) {
      console.error('保存对话记录失败:', await response.text());
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('保存对话记录异常:', error);
    return false;
  }
}

/**
 * 获取聊天历史记录
 * @param userId 用户ID
 * @param sessionId 会话ID
 * @param limit 限制条数
 * @returns 聊天历史记录
 */
export async function getChatHistory(
  userId: string,
  sessionId?: string,
  limit: number = 50
) {
  try {
    const queryParams = new URLSearchParams({
      user_id: userId,
      limit: limit.toString()
    });
    
    if (sessionId) {
      queryParams.append('session_id', sessionId);
    }
    
    const response = await fetch(`/api/chat/history?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (!response.ok) {
      console.error('获取聊天历史失败:', await response.text());
      return null;
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('获取聊天历史异常:', error);
    return null;
  }
}

/**
 * 对回答进行点赞
 * @param userId 用户ID
 * @param userQuestion 用户问题
 * @param assistantAnswer 助手回答
 * @returns 点赞结果
 */
export async function likeAnswer(
  userId: string, 
  userQuestion: string, 
  assistantAnswer: string
): Promise<boolean> {
  try {
    const response = await fetch('/api/feedback/like', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        userQuestion,
        assistantAnswer,
      }),
    });
    
    if (!response.ok) {
      console.error('点赞失败:', await response.text());
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('点赞请求异常:', error);
    return false;
  }
}

/**
 * 对回答进行点踩
 * @param userId 用户ID
 * @param userQuestion 用户问题
 * @param assistantAnswer 助手回答
 * @returns 点踩结果
 */
export async function dislikeAnswer(
  userId: string, 
  userQuestion: string, 
  assistantAnswer: string
): Promise<boolean> {
  try {
    const response = await fetch('/api/feedback/dislike', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        userQuestion,
        assistantAnswer,
      }),
    });
    
    if (!response.ok) {
      console.error('点踩失败:', await response.text());
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('点踩请求异常:', error);
    return false;
  }
}

/**
 * 记录用户工具点击信息
 * @param userId 用户ID
 * @param toolName 工具名称
 * @param toolType 工具类型
 * @returns 记录结果
 */
export async function recordToolClick(
  userId: string,
  toolName: string,
  toolType: string
): Promise<boolean> {
  try {
    const clickTime = new Date().toISOString();
    
    const response = await fetch('/api/tool/click', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: userId,
        tool_name: toolName,
        tool_type: toolType,
        click_time: clickTime
      }),
    });
    
    if (!response.ok) {
      console.error('记录工具点击失败:', await response.text());
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('记录工具点击异常:', error);
    return false;
  }
}
