import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用Navigator API (HTTPS)
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 回退到旧方法
      const textArea = document.createElement("textarea");
      textArea.value = text;
      
      // 将文本区域移到屏幕外
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      
      // 选择并复制
      textArea.focus();
      textArea.select();
      const success = document.execCommand("copy");
      
      // 清理
      document.body.removeChild(textArea);
      return success;
    }
  } catch (err) {
    console.error("复制到剪贴板失败:", err);
    return false;
  }
}
