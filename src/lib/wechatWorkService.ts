/**
 * 企业微信认证服务 - 基于企业微信JS-SDK
 * 参考文档: https://developer.work.weixin.qq.com/document/path/90514
 */

interface WechatWorkConfig {
  corpId: string;
  agentId: string;
  appSecret?: string;
  redirectUri?: string;
}

interface WechatWorkUserInfo {
  userId: string;
  name: string;
  avatar: string;
  department: number[];
  email?: string;
  mobile?: string;
  position?: string;
  gender?: string;
  [key: string]: any;
}

// 本地存储键名
const WECHAT_USER_KEY = 'wechat_work_user';
const WECHAT_TOKEN_KEY = 'wechat_work_token';

/**
 * 企业微信认证服务类
 */
export class WechatWorkService {
  private config: WechatWorkConfig;

  constructor(config: WechatWorkConfig) {
    this.config = config;
  }

  /**
   * 初始化SDK并检查用户认证状态
   */
  async init(): Promise<WechatWorkUserInfo | null> {
    // 检查本地缓存中是否有用户信息
    const cachedUser = this.getUserFromCache();
    if (cachedUser) {
      return cachedUser;  // 如果有缓存，直接返回，不继续认证流程
    }

    // 检查URL中是否已经有认证信息
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('userInfo') || urlParams.has('error')) {
      return null;  // 如果URL中有参数，不进行认证，让上层组件处理
    }

    // 如果没有缓存且URL中没有认证信息，才进行认证
    await this.loadWechatWorkScript();
    return await this.authenticate();
  }

  /**
   * 从本地缓存获取用户信息
   */
  getUserFromCache(): WechatWorkUserInfo | null {
    try {
      const userJson = localStorage.getItem(WECHAT_USER_KEY);
      if (userJson) {
        return JSON.parse(userJson);
      }
    } catch (error) {
      console.error('Failed to get user from cache', error);
    }
    return null;
  }

  /**
   * 存储用户信息到本地缓存
   */
  saveUserToCache(user: WechatWorkUserInfo): void {
    try {
      localStorage.setItem(WECHAT_USER_KEY, JSON.stringify(user));
    } catch (error) {
      console.error('Failed to save user to cache', error);
    }
  }

  /**
   * 清除本地缓存中的用户信息
   */
  clearCache(): void {
    localStorage.removeItem(WECHAT_USER_KEY);
    localStorage.removeItem(WECHAT_TOKEN_KEY);
  }

  /**
   * 加载企业微信JS-SDK脚本
   */
  private loadWechatWorkScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已加载
      if (window.wx) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.2.0.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load WeChat Work SDK'));
      document.head.appendChild(script);
    });
  }

  /**
   * 企业微信用户认证
   */
  private async authenticate(): Promise<WechatWorkUserInfo | null> {
    return new Promise((resolve, reject) => {
      // 确保wx对象已加载
      if (!window.wx) {
        reject(new Error('WeChat Work SDK not loaded'));
        return;
      }

      // 使用企业微信OAuth2认证
      const redirectUri = this.config.redirectUri || window.location.href;
      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.config.corpId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`;

      // 获取code参数
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');

      if (!code) {
        // 重定向到企业微信授权页面
        window.location.href = authUrl;
        return;
      }

      // 使用code从后端获取用户信息
      // 注意: 这里需要后端API支持，因为appSecret不应在前端暴露
      this.getUserInfoByCode(code)
        .then(userInfo => {
          this.saveUserToCache(userInfo);
          resolve(userInfo);
        })
        .catch(error => {
          console.error('Failed to get user info', error);
          reject(error);
        });
    });
  }

  /**
   * 通过code获取用户信息
   * 注意: 实际应用中应该由后端实现，这里仅作示例
   */
  private async getUserInfoByCode(code: string): Promise<WechatWorkUserInfo> {
    // 实际项目中，这里应该调用后端API，不应直接在前端使用appSecret
    // 这里仅作为示例，模拟API调用
    try {
      const response = await fetch(`/api/wechat-work/auth?code=${code}&corpId=${this.config.corpId}&agentId=${this.config.agentId}`);
      if (!response.ok) {
        throw new Error('Failed to authenticate with WeChat Work');
      }
      return await response.json();
    } catch (error) {
      console.error('Error authenticating with WeChat Work', error);
      throw error;
    }
  }
}

// 添加全局类型声明
declare global {
  interface Window {
    wx: any;
  }
}

// 创建默认实例
let wechatWorkService: WechatWorkService | null = null;

/**
 * 初始化企业微信服务
 */
export function initWechatWorkService(config: WechatWorkConfig): WechatWorkService {
  wechatWorkService = new WechatWorkService(config);
  return wechatWorkService;
}

/**
 * 获取企业微信服务实例
 */
export function getWechatWorkService(): WechatWorkService {
  if (!wechatWorkService) {
    throw new Error('WechatWorkService not initialized. Call initWechatWorkService first.');
  }
  return wechatWorkService;
}

export default {
  initWechatWorkService,
  getWechatWorkService
};