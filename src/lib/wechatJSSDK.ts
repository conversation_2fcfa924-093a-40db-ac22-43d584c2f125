/**
 * 企业微信JS-SDK工具类
 *
 * 统一封装企业微信JS-SDK的所有功能，包括：
 * 1. 脚本加载
 * 2. 配置初始化
 * 3. 常用API调用（showToast, showModal等）
 * 4. 系统浏览器打开链接
 */

// 全局状态，跟踪配置获取尝试状态
let globalConfigFailure = false;

// 默认配置
const DEFAULT_CONFIG = {
  debug: false,
  beta: true,
  jsApiList: ['checkJsApi', 'showToast', 'showModal', 'openDefaultBrowser'],
  backendUrl: '/wx_config',
};

// 类型定义
export interface WxConfigOptions {
  debug?: boolean;
  beta?: boolean;
  jsApiList?: string[];
  backendUrl?: string;
  onReady?: () => void;
  onError?: (error: Error) => void;
  skipConfigOnFailure?: boolean;
  autoInit?: boolean;
}

export interface ToastOptions {
  title: string;
  icon?: 'success' | 'loading' | 'none' | 'error';
  duration?: number;
  position?: 'top' | 'bottom' | 'middle';
  mask?: boolean;
  success?: () => void;
  fail?: (error: any) => void;
}

export interface ModalOptions {
  title: string;
  content: string;
  showCancel?: boolean;
  cancelText?: string;
  cancelColor?: string;
  confirmText?: string;
  confirmColor?: string;
  success?: (res: { confirm: boolean; cancel: boolean }) => void;
  fail?: (error: any) => void;
}

/**
 * 企业微信JS-SDK工具类
 */
class WechatJSSDK {
  private static instance: WechatJSSDK;
  private isReady: boolean = false;
  private isLoading: boolean = false;
  private scriptLoaded: boolean = false;
  private error: Error | null = null;
  private options: WxConfigOptions;

  /**
   * 私有构造函数，使用单例模式
   */
  private constructor(options: WxConfigOptions = {}) {
    this.options = {
      ...DEFAULT_CONFIG,
      ...options
    };
  }

  /**
   * 获取单例实例
   */
  public static getInstance(options?: WxConfigOptions): WechatJSSDK {
    if (!WechatJSSDK.instance) {
      WechatJSSDK.instance = new WechatJSSDK(options);
    } else if (options) {
      // 更新选项
      WechatJSSDK.instance.options = {
        ...WechatJSSDK.instance.options,
        ...options
      };
    }
    return WechatJSSDK.instance;
  }

  /**
   * 检测当前是否在企业微信环境中
   */
  public isWechatWorkEnv(): boolean {
    if (typeof window === 'undefined') return false;
    const ua = navigator.userAgent.toLowerCase();
    return /wxwork/.test(ua);
  }

  /**
   * 检测当前是否在微信环境中（包括企业微信和普通微信）
   */
  public isWechatEnv(): boolean {
    if (typeof window === 'undefined') return false;
    const ua = navigator.userAgent.toLowerCase();
    return /micromessenger/.test(ua) || /wxwork/.test(ua);
  }

  /**
   * 获取完整的用户代理字符串
   */
  public getUserAgent(): string {
    if (typeof window === 'undefined') return '';
    return navigator.userAgent;
  }

  /**
   * 判断URL是否为有效URL
   */
  public isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * 重置全局配置失败状态
   */
  public resetConfigFailure(): void {
    globalConfigFailure = false;
  }

  /**
   * 获取全局配置失败状态
   */
  public getConfigFailureStatus(): boolean {
    return globalConfigFailure;
  }

  /**
   * 加载企业微信JS-SDK脚本
   */
  private loadWechatScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果已经加载过了，直接返回
      if (typeof window !== 'undefined' && (window.wx || this.scriptLoaded)) {
        resolve();
        return;
      }

      if (typeof document === 'undefined') {
        reject(new Error('document is not defined'));
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.2.6.js';

      script.onload = () => {
        this.scriptLoaded = true;
        resolve();
      };

      script.onerror = () => {
        const error = new Error('Failed to load WeChat Work JS-SDK');
        this.error = error;
        reject(error);
      };

      document.head.appendChild(script);
    });
  }

  /**
   * 配置企业微信JS-SDK
   */
  public async configWx(customOptions: WxConfigOptions = {}): Promise<boolean> {
    try {
      // 获取合并后的选项
      const mergedOptions = {
        ...this.options,
        ...customOptions
      };

      // 如果之前获取配置失败过且启用了失败跳过，则不再尝试获取
      if (mergedOptions.skipConfigOnFailure && globalConfigFailure) {
        console.warn('JS-SDK配置已曾经失败，跳过配置');
        return false;
      }

      this.isLoading = true;
      this.error = null;

      // 确保JS-SDK脚本已加载
      await this.loadWechatScript();

      if (typeof window === 'undefined' || !window.wx) {
        throw new Error('企业微信JS-SDK未加载');
      }

      // 获取当前URL（去除hash部分）
      const url = window.location.href.split('#')[0];

      try {
        // 从后端获取配置
        const response = await fetch(`${mergedOptions.backendUrl}?url=${encodeURIComponent(url)}`);
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`获取JS-SDK配置失败: ${errorText}`);
        }

        // 解析配置信息
        const config = await response.json();

        // 配置JS-SDK
        window.wx.config({
          beta: mergedOptions.beta,
          debug: mergedOptions.debug,
          appId: config.appId,
          timestamp: config.timestamp,
          nonceStr: config.nonceStr,
          signature: config.signature,
          jsApiList: mergedOptions.jsApiList || config.jsApiList
        });

        // 监听ready事件
        return new Promise<boolean>(resolve => {
          window.wx.ready(() => {
            this.isReady = true;
            this.isLoading = false;
            mergedOptions.onReady?.();
            resolve(true);
          });

          window.wx.error((res: any) => {
            const errorMsg = `JS-SDK配置失败: ${res.errMsg || JSON.stringify(res)}`;
            const error = new Error(errorMsg);
            this.error = error;
            this.isLoading = false;
            mergedOptions.onError?.(error);
            console.error(errorMsg);

            // 标记全局失败状态
            if (mergedOptions.skipConfigOnFailure) {
              globalConfigFailure = true;
            }

            resolve(false);
          });
        });
      } catch (err) {
        // 后端请求失败，标记全局失败状态
        if (mergedOptions.skipConfigOnFailure) {
          globalConfigFailure = true;
        }
        throw err;
      }
    } catch (error) {
      this.isLoading = false;
      this.error = error instanceof Error ? error : new Error(String(error));
      console.error('配置JS-SDK失败:', this.error);
      return false;
    }
  }

  /**
   * 通用API调用方法
   */
  public invokeApi<T = any>(apiName: string, params: any = {}): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      if (typeof window === 'undefined' || !window.wx) {
        reject(new Error('企业微信JS-SDK未加载'));
        return;
      }

      if (!this.isReady) {
        reject(new Error('企业微信JS-SDK未初始化'));
        return;
      }

      if (!window.wx[apiName]) {
        reject(new Error(`企业微信JS-SDK不支持${apiName}接口`));
        return;
      }

      // 添加通用的成功/失败回调
      const apiParams = {
        ...params,
        success: (res: any) => {
          params.success?.(res);
          resolve(res);
        },
        fail: (res: any) => {
          const error = new Error(`${apiName}调用失败: ${res.errMsg}`);
          params.fail?.(res);
          reject(error);
        }
      };

      // 调用API
      window.wx[apiName](apiParams);
    });
  }

  /**
   * 显示消息提示框
   */
  public showToast(options: ToastOptions): Promise<void> {
    return this.invokeApi('showToast', {
      title: options.title,
      icon: options.icon || 'success',
      duration: options.duration || 1500,
      position: options.position || 'middle',
      mask: options.mask !== undefined ? options.mask : false,
      success: options.success,
      fail: options.fail
    });
  }

  /**
   * 显示模态对话框
   */
  public showModal(options: ModalOptions): Promise<{ confirm: boolean; cancel: boolean }> {
    return this.invokeApi('showModal', {
      title: options.title,
      content: options.content,
      showCancel: options.showCancel !== undefined ? options.showCancel : true,
      cancelText: options.cancelText || '取消',
      cancelColor: options.cancelColor || '#000000',
      confirmText: options.confirmText || '确定',
      confirmColor: options.confirmColor || '#3CC51F',
      success: options.success,
      fail: options.fail
    });
  }

  /**
   * 在系统默认浏览器中打开链接
   */
  public openInDefaultBrowser(url: string): Promise<boolean> {
    if (typeof window === 'undefined') return Promise.resolve(false);

    // 检查企业微信环境
    if (!this.isWechatWorkEnv()) {
      console.warn('当前不在企业微信环境，无法使用openDefaultBrowser接口');
      // 使用普通方式打开
      window.open(url, '_blank');
      return Promise.resolve(false);
    }

    // 检查JS-SDK是否已加载
    if (!window.wx) {
      console.error('企业微信JS-SDK未加载，无法使用openDefaultBrowser接口');
      window.open(url, '_blank');
      return Promise.resolve(false);
    }

    // 检查全局失败状态，如果之前失败过，直接使用普通方式打开
    if (globalConfigFailure) {
      console.warn('JS-SDK配置曾经失败，跳过SDK调用，使用普通方式打开链接');
      window.open(url, '_blank');
      return Promise.resolve(false);
    }

    return this.invokeApi<boolean>('openDefaultBrowser', {
      url: url,
      success: () => true,
      fail: (res: any) => {
        console.error('打开系统浏览器失败:', res.errMsg);
        // 失败时尝试使用普通方式打开
        window.open(url, '_blank');
        return false;
      }
    });
  }

  /**
   * 在外部浏览器中打开链接的通用处理函数
   * 可用于onClick事件处理
   */
  public handleExternalLink(url: string, e?: React.MouseEvent<HTMLAnchorElement>): void {
    // 如果有事件对象，阻止默认行为
    if (e) {
      e.preventDefault();
    }

    // 如果已知配置失败，直接使用普通方式打开
    if (globalConfigFailure) {
      window.open(url, '_blank');
      return;
    }

    // 尝试在系统浏览器中打开
    this.openInDefaultBrowser(url).catch(() => {
      // 出错时使用普通方式打开
      window.open(url, '_blank');
    });
  }

  /**
   * 获取SDK准备状态
   */
  public getReadyState(): boolean {
    return this.isReady;
  }

  /**
   * 获取SDK加载状态
   */
  public getLoadingState(): boolean {
    return this.isLoading;
  }

  /**
   * 获取错误信息
   */
  public getError(): Error | null {
    return this.error;
  }
}

// 添加全局类型声明
declare global {
  interface Window {
    wx: any;
  }
}

// 导出单例实例
export default WechatJSSDK.getInstance();
