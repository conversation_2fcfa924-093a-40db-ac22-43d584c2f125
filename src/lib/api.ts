/**
 * 上传文件响应类型
 */
export interface UploadFileResponse {
  /** 显示用的图像URL（解码后的URL） */
  displayUrl: string;
  /** 图片ID，用于发送给API */
  imageId?: string;
}

/**
 * 上传文件到服务器
 * @param file 要上传的文件
 * @returns 上传成功后的文件信息，包含显示URL和图片ID
 */
export async function uploadFile(file: File): Promise<UploadFileResponse> {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('systemCode', 'AI');

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/image/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      console.error('上传失败:', await response.text());
      return { displayUrl: '' };
    }

    const data = await response.json();
    
    // 处理新的返回格式，从imageId解码URL
    if (data && data.success && data.image_id) {
      const imageId = data.image_id;
      
      // 从imageId解码获取URL
      const decodedUrl = atob(imageId);
      
      console.log('获取到图片ID:', imageId);
      
      return { 
        displayUrl: decodedUrl, 
        imageId 
      };
    }
    
    // 兼容旧格式
    if (data && data.data && data.data.imgUrl) {
      return { 
        displayUrl: data.data.imgUrl 
      };
    }

    console.warn('文件服务器未返回有效数据:', data);
    return { displayUrl: '' };
  } catch (error) {
    console.error('上传文件异常:', error);
    return { displayUrl: '' };
  }
}