/**
 * API服务 - 用于处理聊天消息
 */

import { MessageType } from "@/components/chat/ChatInterface";

// 配置信息从环境变量获取
const config = {
  API_URL: process.env.NEXT_PUBLIC_API_URL || "",  // 从环境变量获取 API URL
  API_KEY: process.env.NEXT_PUBLIC_API_KEY || ""   // 从环境变量获取 API KEY
};

export interface ChatResponse {
  content: string;
  thinking?: string;
}

export class ChatService {
  public apiUrl: string;
  public apiKey: string;
  private chatIds: Map<string, string> = new Map(); // 存储会话ID映射

  constructor() {
    // 使用环境变量中的配置
    this.apiUrl = config.API_URL;
    this.apiKey = config.API_KEY;

    // 尝试从本地存储中加载配置
    if (typeof localStorage !== 'undefined') {
      const savedApiUrl = localStorage.getItem("chatApiUrl");
      const savedApiKey = localStorage.getItem("chatApiKey");

      if (savedApiUrl) this.apiUrl = savedApiUrl;
      if (savedApiKey) this.apiKey = savedApiKey;

      console.log(`从本地存储加载的API配置 - URL存在: ${Boolean(savedApiUrl)}, Key存在: ${Boolean(savedApiKey)}`);
    }

    console.log(`最终API配置 - URL存在: ${Boolean(this.apiUrl)}, Key存在: ${Boolean(this.apiKey)}`);
  }

  /**
   * 获取或创建会话ID
   * @param conversationId 会话ID
   * @returns 持久化的chatId
   */
  getChatId(conversationId: string): string {
    if (!this.chatIds.has(conversationId)) {
      // 创建新的chatId (userId + uuid格式)
      const cachedUser = localStorage.getItem('wechat_work_user');
      const userId = cachedUser ? JSON.parse(cachedUser).userId : '';
      const uuid = crypto.randomUUID ? crypto.randomUUID() : Date.now().toString(36);
      const chatId = `${userId}_${uuid}`;
      this.chatIds.set(conversationId, chatId);
    }
    return this.chatIds.get(conversationId)!;
  }

  /**
   * 处理消息并获取回复（非流式）
   * @param conversationId 会话ID
   * @param message 消息内容
   * @returns 聊天回复
   */
  async processMessage(conversationId: string, message: MessageType, modelTypeCalled?: string): Promise<ChatResponse> {
    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${this.apiKey}`
    };

    const chatId = this.getChatId(conversationId);

    // 构建消息内容数组
    const messageContent: any[] = [];

    // 只有当消息内容不为空时才添加文本内容
    if (message.content && message.content.trim() !== '') {
      messageContent.push({
        type: "text",
        text: message.content
      });
    }

    // 添加附件
    if (message.attachments && message.attachments.length > 0) {
      message.attachments.forEach(attachment => {
        if (attachment.type.startsWith('image/')) {
          // 图片附件
          messageContent.push({
            type: "image_url",
            image_url: {
              // 如果有imageId，优先使用它，否则使用url
              url: attachment.imageId || attachment.url
            }
          });
        } else {
          // 文档附件
          messageContent.push({
            type: "file_url",
            name: attachment.name,
            url: attachment.url
          });
        }
      });
    }

    const data = {
      "chatId": chatId,
      "stream": false,
      "detail": false,
      "messages": [
        {
          role: message.role,
          content: messageContent
        }
      ],
      "variables": {
        userId: chatId.split('_')[0],
        modelTypeCalled: modelTypeCalled || 'speed',
        // ... other variables
      }
    };

    try {
      // 检查API URL是否有效
      if (!this.apiUrl) {
        throw new Error('未配置API URL，请在设置中配置API地址');
      }

      // 确保API URL格式正确
      let apiUrl = this.apiUrl;
      if (apiUrl.endsWith('/')) {
        apiUrl = apiUrl.slice(0, -1); // 移除末尾的斜杠
      }

      const fullUrl = `${apiUrl}/v1/chat/completions`;

      console.log("Sending request to Chat API:", data);
      console.log(`完整请求URL: ${fullUrl}`);

      const response = await fetch(fullUrl, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(data),
        // 120秒超时
        signal: AbortSignal.timeout(120000)
      });

      if (!response.ok) {
        throw new Error(`API错误: ${response.status}`);
      }

      const result = await response.json();

      // 解析响应
      const content = result.choices?.[0]?.message?.content?.trim() || "";
      console.log("Received response from Chat API:", content);

      return { content };
    } catch (error) {
      console.error("API请求错误:", error);
      return { content: "服务器繁忙，请稍后再试。" };
    }
  }

  /**
   * 检查网络连接状态
   * @returns 网络是否连接正常
   */
  private checkNetworkConnection(): boolean {
    if (typeof navigator !== 'undefined') {
      // @ts-ignore - 某些浏览器可能不支持navigator.onLine
      return navigator.onLine !== false;
    }
    return true; // 默认假设网络连接正常
  }

  /**
   * 获取设备信息
   * @returns 设备信息对象
   */
  private getDeviceInfo() {
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
    return {
      isMobile,
      screenWidth: typeof window !== 'undefined' ? window.innerWidth : 'unknown',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      platform: typeof navigator !== 'undefined' ? navigator.platform : 'unknown',
      networkType: typeof navigator !== 'undefined' && 'connection' in navigator
        // @ts-ignore - 某些浏览器可能不支持navigator.connection
        ? navigator.connection?.effectiveType || 'unknown'
        : 'unknown'
    };
  }

  /**
   * 处理消息并获取流式回复
   * @param conversationId 会话ID
   * @param message 消息内容
   * @param onChunk 处理每个消息块的回调函数
   * @param modelTypeCalled 模型类型
   * @param abortSignal 中断信号
   */
  async processMessageStream(
    conversationId: string,
    message: MessageType,
    onChunk: (chunk: string) => void,
    modelTypeCalled?: string,
    abortSignal?: AbortSignal
  ): Promise<void> {
    // 获取设备信息
    const deviceInfo = this.getDeviceInfo();
    const isMobile = deviceInfo.isMobile;

    console.log(`设备类型: ${isMobile ? '移动设备' : '桌面设备'}, 屏幕宽度: ${deviceInfo.screenWidth}`);
    console.log(`API URL: ${this.apiUrl}`);
    console.log(`API Key 已配置: ${Boolean(this.apiKey)}`);
    console.log(`网络类型: ${deviceInfo.networkType}`);

    // 检查网络连接
    if (!this.checkNetworkConnection()) {
      console.error("网络连接不可用");
      onChunk("\n\n网络连接不可用，请检查您的网络设置并重试。");
      return;
    }

    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${this.apiKey}`,
      // 添加额外的请求头以帮助调试
      "X-Client-Platform": deviceInfo.platform,
      "X-Client-Type": isMobile ? "mobile" : "desktop"
    };

    const chatId = this.getChatId(conversationId);

    // 构建消息内容数组
    const messageContent: any[] = [];

    // 只有当消息内容不为空时才添加文本内容
    if (message.content && message.content.trim() !== '') {
      messageContent.push({
        type: "text",
        text: message.content
      });
    }

    // 添加附件
    if (message.attachments && message.attachments.length > 0) {
      message.attachments.forEach(attachment => {
        if (attachment.type.startsWith('image/')) {
          // 图片附件
          messageContent.push({
            type: "image_url",
            image_url: {
              // 如果有imageId，优先使用它，否则使用url
              url: attachment.imageId || attachment.url
            }
          });
        } else {
          // 文档附件
          messageContent.push({
            type: "file_url",
            name: attachment.name,
            url: attachment.url
          });
        }
      });
    }

    const data = {
      "chatId": chatId,
      "stream": true,
      "detail": true,
      "messages": [
        {
          role: message.role,
          content: messageContent
        }
      ],
      "variables": {
        userId: chatId.split('_')[0],
        modelTypeCalled: modelTypeCalled || 'speed',
        // ... other variables
      }
    };

    // 重试计数器
    let retryCount = 0;
    const maxRetries = isMobile ? 2 : 1; // 移动设备多一次重试机会

    const attemptFetch = async (): Promise<Response> => {
      try {
        // 详细记录请求信息
        // 检查API URL是否有效
        if (!this.apiUrl) {
          throw new Error('未配置API URL，请在设置中配置API地址');
        }

        // 确保API URL格式正确
        let apiUrl = this.apiUrl;
        if (apiUrl.endsWith('/')) {
          apiUrl = apiUrl.slice(0, -1); // 移除末尾的斜杠
        }

        const fullUrl = `${apiUrl}/v1/chat/completions`;

        console.log("Sending streaming request to Chat API:", data);
        console.log(`完整请求URL: ${fullUrl}`);
        console.log("请求头:", JSON.stringify(headers, null, 2));
        console.log("请求体:", JSON.stringify(data, null, 2));

        // 调整超时时间为120秒
        const timeoutMs = 120000; // 统一设置为120秒

        // 创建请求选项
        const fetchOptions: RequestInit = {
          method: "POST",
          headers: headers,
          body: JSON.stringify(data),
          signal: abortSignal || AbortSignal.timeout(timeoutMs),
          // 添加额外的fetch选项以提高可靠性
          cache: 'no-store',
          credentials: 'same-origin',
          mode: 'cors',
          redirect: 'follow'
        };

        const response = await fetch(fullUrl, fetchOptions);

        console.log(`响应状态: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API错误详情: 状态码=${response.status}, 响应内容=${errorText}`);
          throw new Error(`API错误: ${response.status} - ${errorText}`);
        }

        return response;
      } catch (error) {
        // 如果还有重试次数，则重试
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`请求失败，正在进行第${retryCount}次重试...`);

          // 通知用户正在重试
          onChunk(`\n\n网络请求失败，正在进行第${retryCount}次重试...`);

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
          return attemptFetch();
        }
        throw error;
      }
    };

    try {
      const response = await attemptFetch();

      // 处理流式响应
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法读取响应流");
      }

      const decoder = new TextDecoder('utf-8');
      let buffer = '';

      // 读取流
      const processResult = async () => {
        let buffer = '';
        
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Process any remaining buffer content
            if (buffer.trim()) {
              processLine(buffer.trim());
            }
            console.log('Stream complete');
            return;
          }

          // Decode and add to buffer
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Split on double newlines which separate SSE events
          const events = buffer.split('\n\n');
          
          // Process all complete events
          for (let i = 0; i < events.length - 1; i++) {
            const event = events[i].trim();
            if (event) {
              processLine(event);
            }
          }

          // Keep the last potentially incomplete event in buffer
          buffer = events[events.length - 1];
        }
      };

      // Helper function to process a single SSE event
      const processLine = (line: string) => {
        // Skip [DONE] events
        if (line === 'data: [DONE]') {
          console.log('Stream complete with [DONE] marker');
          return;
        }

        // Extract the data part if it exists
        const match = line.match(/^data: (.+)$/m);
        if (!match) {
          console.warn('Invalid SSE format:', line);
          return;
        }

        try {
          const jsonData = JSON.parse(match[1]);
          
          // 检查是否包含状态和名称信息
          if (jsonData.status === "running" && jsonData.name) {
            // 发送一个特殊格式的消息，包含状态和名称
            onChunk(`__STATUS_NAME__${jsonData.name}__`);
            return;
          }
          
          if (jsonData.choices?.[0]?.delta?.content !== undefined) {
            onChunk(jsonData.choices[0].delta.content);
          }
        } catch (error) {
          console.error('Failed to parse SSE data:', error, 'Line:', line);
        }
      };

      await processResult();
    } catch (error) {
      // 详细记录错误信息
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace';
      console.error("流式API请求错误:", errorMessage);
      console.error("错误堆栈:", errorStack);

      // 获取并记录详细的设备和网络信息
      const deviceInfo = this.getDeviceInfo();
      console.error("设备信息:", deviceInfo);

      // 检查网络状态
      const isOnline = this.checkNetworkConnection();
      console.error("网络状态:", isOnline ? "在线" : "离线");

      // 根据错误类型和设备信息提供更具体的错误消息
      let errorResponse = "\n\n";

      if (!isOnline) {
        // 网络离线情况
        errorResponse += "您的设备当前处于离线状态，请检查网络连接后重试。";
      } else if (errorMessage.includes('Failed to fetch') || errorMessage.includes('NetworkError')) {
        // 网络连接错误
        if (deviceInfo.isMobile) {
          errorResponse += "移动网络连接不稳定，请尝试切换到WiFi网络或移动到信号更好的位置后重试。";
        } else {
          errorResponse += "网络连接错误，请检查您的网络连接并重试。";
        }
      } else if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
        // 超时错误
        if (deviceInfo.isMobile) {
          errorResponse += "移动网络响应超时，请检查您的网络信号强度或切换到更稳定的网络后重试。";
        } else {
          errorResponse += "请求超时，服务器响应时间过长，请稍后重试。";
        }
      } else if (errorMessage.includes('CORS') || errorMessage.includes('cross-origin')) {
        errorResponse += "跨域请求错误，请联系管理员检查API配置。";
      } else if (errorMessage.includes('AbortError') || errorMessage.includes('BodyStreamBuffer was aborted')) {
        // 请求被中断 - 不输出任何错误信息，因为这是用户主动操作
        console.log('流式请求被用户中断');
        return;
      } else {
        // 其他错误
        errorResponse += "服务器连接错误，请稍后再试。";

        // 只在非生产环境或开发模式下显示详细错误
        if (process.env.NODE_ENV !== 'production') {
          errorResponse += "\n错误详情: " + errorMessage;
        }
      }

      // 添加移动设备特定的建议
      if (deviceInfo.isMobile) {
        errorResponse += "\n\n移动设备使用提示：\n1. 确保您的网络信号良好\n2. 尝试切换到WiFi网络\n3. 关闭并重新打开应用后再试";
      }

      onChunk(errorResponse);
    }
  }

  /**
   * 根据对话内容生成会话标题
   * @param userMessage, 用户的第一条消息
   * @returns 生成的标题
   */
  async generateConversationTitle(userMessage: string): Promise<string> {
    if (!this.apiUrl || !this.apiKey) {
      // 如果API未配置，返回从消息中提取的简短标题
      return this.extractTitleFromMessage(userMessage);
    }

    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${this.apiKey}`
    };

    const data = {
      "chatId": `title_${Date.now()}`,
      "stream": false,
      "messages": [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "根据用户的输入，生成一个不超过10个字符的简短会话标题。直接返回标题内容，不要加引号或其他任何字符。下面是用户的输入：" + (userMessage ? userMessage.slice(0, 150) : "新对话")
            }
          ]
        }
      ]
    };

    try {
      // 检查API URL是否有效
      if (!this.apiUrl) {
        throw new Error('未配置API URL，请在设置中配置API地址');
      }

      // 确保API URL格式正确
      let apiUrl = this.apiUrl;
      if (apiUrl.endsWith('/')) {
        apiUrl = apiUrl.slice(0, -1); // 移除末尾的斜杠
      }

      const fullUrl = `${apiUrl}/v1/chat/completions`;
      console.log(`生成标题API请求URL: ${fullUrl}`);

      const response = await fetch(fullUrl, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(data),
        signal: AbortSignal.timeout(30000) // 生成标题使用30秒超时
      });

      if (!response.ok) {
        throw new Error(`API错误: ${response.status}`);
      }

      const result = await response.json();
      let title = result.choices?.[0]?.message?.content?.trim() || "";

      // 确保标题不超过10个字符
      if (title.length > 10) {
        title = title.substring(0, 10);
      }

      return title || this.extractTitleFromMessage(userMessage);
    } catch (error) {
      console.error("生成标题错误:", error);
      // 如果API调用失败，使用备选方法
      return this.extractTitleFromMessage(userMessage);
    }
  }

  /**
   * 从消息中提取简短标题（备选方法）
   */
  private extractTitleFromMessage(message: string): string {
    // 移除特殊字符和多余空格
    const cleanedMessage = message.replace(/[^\w\s\u4e00-\u9fa5]/g, ' ').replace(/\s+/g, ' ').trim();

    // 提取前10个字符，如果消息长度不足则全部返回
    const shortTitle = cleanedMessage.length > 10
      ? cleanedMessage.substring(0, 10)
      : cleanedMessage;

    return shortTitle || "新对话";
  }

  /**
   * 中断聊天会话
   * @param conversationId 会话ID
   * @returns 是否成功中断
   */
  async abortChatCompletion(conversationId: string): Promise<boolean> {
    try {
      const chatId = this.getChatId(conversationId);
      
      // 如果没有API URL，跳过后端中断请求
      if (!this.apiUrl) {
        console.warn('未配置API URL，跳过后端中断请求');
        return false;
      }

      const headers = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      };

      // 确保API URL格式正确
      let apiUrl = this.apiUrl;
      if (apiUrl.endsWith('/')) {
        apiUrl = apiUrl.slice(0, -1); // 移除末尾的斜杠
      }

      const fullUrl = `${apiUrl}/v1/chat/abort?chat_id=${encodeURIComponent(chatId)}`;
      console.log(`中断请求URL: ${fullUrl}`);

      const response = await fetch(fullUrl, {
        method: "POST",
        headers: headers,
        signal: AbortSignal.timeout(5000) // 中断请求使用5秒超时
      });

      if (!response.ok) {
        console.error(`中断请求失败: ${response.status}`);
        return false;
      }

      const result = await response.json();
      console.log('中断请求响应:', result);
      
      return result.success === true;
    } catch (error) {
      console.error("中断请求错误:", error);
      return false;
    }
  }
}

// 导出服务实例
export const chatService = new ChatService();