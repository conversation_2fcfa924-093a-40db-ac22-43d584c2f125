/**
 * 企业微信工具函数库
 */

// 全局状态跟踪配置失败
let globalConfigAttemptFailed = false;

/**
 * 检测当前是否在企业微信环境中
 */
export function isWechatWorkEnv(): boolean {
  if (typeof window === 'undefined') return false

  const ua = navigator.userAgent.toLowerCase()
  return /wxwork/.test(ua)
}

/**
 * 检测当前是否在微信环境中（包括企业微信和普通微信）
 */
export function isWechatEnv(): boolean {
  if (typeof window === 'undefined') return false

  const ua = navigator.userAgent.toLowerCase()
  return /micromessenger/.test(ua) || /wxwork/.test(ua)
}

/**
 * 获取完整的用户代理字符串
 */
export function getUserAgent(): string {
  if (typeof window === 'undefined') return ''
  return navigator.userAgent
}

/**
 * 获取全局配置失败状态
 */
export function getConfigFailureStatus(): boolean {
  return globalConfigAttemptFailed;
}

/**
 * 设置全局配置失败状态
 */
export function setConfigFailureStatus(status: boolean): void {
  globalConfigAttemptFailed = status;
}

/**
 * 在系统默认浏览器中打开链接
 *
 * 注意：该函数仅在企业微信环境中且JS-SDK已正确配置时有效
 */
export async function openInDefaultBrowser(url: string): Promise<boolean> {
  if (typeof window === 'undefined') return false

  // 检查企业微信环境
  if (!isWechatWorkEnv()) {
    console.warn('当前不在企业微信环境，无法使用openDefaultBrowser接口')
    // 使用普通方式打开
    window.open(url, '_blank')
    return false
  }

  // 检查JS-SDK是否已加载
  if (!window.wx) {
    console.error('企业微信JS-SDK未加载，无法使用openDefaultBrowser接口')
    window.open(url, '_blank')
    return false
  }

  // 检查全局失败状态，如果之前失败过，直接使用普通方式打开
  if (globalConfigAttemptFailed) {
    console.warn('JS-SDK配置曾经失败，跳过SDK调用，使用普通方式打开链接');
    window.open(url, '_blank');
    return false;
  }

  try {
    return new Promise((resolve) => {
      window.wx.openDefaultBrowser({
        url: url,
        success: () => resolve(true),
        fail: (res: any) => {
          console.error('打开系统浏览器失败:', res.errMsg)
          // 失败时尝试使用普通方式打开
          window.open(url, '_blank')
          resolve(false)
        }
      })
    })
  } catch (error) {
    console.error('调用openDefaultBrowser时发生错误:', error)
    // 发生错误时使用普通方式打开
    window.open(url, '_blank')
    return false
  }
}

/**
 * 在外部浏览器中打开链接的通用处理函数
 *
 * 可用于onClick事件处理
 */
export function handleExternalLink(
  url: string,
  e?: React.MouseEvent<HTMLAnchorElement>
): void {
  // 如果有事件对象，阻止默认行为
  if (e) {
    e.preventDefault()
  }

  // 如果已知配置失败，直接使用普通方式打开
  if (globalConfigAttemptFailed) {
    window.open(url, '_blank');
    return;
  }

  // 尝试在系统浏览器中打开
  openInDefaultBrowser(url).catch(() => {
    // 出错时使用普通方式打开
    window.open(url, '_blank')
  })
}

/**
 * 判断URL是否为有效URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch (e) {
    return false
  }
}

// 添加全局类型声明
declare global {
  interface Window {
    wx: any
  }
}

export default {
  isWechatWorkEnv,
  isWechatEnv,
  getUserAgent,
  openInDefaultBrowser,
  handleExternalLink,
  isValidUrl,
  getConfigFailureStatus,
  setConfigFailureStatus
}