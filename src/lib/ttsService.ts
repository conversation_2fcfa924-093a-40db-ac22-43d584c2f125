/**
 * TTS（文本转语音）服务
 */

export interface TTSConfig {
  voice?: string;
  speed?: number;
  pitch?: number;
}

export class TTSService {
  private apiUrl: string;
  private apiKey: string;
  private currentAudio: HTMLAudioElement | null = null;
  private currentAudioUrl: string | null = null;
  private currentMessageId: string | null = null; // 添加：当前播放的消息ID
  private audioQueue: { blob: Blob; index: number }[] = [];
  private currentSegmentIndex: number = 0;
  private isPlayingQueue: boolean = false;
  private onPlaybackComplete?: () => void;
  private onPlaybackError?: (error: Error) => void;

  constructor() {
    // 使用与聊天服务相同的API配置
    this.apiUrl = process.env.NEXT_PUBLIC_API_URL || "";
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || "";

    // 尝试从本地存储中加载配置
    if (typeof localStorage !== 'undefined') {
      const savedApiUrl = localStorage.getItem("chatApiUrl");
      const savedApiKey = localStorage.getItem("chatApiKey");

      if (savedApiUrl) this.apiUrl = savedApiUrl;
      if (savedApiKey) this.apiKey = savedApiKey;
    }
  }

  /**
   * 调用后端TTS接口，将文本转换为语音
   * @param text 要转换的文本
   * @param config TTS配置选项
   * @returns Promise<Blob> 音频文件流
   */
  async textToSpeech(text: string, config: TTSConfig = {}): Promise<Blob> {
    if (!this.apiUrl) {
      throw new Error('未配置API URL，请在设置中配置API地址');
    }

    // 确保API URL格式正确
    let apiUrl = this.apiUrl;
    if (apiUrl.endsWith('/')) {
      apiUrl = apiUrl.slice(0, -1);
    }

    const ttsUrl = `${apiUrl}/tts`;

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // 如果有API Key，添加到请求头
    if (this.apiKey) {
      headers["Authorization"] = `Bearer ${this.apiKey}`;
    }

    const requestBody = {
      text: text,
      voice: config.voice || 'default',
      speed: config.speed || 1.0,
      pitch: config.pitch || 1.0,
    };

    try {
      console.log(`发送TTS请求到: ${ttsUrl}`);
      console.log('请求体:', requestBody);

      const response = await fetch(ttsUrl, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(60000), // 60秒超时
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`TTS API错误: 状态码=${response.status}, 响应内容=${errorText}`);
        throw new Error(`TTS API错误: ${response.status} - ${errorText}`);
      }

      // 检查响应类型是否为音频
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('audio')) {
        throw new Error('服务器返回的不是音频文件');
      }

      const audioBlob = await response.blob();
      console.log('成功获取音频文件，大小:', audioBlob.size, 'bytes');
      
      return audioBlob;
    } catch (error) {
      console.error("TTS请求错误:", error);
      throw error;
    }
  }

  /**
   * 分段处理长文本
   * @param text 要分段的文本
   * @param maxLength 每段的最大长度
   * @returns 文本段数组
   */
  private splitTextIntoSegments(text: string, maxLength: number = 2000): string[] {
    const segments: string[] = [];
    const sentences = text.match(/[^。！？.!?]+[。！？.!?]+/g) || [text];
    
    let currentSegment = '';
    
    for (const sentence of sentences) {
      if ((currentSegment + sentence).length <= maxLength) {
        currentSegment += sentence;
      } else {
        if (currentSegment) {
          segments.push(currentSegment.trim());
        }
        currentSegment = sentence;
      }
    }
    
    if (currentSegment) {
      segments.push(currentSegment.trim());
    }
    
    return segments;
  }

  /**
   * 批量转换文本为语音
   * @param segments 文本段数组
   * @param config TTS配置
   * @returns Promise<Blob[]> 音频文件数组
   */
  async batchTextToSpeech(segments: string[], config: TTSConfig = {}): Promise<{ blob: Blob; index: number }[]> {
    const results: { blob: Blob; index: number }[] = [];
    
    for (let i = 0; i < segments.length; i++) {
      try {
        console.log(`转换第 ${i + 1}/${segments.length} 段文本...`);
        const blob = await this.textToSpeech(segments[i], config);
        results.push({ blob, index: i });
      } catch (error) {
        console.error(`第 ${i + 1} 段转换失败:`, error);
        throw error;
      }
    }
    
    return results;
  }

  /**
   * 创建音频实例（不自动播放）
   * @param audioBlob 音频文件Blob
   * @param messageId 消息ID
   */
  createAudio(audioBlob: Blob, messageId: string): void {
    try {
      // 清理之前的音频资源
      this.cleanup();

      // 创建音频URL
      this.currentAudioUrl = URL.createObjectURL(audioBlob);
      
      // 创建音频元素
      this.currentAudio = new Audio(this.currentAudioUrl);
      
      // 保存当前消息ID
      this.currentMessageId = messageId;
      
      // 设置音频事件监听器
      this.currentAudio.addEventListener('ended', () => {
        console.log('音频段播放结束');
        // 如果有队列，播放下一段
        if (this.audioQueue.length > 0 && this.isPlayingQueue) {
          this.playNextSegment();
        } else {
          // 所有段播放结束
          console.log('所有音频段播放完成');
          this.currentMessageId = null;
          this.isPlayingQueue = false;
          this.currentSegmentIndex = 0;
          if (this.onPlaybackComplete) {
            this.onPlaybackComplete();
          }
        }
      });

      this.currentAudio.addEventListener('error', (error) => {
        console.error('音频播放错误:', error);
        this.isPlayingQueue = false;
        if (this.onPlaybackError) {
          this.onPlaybackError(new Error('音频播放失败'));
        }
        // 出错时才cleanup
        this.cleanup();
      });

      console.log('音频实例创建成功，消息ID:', messageId);
    } catch (error) {
      console.error('创建音频实例失败:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * 播放下一个音频段
   */
  private async playNextSegment(): Promise<void> {
    if (this.audioQueue.length === 0) {
      return;
    }

    const segment = this.audioQueue.shift()!;
    this.currentSegmentIndex = segment.index;
    
    // 清理当前音频但保留消息ID和队列信息
    if (this.currentAudio) {
      this.currentAudio.removeEventListener('ended', () => {});
      this.currentAudio.removeEventListener('error', () => {});
      this.currentAudio = null;
    }
    if (this.currentAudioUrl) {
      URL.revokeObjectURL(this.currentAudioUrl);
      this.currentAudioUrl = null;
    }

    // 创建新的音频实例
    this.currentAudioUrl = URL.createObjectURL(segment.blob);
    this.currentAudio = new Audio(this.currentAudioUrl);
    
    // 重新设置事件监听器
    this.currentAudio.addEventListener('ended', () => {
      console.log(`音频段 ${segment.index + 1} 播放结束`);
      if (this.audioQueue.length > 0 && this.isPlayingQueue) {
        this.playNextSegment();
      } else {
        console.log('所有音频段播放完成');
        this.currentMessageId = null;
        this.isPlayingQueue = false;
        this.currentSegmentIndex = 0;
        if (this.onPlaybackComplete) {
          this.onPlaybackComplete();
        }
      }
    });

    this.currentAudio.addEventListener('error', (error) => {
      console.error('音频播放错误:', error);
      this.isPlayingQueue = false;
      if (this.onPlaybackError) {
        this.onPlaybackError(new Error('音频播放失败'));
      }
      this.cleanup();
    });

    // 播放音频
    try {
      await this.currentAudio.play();
      console.log(`开始播放第 ${segment.index + 1} 段音频`);
    } catch (error) {
      console.error('播放音频段失败:', error);
      this.isPlayingQueue = false;
      throw error;
    }
  }

  /**
   * 创建并播放音频队列
   * @param audioSegments 音频段数组
   * @param messageId 消息ID
   * @param onComplete 播放完成回调
   * @param onError 播放错误回调
   */
  async createAndPlayQueue(
    audioSegments: { blob: Blob; index: number }[], 
    messageId: string,
    onComplete?: () => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // 清理之前的资源
    this.cleanup();
    
    this.currentMessageId = messageId;
    this.audioQueue = [...audioSegments];
    this.currentSegmentIndex = 0;
    this.isPlayingQueue = true;
    this.onPlaybackComplete = onComplete;
    this.onPlaybackError = onError;
    
    // 播放第一段
    await this.playNextSegment();
  }

  /**
   * 开始播放当前音频
   */
  async playAudio(): Promise<void> {
    if (!this.currentAudio) {
      throw new Error('音频实例不存在，请先创建音频');
    }

    try {
      await this.currentAudio.play();
      console.log('音频开始播放');
    } catch (error) {
      console.error('音频播放失败:', error);
      throw error;
    }
  }

  /**
   * 停止当前播放的音频并清理资源
   */
  stopAudio(): void {
    this.isPlayingQueue = false;
    this.audioQueue = [];
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      console.log('音频已停止');
    }
    this.cleanup();
  }

  /**
   * 暂停当前播放的音频（不清理资源）
   */
  pauseAudio(): void {
    this.isPlayingQueue = false; // 暂停队列播放
    if (this.currentAudio && !this.currentAudio.paused) {
      this.currentAudio.pause();
      console.log('音频已暂停');
    } else if (!this.currentAudio) {
      console.warn('音频实例不存在，无法暂停');
    }
  }

  /**
   * 恢复播放当前音频
   */
  async resumeAudio(): Promise<void> {
    if (this.currentAudio && this.currentAudio.paused && !this.currentAudio.ended) {
      try {
        this.isPlayingQueue = true; // 恢复队列播放
        await this.currentAudio.play();
        console.log('音频恢复播放');
      } catch (error) {
        console.error('恢复播放失败:', error);
        throw error;
      }
    } else if (!this.currentAudio) {
      console.error('音频实例不存在，无法恢复播放');
      throw new Error('音频实例不存在');
    } else if (this.currentAudio.ended) {
      console.error('音频已播放完成，无法恢复播放');
      throw new Error('音频已播放完成');
    } else {
      console.log('音频已在播放中');
    }
  }

  /**
   * 获取当前音频播放状态
   */
  getPlaybackState(): 'idle' | 'playing' | 'paused' {
    if (!this.currentAudio) {
      return 'idle';
    }
    
    // 检查音频是否已结束
    if (this.currentAudio.ended) {
      return 'idle';
    }
    
    return this.currentAudio.paused ? 'paused' : 'playing';
  }

  /**
   * 检查是否有指定消息的音频实例
   * @param messageId 消息ID
   */
  hasAudioForMessage(messageId: string): boolean {
    return this.currentMessageId === messageId && this.currentAudio !== null && !this.currentAudio.ended;
  }

  /**
   * 获取当前播放的消息ID
   */
  getCurrentMessageId(): string | null {
    return this.currentMessageId;
  }

  /**
   * 清理音频资源
   */
  private cleanup(): void {
    if (this.currentAudio) {
      // 移除所有事件监听器
      this.currentAudio.removeEventListener('ended', () => {});
      this.currentAudio.removeEventListener('error', () => {});
      this.currentAudio.removeEventListener('loadstart', () => {});
      this.currentAudio.removeEventListener('loadeddata', () => {});
      
      // 清空音频元素引用
      this.currentAudio = null;
    }

    // 释放音频URL资源
    if (this.currentAudioUrl) {
      URL.revokeObjectURL(this.currentAudioUrl);
      this.currentAudioUrl = null;
      console.log('音频URL资源已释放');
    }
    
    // 清除消息ID和队列
    this.currentMessageId = null;
    this.audioQueue = [];
    this.isPlayingQueue = false;
    this.currentSegmentIndex = 0;
    this.onPlaybackComplete = undefined;
    this.onPlaybackError = undefined;
  }

  /**
   * 清理所有资源
   */
  destroy(): void {
    this.stopAudio();
  }
}

// 导出服务实例
export const ttsService = new TTSService();