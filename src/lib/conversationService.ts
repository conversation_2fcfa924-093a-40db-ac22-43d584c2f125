/**
 * 会话管理服务 - 封装会话相关的API调用
 */

import { ConversationType, MessageType } from "@/components/chat/ChatInterface";

// 同步状态枚举
export enum SyncStatus {
  PENDING = 'pending',
  SYNCING = 'syncing', 
  SYNCED = 'synced',
  ERROR = 'error',
  OFFLINE = 'offline'
}

// API响应接口
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 会话服务配置
interface ConversationServiceConfig {
  enableOfflineMode: boolean;
  retryAttempts: number;
  retryDelay: number;
}

class ConversationService {
  private config: ConversationServiceConfig = {
    enableOfflineMode: true,
    retryAttempts: 3,
    retryDelay: 1000
  };

  private isOnline: boolean = true;
  private syncQueue: Array<() => Promise<void>> = [];

  constructor() {
    // 监听网络状态
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine;
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.processSyncQueue();
      });
      window.addEventListener('offline', () => {
        this.isOnline = false;
      });
    }
  }

  /**
   * 获取当前用户ID
   */
  private getUserId(): string {
    if (typeof localStorage === 'undefined') return '';
    
    const cachedUser = localStorage.getItem('wechat_work_user');
    return cachedUser ? JSON.parse(cachedUser).userId : '';
  }

  /**
   * 重试机制的API调用
   */
  private async apiCallWithRetry<T>(
    apiCall: () => Promise<Response>,
    fallback?: () => T
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        if (!this.isOnline && this.config.enableOfflineMode && fallback) {
          console.warn('离线模式：使用本地fallback');
          return fallback();
        }

        const response = await apiCall();
        
        if (!response.ok) {
          throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
        }

        const result: ApiResponse<T> = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || '未知错误');
        }

        return result.data as T;
      } catch (error) {
        lastError = error as Error;
        console.error(`API调用失败 (尝试 ${attempt}/${this.config.retryAttempts}):`, error);

        if (attempt < this.config.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempt));
        }
      }
    }

    // 所有重试都失败后，如果有fallback则使用
    if (this.config.enableOfflineMode && fallback) {
      console.warn('API调用失败，使用本地fallback');
      return fallback();
    }

    throw lastError!;
  }

  /**
   * 获取用户所有会话列表
   */
  async getConversations(): Promise<ConversationType[]> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    const fallback = (): ConversationType[] => {
      // 离线模式：从localStorage获取
      if (typeof localStorage === 'undefined') return [];
      const saved = localStorage.getItem('conversations');
      return saved ? JSON.parse(saved) : [];
    };

    return this.apiCallWithRetry(
      () => fetch(`/api/conversations?user_id=${userId}&limit=100`),
      fallback
    );
  }

  /**
   * 创建新会话
   */
  async createConversation(conversation: Omit<ConversationType, 'id'>): Promise<ConversationType> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    const conversationData = {
      user_id: userId,
      title: conversation.title,
      session_id: conversation.sessionId
    };

    const fallback = (): ConversationType => {
      // 离线模式：创建本地会话
      const newConversation: ConversationType = {
        ...conversation,
        id: `local_${Date.now()}`, // 临时ID，稍后同步时替换
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      
      // 保存到localStorage
      if (typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('conversations');
        const conversations = saved ? JSON.parse(saved) : [];
        conversations.push(newConversation);
        localStorage.setItem('conversations', JSON.stringify(conversations));
        
        // 添加到同步队列
        this.syncQueue.push(async () => {
          try {
            await this.createConversation(conversation);
          } catch (error) {
            console.error('同步创建会话失败:', error);
          }
        });
      }
      
      return newConversation;
    };

    return this.apiCallWithRetry(
      () => fetch('/api/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(conversationData)
      }),
      fallback
    );
  }

  /**
   * 更新会话信息
   */
  async updateConversation(
    conversationId: string, 
    updates: Partial<Pick<ConversationType, 'title'>>
  ): Promise<ConversationType> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    // 如果是本地会话ID（以local_开头），需要先创建到后端
    if (conversationId.startsWith('local_')) {
      return this.handleLocalConversationUpdate(conversationId, updates);
    }

    const updateData = {
      user_id: userId,
      ...updates,
      updated_at: new Date().toISOString()
    };

    const fallback = (): ConversationType => {
      // 离线模式：更新localStorage
      if (typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('conversations');
        const conversations: ConversationType[] = saved ? JSON.parse(saved) : [];
        const index = conversations.findIndex(c => c.id === conversationId);
        
        if (index !== -1) {
          conversations[index] = {
            ...conversations[index],
            ...updates,
            updatedAt: Date.now()
          };
          localStorage.setItem('conversations', JSON.stringify(conversations));
          
          // 添加到同步队列
          this.syncQueue.push(async () => {
            try {
              await this.updateConversation(conversationId, updates);
            } catch (error) {
              console.error('同步更新会话失败:', error);
            }
          });
          
          return conversations[index];
        }
      }
      
      throw new Error('会话不存在');
    };

    return this.apiCallWithRetry(
      () => fetch(`/api/conversations/${conversationId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      }),
      fallback
    );
  }

  /**
   * 删除会话
   */
  async deleteConversation(conversationId: string): Promise<void> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    const fallback = (): void => {
      // 离线模式：从localStorage删除
      if (typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('conversations');
        const conversations: ConversationType[] = saved ? JSON.parse(saved) : [];
        const filtered = conversations.filter(c => c.id !== conversationId);
        localStorage.setItem('conversations', JSON.stringify(filtered));
        
        // 添加到同步队列
        this.syncQueue.push(async () => {
          try {
            await this.deleteConversation(conversationId);
          } catch (error) {
            console.error('同步删除会话失败:', error);
          }
        });
      }
    };

    return this.apiCallWithRetry(
      () => fetch(`/api/conversations/${conversationId}?user_id=${userId}`, {
        method: 'DELETE'
      }),
      fallback
    );
  }

  /**
   * 获取会话消息历史
   */
  async getConversationMessages(conversationId: string): Promise<MessageType[]> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    const fallback = (): MessageType[] => {
      // 离线模式：从localStorage获取
      if (typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('conversations');
        const conversations: ConversationType[] = saved ? JSON.parse(saved) : [];
        const conversation = conversations.find(c => c.id === conversationId);
        return conversation?.messages || [];
      }
      return [];
    };

    return this.apiCallWithRetry(
      () => fetch(`/api/conversations/${conversationId}/messages?user_id=${userId}`),
      fallback
    );
  }

  /**
   * 添加消息到会话
   */
  async addMessageToConversation(
    conversationId: string, 
    message: Omit<MessageType, 'id'>
  ): Promise<MessageType> {
    const userId = this.getUserId();
    if (!userId) {
      throw new Error('用户未登录');
    }

    const messageData = {
      user_id: userId,
      role: message.role,
      content: message.content,
      thinking: message.thinking,
      process_time: message.processTime,
      attachments: message.attachments,
      timestamp: message.timestamp
    };

    const fallback = (): MessageType => {
      // 离线模式：添加到localStorage
      const newMessage: MessageType = {
        ...message,
        id: `local_${Date.now()}`
      };
      
      if (typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('conversations');
        const conversations: ConversationType[] = saved ? JSON.parse(saved) : [];
        const index = conversations.findIndex(c => c.id === conversationId);
        
        if (index !== -1) {
          conversations[index].messages.push(newMessage);
          conversations[index].updatedAt = Date.now();
          localStorage.setItem('conversations', JSON.stringify(conversations));
          
          // 添加到同步队列
          this.syncQueue.push(async () => {
            try {
              await this.addMessageToConversation(conversationId, message);
            } catch (error) {
              console.error('同步添加消息失败:', error);
            }
          });
        }
      }
      
      return newMessage;
    };

    return this.apiCallWithRetry(
      () => fetch(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(messageData)
      }),
      fallback
    );
  }

  /**
   * 处理本地会话的更新（需要先迁移到后端）
   */
  private async handleLocalConversationUpdate(
    localConversationId: string,
    updates: Partial<Pick<ConversationType, 'title'>>
  ): Promise<ConversationType> {
    if (typeof localStorage === 'undefined') {
      throw new Error('localStorage不可用');
    }

    // 从localStorage获取本地会话
    const saved = localStorage.getItem('conversations');
    const conversations: ConversationType[] = saved ? JSON.parse(saved) : [];
    const localConversation = conversations.find(c => c.id === localConversationId);
    
    if (!localConversation) {
      throw new Error('本地会话不存在');
    }

    try {
      // 生成新的session_id用于后端
      const backendSessionId = `migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 先在后端创建会话
      const createdConversation = await this.createConversation({
        title: updates.title || localConversation.title,
        sessionId: backendSessionId
      });

      // 如果本地会话有消息，需要迁移消息
      if (localConversation.messages && localConversation.messages.length > 0) {
        for (const message of localConversation.messages) {
          try {
            await this.addMessageToConversation(backendSessionId, {
              role: message.role,
              content: message.content,
              thinking: message.thinking,
              processTime: message.processTime,
              timestamp: message.timestamp,
              attachments: message.attachments
            });
          } catch (error) {
            console.error('迁移消息失败:', error);
          }
        }
      }

      // 更新localStorage中的会话ID
      const conversationIndex = conversations.findIndex(c => c.id === localConversationId);
      if (conversationIndex !== -1) {
        conversations[conversationIndex] = {
          ...createdConversation,
          id: backendSessionId,
          sessionId: backendSessionId
        };
        localStorage.setItem('conversations', JSON.stringify(conversations));
      }

      console.log(`本地会话 ${localConversationId} 已迁移到后端，新ID: ${backendSessionId}`);
      return createdConversation;

    } catch (error) {
      console.error('迁移本地会话到后端失败:', error);
      
      // 迁移失败，回退到本地更新
      const conversationIndex = conversations.findIndex(c => c.id === localConversationId);
      if (conversationIndex !== -1) {
        conversations[conversationIndex] = {
          ...conversations[conversationIndex],
          ...updates,
          updatedAt: Date.now()
        };
        localStorage.setItem('conversations', JSON.stringify(conversations));
        return conversations[conversationIndex];
      }
      
      throw error;
    }
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    console.log(`开始处理同步队列，共 ${this.syncQueue.length} 个任务`);
    
    const queue = [...this.syncQueue];
    this.syncQueue = [];

    for (const syncTask of queue) {
      try {
        await syncTask();
      } catch (error) {
        console.error('同步任务失败:', error);
        // 失败的任务重新加入队列
        this.syncQueue.push(syncTask);
      }
    }
  }

  /**
   * 手动触发同步
   */
  async sync(): Promise<void> {
    if (this.isOnline) {
      await this.processSyncQueue();
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): SyncStatus {
    if (!this.isOnline) return SyncStatus.OFFLINE;
    if (this.syncQueue.length > 0) return SyncStatus.PENDING;
    return SyncStatus.SYNCED;
  }
}

// 导出单例实例
export const conversationService = new ConversationService();
export default conversationService;