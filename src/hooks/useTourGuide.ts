"use client"

import { useState, useEffect, useCallback } from 'react'
import { useWechatWorkAuth } from '@/components/auth/WechatWorkAuth'
import { getTourSteps, defaultTourConfig, TourConfig } from '@/config/tourSteps'
import { TourStep } from '@/components/tour/TourGuide'

export type TourModule = 'main' | 'chat' | 'ai-tools' | 'doc-processing' | 'visualization' | 'test'

interface TourState {
  isOpen: boolean
  currentModule: TourModule | null
  steps: TourStep[]
  hasCompletedTour: boolean
  hasSeenModule: Record<TourModule, boolean>
}

interface UseTourGuideReturn {
  // 状态
  isOpen: boolean
  currentModule: TourModule | null
  steps: TourStep[]
  hasCompletedTour: boolean
  hasSeenModule: (module: TourModule) => boolean
  
  // 操作方法
  startTour: (module: TourModule) => void
  closeTour: () => void
  completeTour: () => void
  resetTour: () => void
  markModuleAsSeen: (module: TourModule) => void
  
  // 配置
  config: TourConfig
}

export function useTourGuide(customConfig?: Partial<TourConfig>): UseTourGuideReturn {
  const { userInfo } = useWechatWorkAuth()
  const config = { ...defaultTourConfig, ...customConfig }
  
  const [tourState, setTourState] = useState<TourState>({
    isOpen: false,
    currentModule: null,
    steps: [],
    hasCompletedTour: false,
    hasSeenModule: {
      main: false,
      chat: false,
      'ai-tools': false,
      'doc-processing': false,
      visualization: false,
      test: false
    }
  })

  // 获取存储键名（包含用户ID）
  const getStorageKey = useCallback((key: string) => {
    const userId = userInfo?.userId || 'anonymous'
    return `${key}_${userId}`
  }, [userInfo?.userId])



  // 保存引导完成状态
  const saveTourCompleted = useCallback(() => {
    try {
      const completedKey = getStorageKey(config.storageKey)
      localStorage.setItem(completedKey, 'true')
    } catch (error) {
      console.error('保存引导完成状态失败:', error)
    }
  }, [config.storageKey, getStorageKey])

  // 保存模块访问状态
  const saveModuleSeenState = useCallback((moduleSeenState: Record<TourModule, boolean>) => {
    try {
      const moduleSeenKey = getStorageKey('mengchat_tour_modules_seen')
      localStorage.setItem(moduleSeenKey, JSON.stringify(moduleSeenState))
    } catch (error) {
      console.error('保存模块访问状态失败:', error)
    }
  }, [getStorageKey])

  // 开始引导
  const startTour = useCallback((module: TourModule) => {
    const steps = getTourSteps(module)
    setTourState(prev => ({
      ...prev,
      isOpen: true,
      currentModule: module,
      steps
    }))
  }, [])

  // 关闭引导
  const closeTour = useCallback(() => {
    setTourState(prev => ({
      ...prev,
      isOpen: false,
      currentModule: null,
      steps: []
    }))
  }, [])

  // 完成引导
  const completeTour = useCallback(() => {
    const currentModule = tourState.currentModule
    
    // 标记当前模块为已访问
    if (currentModule) {
      const newModuleSeenState = {
        ...tourState.hasSeenModule,
        [currentModule]: true
      }
      
      setTourState(prev => ({
        ...prev,
        hasSeenModule: newModuleSeenState
      }))
      
      saveModuleSeenState(newModuleSeenState)
    }
    
    // 如果完成的是主引导，标记整个引导为已完成
    if (currentModule === 'main') {
      setTourState(prev => ({
        ...prev,
        hasCompletedTour: true
      }))
      saveTourCompleted()
    }
    
    closeTour()
  }, [tourState.currentModule, tourState.hasSeenModule, saveModuleSeenState, saveTourCompleted, closeTour])

  // 重置引导状态
  const resetTour = useCallback(() => {
    try {
      const completedKey = getStorageKey(config.storageKey)
      const moduleSeenKey = getStorageKey('mengchat_tour_modules_seen')
      
      localStorage.removeItem(completedKey)
      localStorage.removeItem(moduleSeenKey)
      
      setTourState({
        isOpen: false,
        currentModule: null,
        steps: [],
        hasCompletedTour: false,
        hasSeenModule: {
          main: false,
          chat: false,
          'ai-tools': false,
          'doc-processing': false,
          visualization: false,
          test: false
        }
      })
    } catch (error) {
      console.error('重置引导状态失败:', error)
    }
  }, [config.storageKey, getStorageKey])

  // 标记模块为已访问
  const markModuleAsSeen = useCallback((module: TourModule) => {
    const newModuleSeenState = {
      ...tourState.hasSeenModule,
      [module]: true
    }
    
    setTourState(prev => ({
      ...prev,
      hasSeenModule: newModuleSeenState
    }))
    
    saveModuleSeenState(newModuleSeenState)
  }, [tourState.hasSeenModule, saveModuleSeenState])

  // 检查模块是否已访问
  const hasSeenModule = useCallback((module: TourModule) => {
    return tourState.hasSeenModule[module]
  }, [tourState.hasSeenModule])

  // 初始化时加载状态
  useEffect(() => {
    try {
      const userId = userInfo?.userId || 'anonymous'
      const completedKey = `${config.storageKey}_${userId}`
      const moduleSeenKey = `mengchat_tour_modules_seen_${userId}`

      const hasCompletedTour = localStorage.getItem(completedKey) === 'true'
      const moduleSeenData = localStorage.getItem(moduleSeenKey)

      let hasSeenModule = {
        main: false,
        chat: false,
        'ai-tools': false,
        'doc-processing': false,
        visualization: false,
        test: false
      }

      if (moduleSeenData) {
        try {
          hasSeenModule = { ...hasSeenModule, ...JSON.parse(moduleSeenData) }
        } catch (error) {
          console.error('解析模块访问状态失败:', error)
        }
      }

      setTourState(prev => ({
        ...prev,
        hasCompletedTour,
        hasSeenModule
      }))

      // 如果是首次访问且配置为自动开始，则开始主引导
      if (config.showOnFirstVisit && !hasCompletedTour && !hasSeenModule.main) {
        setTimeout(() => {
          const steps = getTourSteps('main')
          setTourState(prev => ({
            ...prev,
            isOpen: true,
            currentModule: 'main',
            steps
          }))
        }, 1000) // 延迟1秒开始，确保页面完全加载
      }
    } catch (error) {
      console.error('加载引导状态失败:', error)
    }
  }, [userInfo?.userId, config.storageKey, config.showOnFirstVisit])

  return {
    // 状态
    isOpen: tourState.isOpen,
    currentModule: tourState.currentModule,
    steps: tourState.steps,
    hasCompletedTour: tourState.hasCompletedTour,
    hasSeenModule,
    
    // 操作方法
    startTour,
    closeTour,
    completeTour,
    resetTour,
    markModuleAsSeen,
    
    // 配置
    config
  }
}

export default useTourGuide
