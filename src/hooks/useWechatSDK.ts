import { useState, useEffect, useCallback } from 'react';
import wechatJSSDK, { WxConfigOptions, ToastOptions, ModalOptions } from '@/lib/wechatJSSDK';

/**
 * 企业微信JS-SDK Hook
 * 用于在React组件中方便地使用企业微信JS-SDK
 */
export function useWechatSDK(options: WxConfigOptions = {}) {
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 配置企业微信JS-SDK
  const configWx = useCallback(async (customOptions: WxConfigOptions = {}): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const success = await wechatJSSDK.configWx({
        ...options,
        ...customOptions,
        onReady: () => {
          customOptions.onReady?.();
          options.onReady?.();
        },
        onError: (err) => {
          customOptions.onError?.(err);
          options.onError?.(err);
        }
      });
      
      setIsReady(success);
      return success;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err));
      setError(errorObj);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [options]);

  // 显示消息提示框
  const showToast = useCallback((toastOptions: ToastOptions): Promise<void> => {
    return wechatJSSDK.showToast(toastOptions);
  }, []);

  // 显示模态对话框
  const showModal = useCallback((modalOptions: ModalOptions): Promise<{ confirm: boolean; cancel: boolean }> => {
    return wechatJSSDK.showModal(modalOptions);
  }, []);

  // 在系统默认浏览器中打开链接
  const openInDefaultBrowser = useCallback((url: string): Promise<boolean> => {
    return wechatJSSDK.openInDefaultBrowser(url);
  }, []);

  // 通用API调用方法
  const invokeApi = useCallback(<T = any>(apiName: string, params: any = {}): Promise<T> => {
    return wechatJSSDK.invokeApi(apiName, params);
  }, []);

  // 重置全局失败状态
  const resetConfigFailure = useCallback((): void => {
    wechatJSSDK.resetConfigFailure();
  }, []);

  // 检测是否在企业微信环境
  const isWechatWorkEnv = useCallback((): boolean => {
    return wechatJSSDK.isWechatWorkEnv();
  }, []);

  // 检测是否在微信环境
  const isWechatEnv = useCallback((): boolean => {
    return wechatJSSDK.isWechatEnv();
  }, []);

  // 自动初始化（如果options.autoInit为true）
  useEffect(() => {
    if (options.autoInit && typeof window !== 'undefined') {
      configWx().catch(err => {
        console.error('自动初始化JS-SDK失败:', err);
      });
    }
    
    // 同步状态
    setIsReady(wechatJSSDK.getReadyState());
    setIsLoading(wechatJSSDK.getLoadingState());
    setError(wechatJSSDK.getError());
  }, [configWx, options.autoInit]);

  return {
    isReady,
    isLoading,
    error,
    configWx,
    showToast,
    showModal,
    openInDefaultBrowser,
    invokeApi,
    resetConfigFailure,
    isWechatWorkEnv,
    isWechatEnv
  };
}

export default useWechatSDK;
