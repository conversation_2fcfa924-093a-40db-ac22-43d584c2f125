"use client"

import React from 'react'
import { useState, useEffect } from 'react'
import { useWechatSDK } from '@/hooks/useWechatSDK'

interface ExternalBrowserLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  onSuccess?: () => void
  onError?: (error: Error) => void
  fallback?: boolean // 是否在失败时使用默认链接行为
}

/**
 * 企业微信外部浏览器链接组件
 *
 * 使用系统默认浏览器打开链接，而不是在企业微信内打开
 */
export function ExternalBrowserLink({
  href,
  children,
  className = '',
  onSuccess,
  onError,
  fallback = true
}: ExternalBrowserLinkProps) {
  const [mounted, setMounted] = useState(false)
  const {
    isReady,
    configWx,
    openInDefaultBrowser,
    isWechatWorkEnv
  } = useWechatSDK({
    jsApiList: ['openDefaultBrowser'],
    skipConfigOnFailure: true // 使用全局失败状态
  })

  // 检测是否已挂载
  useEffect(() => {
    setMounted(true)
  }, [])

  // 处理点击事件
  const handleClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    // 如果不在企业微信环境或没有挂载，使用默认链接行为
    if (!mounted || !isWechatWorkEnv()) {
      return
    }

    // 阻止默认行为
    e.preventDefault()

    try {
      // 如果JS-SDK未就绪，尝试配置(只尝试一次)
      if (!isReady) {
        const success = await configWx({
          jsApiList: ['openDefaultBrowser']
        })

        // 如果配置失败，使用回退行为
        if (!success && fallback) {
          window.open(href, '_blank')
          return
        }
      }

      // 尝试调用接口打开外部浏览器
      await openInDefaultBrowser(href)

      onSuccess?.()
    } catch (error) {
      console.error('打开外部浏览器失败:', error)

      // 调用错误回调
      if (error instanceof Error) {
        onError?.(error)
      } else {
        onError?.(new Error(String(error)))
      }

      // 如果设置了回退选项，则使用默认行为
      if (fallback) {
        window.open(href, '_blank')
      }
    }
  }

  return (
    <a
      href={href}
      className={className}
      onClick={handleClick}
      target="_blank"
      rel="noopener noreferrer"
    >
      {children}
    </a>
  )
}

/**
 * 外部链接按钮 - 样式化版本
 */
export function ExternalBrowserButton({
  href,
  children,
  className = '',
  ...props
}: ExternalBrowserLinkProps) {
  return (
    <ExternalBrowserLink
      href={href}
      className={`inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 ${className}`}
      {...props}
    >
      {children}
    </ExternalBrowserLink>
  )
}

export default ExternalBrowserLink