"use client"

import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import useTourGuide, { TourModule } from '@/hooks/useTourGuide'
import { HelpCircle, RotateCcw, CheckCircle, Play } from 'lucide-react'

interface TourManagerProps {
  className?: string
}

export default function TourManager({ className }: TourManagerProps) {
  const {
    startTour,
    resetTour,
    hasSeenModule,
    hasCompletedTour
  } = useTourGuide()

  const tourModules: Array<{
    id: TourModule
    name: string
    description: string
    icon: React.ReactNode
  }> = [
    {
      id: 'main',
      name: '主要功能引导',
      description: '介绍系统的核心功能和导航方式',
      icon: <HelpCircle className="h-5 w-5" />
    },
    {
      id: 'chat',
      name: '聊天功能引导',
      description: '学习如何与AI进行有效对话（适用于有无消息状态）',
      icon: <HelpCircle className="h-5 w-5" />
    },
    {
      id: 'ai-tools',
      name: 'AI工具引导',
      description: '探索各种AI工具和应用',
      icon: <HelpCircle className="h-5 w-5" />
    },
    {
      id: 'doc-processing',
      name: '文档处理引导',
      description: '掌握文档翻译、总结等功能',
      icon: <HelpCircle className="h-5 w-5" />
    },
    {
      id: 'visualization',
      name: '数据可视化引导',
      description: '学习如何创建数据图表',
      icon: <HelpCircle className="h-5 w-5" />
    }
  ]

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HelpCircle className="h-5 w-5" />
          功能引导管理
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant={hasCompletedTour ? "default" : "secondary"}>
            {hasCompletedTour ? "已完成主引导" : "未完成主引导"}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={resetTour}
            className="flex items-center gap-1"
          >
            <RotateCcw className="h-4 w-4" />
            重置所有引导
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-3">
          {tourModules.map((module) => {
            const isCompleted = hasSeenModule(module.id)
            
            return (
              <div
                key={module.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${isCompleted ? 'bg-green-100 text-green-600' : 'bg-muted'}`}>
                    {isCompleted ? <CheckCircle className="h-4 w-4" /> : module.icon}
                  </div>
                  <div>
                    <h4 className="font-medium">{module.name}</h4>
                    <p className="text-sm text-muted-foreground">{module.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={isCompleted ? "default" : "outline"}>
                    {isCompleted ? "已完成" : "未开始"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => startTour(module.id)}
                    className="flex items-center gap-1"
                  >
                    <Play className="h-3 w-3" />
                    {isCompleted ? "重新开始" : "开始引导"}
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        <div className="pt-4 border-t">
          <h4 className="font-medium mb-2">引导说明</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• 首次访问时会自动开始主要功能引导</li>
            <li>• 访问不同功能模块时会自动触发相应引导</li>
            <li>• 可以随时重新开始任何引导</li>
            <li>• 引导进度会自动保存</li>
            <li>• 每次点击引导按钮都会从第1步开始</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
