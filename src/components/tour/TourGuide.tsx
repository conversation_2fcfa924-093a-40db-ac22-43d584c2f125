"use client"

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { X, ChevronLeft, ChevronRight, SkipForward } from "lucide-react"
import { cn } from "@/lib/utils"

export interface TourStep {
  id: string
  title: string
  content: string
  target: string // CSS selector for the target element
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  offset?: { x: number; y: number }
  allowClickOutside?: boolean
  showSkip?: boolean
  condition?: 'hasFeatureCards' | 'hasMessages' // 条件显示
  simulateAction?: () => void // 模拟操作函数
}

export interface TourGuideProps {
  steps: TourStep[]
  isOpen: boolean
  onClose: () => void
  onComplete: () => void
  className?: string
}

export function TourGuide({
  steps,
  isOpen,
  onClose,
  onComplete,
  className
}: TourGuideProps) {
  // 检查条件并过滤步骤 - 使用useCallback避免每次渲染都重新创建
  const checkCondition = useCallback((condition?: string): boolean => {
    if (!condition) return true

    switch (condition) {
      case 'hasFeatureCards':
        const featureCards = document.querySelector('[data-tour="feature-cards"]') as HTMLElement
        return featureCards && featureCards.offsetParent !== null
      case 'hasMessages':
        const lastAiMessage = document.querySelector('[data-tour="last-ai-message"]') as HTMLElement
        return lastAiMessage && lastAiMessage.offsetParent !== null
      default:
        return true
    }
  }, [])

  // 过滤符合条件的步骤 - 使用useMemo避免每次渲染都重新计算
  const filteredSteps = useMemo(() => {
    return steps.filter(step => checkCondition(step.condition))
  }, [steps, checkCondition])

  const [currentStep, setCurrentStep] = useState(0)
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const [highlightRect, setHighlightRect] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)

  // 计算目标元素位置和高亮区域
  const calculatePositions = useCallback(() => {
    if (!isOpen || currentStep >= filteredSteps.length) return

    const step = filteredSteps[currentStep]
    const element = document.querySelector(step.target) as HTMLElement

    if (!element) {
      console.warn(`Tour target element not found: ${step.target}`)
      return
    }

    setTargetElement(element)

    const rect = element.getBoundingClientRect()
    const scrollTop = window.scrollY || document.documentElement.scrollTop
    const scrollLeft = window.scrollX || document.documentElement.scrollLeft

    // 检查是否是iframe元素，如果是，需要特殊处理
    const isIframe = element.tagName.toLowerCase() === 'iframe'
    let highlightRect = {
      x: rect.left + scrollLeft,
      y: rect.top + scrollTop,
      width: rect.width,
      height: rect.height
    }

    // 如果是iframe，根据步骤调整高亮区域
    // 基于ChatChart.html的实际布局结构进行精确计算
    if (isIframe && step.offset) {
      const iframeWidth = rect.width
      const iframeHeight = rect.height

      // ChatChart.html布局分析：
      // - header高度约80px
      // - main-content padding 20px
      // - content-wrapper 左右分栏，左侧400px固定宽度，右侧flex:1
      // - 左侧面板：聊天区域
      // - 右侧面板：上传/预览/图表区域

      const headerHeight = 80
      const mainPadding = 20
      const leftPanelWidth = 400
      const gap = 20

      switch (step.id) {
        case 'viz-data-preview':
          // 数据预览区域 - 整个右侧面板
          highlightRect = {
            x: rect.left + scrollLeft + leftPanelWidth + gap,
            y: rect.top + scrollTop + headerHeight + mainPadding,
            width: iframeWidth - leftPanelWidth - gap - 20,
            height: iframeHeight - headerHeight - mainPadding * 2
          }
          break
        case 'viz-chat-panel':
          // 左侧聊天面板 - 固定400px宽度
          highlightRect = {
            x: rect.left + scrollLeft + mainPadding,
            y: rect.top + scrollTop + headerHeight + mainPadding,
            width: leftPanelWidth,
            height: iframeHeight - headerHeight - mainPadding * 2
          }
          break
        case 'viz-chart-generation':
          // 右侧图表区域 - 与数据预览相同区域
          highlightRect = {
            x: rect.left + scrollLeft + leftPanelWidth + gap,
            y: rect.top + scrollTop + headerHeight + mainPadding,
            width: iframeWidth - leftPanelWidth - gap - 20,
            height: iframeHeight - headerHeight - mainPadding * 2
          }
          break
        case 'viz-chart-controls':
          // 图表控制按钮 - 右侧面板的header区域，往下移动一个按钮高度
          highlightRect = {
            x: rect.left + scrollLeft + leftPanelWidth + gap + (iframeWidth - leftPanelWidth - gap) * 0.6,
            y: rect.top + scrollTop + headerHeight + mainPadding + 50,
            width: (iframeWidth - leftPanelWidth - gap) * 0.35,
            height: 50
          }
          break
      }
    }

    // 设置高亮区域（相对于页面）
    setHighlightRect(highlightRect)

    // 计算提示框位置
    const tooltipElement = tooltipRef.current
    if (!tooltipElement) return

    const tooltipRect = tooltipElement.getBoundingClientRect()
    const position = step.position || 'bottom'
    const offset = step.offset || { x: 0, y: 0 }

    let x = 0, y = 0

    // 使用高亮区域的位置来计算提示框位置
    const targetRect = {
      left: highlightRect.x - scrollLeft,
      top: highlightRect.y - scrollTop,
      right: highlightRect.x - scrollLeft + highlightRect.width,
      bottom: highlightRect.y - scrollTop + highlightRect.height,
      width: highlightRect.width,
      height: highlightRect.height
    }

    switch (position) {
      case 'top':
        x = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
        y = targetRect.top - tooltipRect.height - 10
        break
      case 'bottom':
        x = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
        y = targetRect.bottom + 10
        break
      case 'left':
        x = targetRect.left - tooltipRect.width - 10
        y = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = targetRect.right + 10
        y = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2
        break
      case 'center':
        x = window.innerWidth / 2 - tooltipRect.width / 2
        y = window.innerHeight / 2 - tooltipRect.height / 2
        break
    }

    // 应用偏移量
    x += offset.x + scrollLeft
    y += offset.y + scrollTop

    // 确保提示框在视窗内，移动端使用更大的边距
    const isMobile = window.innerWidth < 768
    const padding = isMobile ? 16 : 10
    const maxWidth = window.innerWidth - padding * 2

    // 在移动端，如果提示框太宽，调整到屏幕中央
    if (isMobile && tooltipRect.width > maxWidth * 0.9) {
      x = padding
    } else {
      x = Math.max(padding, Math.min(x, window.innerWidth - tooltipRect.width - padding))
    }

    y = Math.max(padding, Math.min(y, window.innerHeight - tooltipRect.height - padding))

    setTooltipPosition({ x, y })
  }, [isOpen, currentStep, filteredSteps])

  // 监听窗口大小变化和滚动
  useEffect(() => {
    if (!isOpen) return

    calculatePositions()

    const handleResize = () => calculatePositions()
    const handleScroll = () => calculatePositions()

    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [calculatePositions, isOpen])

  // 当引导打开时，重置到第一步
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0)
    }
  }, [isOpen])

  // 当步骤改变时，执行模拟操作（除了最后一步）
  useEffect(() => {
    if (isOpen && filteredSteps.length > 0 && currentStep < filteredSteps.length) {
      const currentStepData = filteredSteps[currentStep]
      const isLastStep = currentStep === filteredSteps.length - 1

      // 最后一步的模拟操作在点击完成时执行，不在进入时执行
      if (currentStepData?.simulateAction && !isLastStep) {
        // 延迟执行模拟操作，确保界面已经渲染
        setTimeout(() => {
          currentStepData.simulateAction!()
        }, 500)
      }
    }
  }, [isOpen, currentStep, filteredSteps])

  // 滚动到目标元素（暂时未使用，保留以备后续功能扩展）
  // const scrollToTarget = useCallback(() => {
  //   if (!targetElement) return
  //
  //   targetElement.scrollIntoView({
  //     behavior: 'smooth',
  //     block: 'center',
  //     inline: 'center'
  //   })
  // }, [targetElement])

  // 下一步
  const handleNext = () => {
    if (currentStep < filteredSteps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      // 在最后一步点击完成时执行模拟操作
      const lastStepData = filteredSteps[currentStep]
      if (lastStepData?.simulateAction) {
        lastStepData.simulateAction()
      }
      onComplete()
    }
  }

  // 上一步
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // 跳过引导
  const handleSkip = () => {
    onClose()
  }

  // 处理遮罩点击
  const handleOverlayClick = () => {
    const step = filteredSteps[currentStep]
    if (step?.allowClickOutside) {
      onClose()
    }
  }

  // 阻止事件冒泡
  const handleTooltipClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  if (!isOpen || currentStep >= filteredSteps.length) {
    return null
  }

  const step = filteredSteps[currentStep]
  const isFirstStep = currentStep === 0
  const isLastStep = currentStep === filteredSteps.length - 1

  return (
    <div className={cn("fixed inset-0 z-[9999]", className)}>
      {/* 遮罩层 */}
      <div 
        className="absolute inset-0 bg-black/50 transition-opacity duration-300"
        onClick={handleOverlayClick}
      />
      
      {/* 高亮区域 */}
      <div
        className="absolute border-2 border-blue-500 rounded-lg shadow-lg transition-all duration-300"
        style={{
          left: highlightRect.x - 4,
          top: highlightRect.y - 4,
          width: highlightRect.width + 8,
          height: highlightRect.height + 8,
          boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
          zIndex: 10000
        }}
      />

      {/* 提示框 */}
      <Card
        ref={tooltipRef}
        className="absolute bg-white shadow-xl border-0 max-w-sm w-80 md:w-80 sm:w-72 sm:max-w-[calc(100vw-2rem)] z-[10001]"
        style={{
          left: tooltipPosition.x,
          top: tooltipPosition.y,
        }}
        onClick={handleTooltipClick}
      >
        <CardContent className="p-4">
          {/* 头部 */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                {currentStep + 1} / {filteredSteps.length}
              </span>
              {step.showSkip !== false && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSkip}
                  className="text-xs text-gray-500 hover:text-gray-700 p-1 h-auto"
                >
                  <SkipForward className="h-3 w-3 mr-1" />
                  跳过
                </Button>
              )}
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6 text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* 内容 */}
          <div className="mb-4">
            <h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
            <p className="text-sm text-gray-600 leading-relaxed">{step.content}</p>
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevious}
              disabled={isFirstStep}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="h-4 w-4" />
              上一步
            </Button>
            
            <Button
              onClick={handleNext}
              size="sm"
              className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700"
            >
              {isLastStep ? '完成' : '下一步'}
              {!isLastStep && <ChevronRight className="h-4 w-4" />}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default TourGuide
