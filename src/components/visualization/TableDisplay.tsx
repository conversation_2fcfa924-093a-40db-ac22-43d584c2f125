"use client"

import React, { useRef } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ChartBar, Maximize, Minimize } from 'lucide-react';
import styles from './visualization.module.css';

interface TableDisplayProps {
  headers: string[];
  data: Record<string, any>[];
  title?: string;
  onToggleChartView?: () => void;
}

const TableDisplay: React.FC<TableDisplayProps> = ({
  headers,
  data,
  title = "Excel数据预览",
  onToggleChartView
}) => {
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 切换全屏/正常显示模式
  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  return (
    <div
      ref={containerRef}
      className={`${styles.chartContainer} ${isFullscreen ? styles.fullscreen : ''}`}
      style={{
        width: '100%',
        maxWidth: isFullscreen ? '100vw' : '100%',
        overflow: isFullscreen ? 'auto' : 'hidden',
        height: isFullscreen ? '100vh' : 'auto',
        position: isFullscreen ? 'fixed' : 'relative',
        zIndex: isFullscreen ? 100 : 'auto'
      }}
    >
      <div className={isFullscreen ? styles.fullscreenControls : styles.chartControls}>
        {onToggleChartView && (
          <Button
            variant="outline"
            size="icon"
            onClick={onToggleChartView}
            title="切换到图表视图"
            className={isFullscreen ? "h-8 w-8" : "h-7 w-7"}
          >
            <ChartBar className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          </Button>
        )}
        <Button
          variant="outline"
          size="icon"
          onClick={toggleFullscreen}
          title={isFullscreen ? "退出全屏" : "全屏显示"}
          className={isFullscreen ? "h-8 w-8" : "h-7 w-7"}
        >
          {isFullscreen ? (
            <Minimize className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          ) : (
            <Maximize className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          )}
        </Button>
      </div>

      <div className={styles.tableContainer} style={{ width: '100%', height: isFullscreen ? 'calc(100vh - 40px)' : '100%' }}>
        <div className="p-4 border-b border-border">
          <h3 className="text-lg font-medium">{title}</h3>
          <p className="text-sm text-muted-foreground">
            共 {data.length} 行数据，{headers.length} 列
            <span className="ml-2 text-xs text-muted-foreground">(提示：可横向滚动查看更多列)</span>
          </p>
        </div>
        <div className={styles.tableScrollArea} style={{
          maxHeight: isFullscreen ? 'calc(100vh - 120px)' : '500px',
          height: isFullscreen ? 'calc(100vh - 120px)' : '500px',
          width: '100%',
          overflowX: 'auto',
          overflowY: 'auto'
        }}>
          <div className="min-w-max" style={{ width: 'max-content' }}>
            <Table style={{ width: 'max-content' }}>
              <TableHeader className="sticky top-0 bg-card z-10">
                <TableRow>
                  <TableHead className="w-[60px] text-center sticky left-0 bg-card z-20 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">#</TableHead>
                  {headers.map((header, index) => (
                    <TableHead key={index} className="min-w-[150px]">{header}</TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.slice(0, 50).map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="hover:bg-muted/30">
                    <TableCell className="text-center font-medium sticky left-0 bg-card z-10 shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">{rowIndex + 1}</TableCell>
                    {headers.map((header, colIndex) => (
                      <TableCell key={colIndex}>
                        {row[header] !== null && row[header] !== undefined
                          ? String(row[header])
                          : ''}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableDisplay;
