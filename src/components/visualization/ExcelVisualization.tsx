"use client"

import React, { useState, useEffect, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import * as XLSX from 'xlsx';
import VisualizationControls from './VisualizationControls';
import ChartDisplay from './ChartDisplay';
import TableDisplay from './TableDisplay';
import {
  AggregationType,
  ChartDataConfig,
  ChartType,
  DataFilter,
  ProportionFilter,
  SortFilterType,
  applyDataFilters,
  generateColors,
  inferColumnTypes
} from './VisualizationUtils';
import { FileSpreadsheet } from 'lucide-react';

const ExcelVisualization: React.FC = () => {
  // 状态变量
  const [file, setFile] = useState<File | null>(null);
  const [headers, setHeaders] = useState<string[]>([]);
  const [jsonData, setJsonData] = useState<Record<string, any>[]>([]);
  const [chartData, setChartData] = useState<ChartDataConfig | null>(null);
  const [chartType, setChartType] = useState<ChartType>('bar');
  const [chartTitle, setChartTitle] = useState<string>('数据可视化');
  const [showTable, setShowTable] = useState<boolean>(false);
  const [dataFilters, setDataFilters] = useState<DataFilter[]>([]);
  const [proportionFilter, setProportionFilter] = useState<ProportionFilter | null>(null);
  const [showDataLabels, setShowDataLabels] = useState<boolean>(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件上传
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) {
      toast.error('未选择文件。');
      return;
    }

    // 检查文件类型是否为Excel格式
    const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
    if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
      toast.error('上传失败：请上传Excel格式文件（.xlsx或.xls）。');
      // 重置文件输入框
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    setFile(selectedFile);

    // 重置状态
    setChartData(null);
    setDataFilters([]);
    setProportionFilter(null);
    setShowTable(false);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const data = new Uint8Array(arrayBuffer);
      const workbook = XLSX.read(data, { type: 'array', cellDates: true });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      const dataArray = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null }) as any[][];

      if (dataArray.length < 2) {
        toast.error('Excel 文件为空或表头缺失。');
        return;
      }

      const fileHeaders = dataArray[0].map(String);
      setHeaders(fileHeaders);

      const data_json = dataArray.slice(1).map(row => {
        let obj: Record<string, any> = {};
        fileHeaders.forEach((header, index) => {
          obj[header] = row[index];
        });
        return obj;
      });
      setJsonData(data_json);

      // 只显示成功消息
      toast.success(`成功解析 "${selectedFile.name}"`);
    } catch (error) {
      console.error("文件解析错误:", error);
      toast.error(`文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 处理生成图表
  const handleGenerateChart = (
    xAxis: string,
    yAxis: string,
    chartType: ChartType,
    aggregation: AggregationType,
    sortFilter: SortFilterType,
    customTopN: number,
    binCount: number,
    showDataLabels: boolean,
    customResultType?: string | null,
    dataFilters: DataFilter[] = [],
    proportionFilter?: ProportionFilter | null
  ) => {
    if (jsonData.length === 0) {
      toast.error('请先上传并解析 Excel 文件。');
      return;
    }

    // 对于proportion_summary类型，我们不需要检查Y轴
    const isProportion = customResultType === 'proportion_summary' || (proportionFilter && chartType === 'pie');

    if (!xAxis || (chartType !== 'histogram' && !isProportion && !yAxis)) {
      let msg = '请选择X轴';
      if (chartType !== 'histogram' && !isProportion) {
        msg += '和Y轴。';
      } else {
        msg += '。';
      }
      toast.error(msg);
      return;
    }

    if (chartType !== 'histogram' && !isProportion && xAxis === yAxis &&
        ['bar', 'line', 'scatter'].includes(chartType) && aggregation === 'none') {
      toast.error('对于此类图表和无聚合，X轴和Y轴不能相同。');
      return;
    }

    // 确定结果类型
    const resultType = customResultType || (proportionFilter && chartType === 'pie' ? 'proportion_summary' : null);

    // 构建标题
    let title = `${xAxis} ${(chartType === 'histogram' || (chartType === 'pie' && resultType === 'proportion_summary'))
      ? (chartType === 'histogram' ? '分布' : '比例')
      : 'vs ' + yAxis}`;

    if (resultType === 'proportion_summary' && proportionFilter) {
      title = `${proportionFilter.filterColumn} 比例分析`;
    }

    console.log('图表生成参数:', {
      xAxis,
      yAxis,
      chartType,
      aggregation,
      sortFilter,
      customTopN,
      binCount,
      resultType,
      dataFilters,
      proportionFilter
    });

    setChartTitle(title);
    setChartType(chartType);
    setShowDataLabels(showDataLabels);
    setShowTable(false);

    const chartDataConfig = prepareChartData(
      jsonData,
      xAxis,
      yAxis,
      chartType,
      aggregation,
      sortFilter,
      customTopN,
      binCount,
      dataFilters,
      proportionFilter,
      resultType
    );

    if (chartDataConfig) {
      // 添加x轴和y轴标题到chartDataConfig
      chartDataConfig.xAxisTitle = xAxis;
      chartDataConfig.yAxisTitle = yAxis === '' && chartType === 'histogram' ? '频率' : yAxis;

      setChartData(chartDataConfig);
      let messageText = `已生成${chartType === 'bar' ? '柱状图' :
                         chartType === 'line' ? '折线图' :
                         chartType === 'pie' ? '饼图' :
                         chartType === 'doughnut' ? '甜甜圈图' :
                         chartType === 'scatter' ? '散点图' : '直方图'}。`;

      let activeFiltersForMessage = [];
      if (dataFilters && dataFilters.length > 0) {
        activeFiltersForMessage.push(dataFilters.map(f => `"${f.column}" ${f.operator} "${f.value}"`).join(' AND '));
      }
      if (proportionFilter) {
        activeFiltersForMessage.push(`比例筛选: "${proportionFilter.filterColumn}" ${proportionFilter.operator} "${proportionFilter.value}"`);
      }
      if (activeFiltersForMessage.length > 0) {
        messageText += ` (已应用筛选: ${activeFiltersForMessage.join('; ')})`;
      }

      // toast.success(messageText);
      toast.success('图表生成成功');
    } else {
      toast.error('无法为此选择生成图表数据 (可能数据类型不匹配、聚合、筛选或排序结果为空)。');
    }
  };

  // 处理获取LLM建议
  const handleGetLLMSuggestion = async (query: string) => {
    // 固定的API Key
    const apiKey = 'c2stbWVuZ2NoYXQtYm90IQ==';

    if (jsonData.length === 0) {
      toast.error('请先上传并解析 Excel 文件。');
      return;
    }

    // 显示加载提示
    toast.info('正在向 MengChat 请求图表建议...');

    const sampleData = jsonData.slice(0, 10);
    const columnTypes = inferColumnTypes(headers, sampleData);

    // 构建提示词
    let prompt = `You are an expert data visualization assistant.
Based on the following dataset information and user query, provide the best chart suggestion in JSON format.

Dataset Information:
- Columns and Detected Types:
${headers.map(h => `  - "${h}": "${columnTypes[h] || 'Unknown'}"`).join('\n')}
- Sample Data (first ${sampleData.length} rows, only showing a few columns if many):
${JSON.stringify(sampleData.map(row => {
    const limitedRow: Record<string, any> = {};
    headers.slice(0, 5).forEach(h => limitedRow[h] = row[h]);
    return limitedRow;
}), null, 2)}

User Query: "${query || 'No specific query, suggest a generally useful visualization.'}"

Your JSON output MUST be a single, valid JSON object with the following keys:
- "chartType": string (e.g., "bar", "line", "pie", "scatter", "histogram")
- "resultType": string (optional, e.g., "proportion_summary" if the query is about proportion after a filter)
- "xAxis": string (column name for X-axis; for histogram, this is the numeric column to bin; for proportion_summary, this might be descriptive categories like "符合条件", "不符合条件")
- "yAxis": string (column name for Y-axis; for histogram or proportion_summary, this is usually "Frequency" or "Count" and not directly from a column)
- "aggregation": string (e.g., "sum", "average", "count", "none"; for histogram/proportion_summary, this is implicitly "count")
- "sortFilter": string (e.g., "none", "asc", "desc", "top1", "top3", "top5", "top10", "topN_custom"; usually "none" for histogram/proportion_summary)
- "topNValue": number (integer, only if sortFilter is "topN_custom")
- "binCount": number (integer, optional, suggested number of bins if chartType is "histogram", e.g., 10)
- "dataFilters": array of objects (optional, for filtering data before aggregation. Each object: {"column": "string", "operator": "equals" or "contains", "value": "string/number"})
- "proportionFilter": object (optional, if resultType is "proportion_summary". Structure: {"filterColumn": "Name of the column to be filtered", "operator": "greaterThan" | "lessThan" | "equals" | "greaterThanOrEquals" | "lessThanOrEquals", "value": "number_or_string"}, "labelForMatch": "string", "labelForNonMatch": "string"})
- "reasoning": string (optional, use of chinese)
- "error": string (optional, use of chinese)

If user asks for a distribution of a single numeric column (e.g., "distribution of Sales"), suggest "histogram".
If user asks for a proportion based on a condition (e.g., "percentage of employees with salary > 10000"), set resultType to "proportion_summary", chartType to "pie", and fill "proportionFilter".
For histogram, "xAxis" is the numeric column, "yAxis" can be "Frequency", "aggregation" is "count" (by bin), "sortFilter" is "none".
For proportion_summary, "xAxis" will be the labels from proportionFilter, "yAxis" is "Count", "aggregation" is "count".
`;

    // API URL
    const API_URL = process.env.NEXT_PUBLIC_API_URL + '/chart/llm';

    try {
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: query,
          prompt: prompt,
          temperature: 0.2,
          max_tokens: 4096,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('LLM API Error:', errorData);
        throw new Error(`LLM API 请求失败: ${response.status} ${response.statusText}. ${errorData?.error?.message || ''}`);
      }

      const responseData = await response.json();

      // 解析返回的JSON
      let suggestionJsonText = responseData.data.result;
      suggestionJsonText = suggestionJsonText.replace(/^```json\s*|```$/g, '').trim();
      console.log("LLM Suggestion JSON:", suggestionJsonText);

      // 解析JSON
      const suggestion = JSON.parse(suggestionJsonText);
      if (suggestion.error) {
        toast.info(`LLM 提示: ${suggestion.error}`);
        setDataFilters([]);
        setProportionFilter(null);
        return;
      }

      // 应用LLM建议
      let xAxis = '';
      let yAxis = '';
      let newChartType: ChartType = 'bar';
      let aggregation: AggregationType = 'none';
      let sortFilter: SortFilterType = 'none';
      let customTopN = 5;
      let binCount = 10;

      // 设置X轴
      if (headers.includes(suggestion.xAxis)) {
        xAxis = suggestion.xAxis;
      } else if (headers.length > 0) {
        xAxis = headers[0];
      }

      // 根据图表类型设置Y轴和其他参数
      if (suggestion.chartType === 'histogram') {
        newChartType = 'histogram';
        yAxis = '';
        aggregation = 'count';
        sortFilter = 'none';
        if (suggestion.binCount) binCount = suggestion.binCount;
      } else if (suggestion.resultType === 'proportion_summary' && suggestion.chartType === 'pie') {
        newChartType = 'pie';
        yAxis = '';
        aggregation = 'count';
        sortFilter = 'none';
      } else {
        if (suggestion.chartType && ['bar', 'line', 'pie', 'doughnut', 'scatter'].includes(suggestion.chartType)) {
          newChartType = suggestion.chartType as ChartType;
        }

        if (headers.includes(suggestion.yAxis)) {
          yAxis = suggestion.yAxis;
        } else if (headers.length > 1) {
          yAxis = headers[1];
        } else if (headers.length > 0) {
          yAxis = headers[0];
        }

        // if (suggestion.aggregation && suggestion.chartType !== 'histogram' && suggestion.resultType !== 'proportion_summary') {
        if (suggestion.aggregation && suggestion.chartType !== 'histogram') {
          aggregation = suggestion.aggregation as AggregationType;
        }

        // if (suggestion.sortFilter && suggestion.chartType !== 'histogram' && suggestion.resultType !== 'proportion_summary') {
        if (suggestion.sortFilter && suggestion.chartType !== 'histogram') {
          sortFilter = suggestion.sortFilter as SortFilterType;
          if (sortFilter === 'topN_custom' && suggestion.topNValue) {
            customTopN = suggestion.topNValue;
          }
        }
      }

      // 设置数据筛选器和比例筛选器
      const newDataFilters = suggestion.dataFilters || [];
      const newProportionFilter = suggestion.proportionFilter || null;

      // 更新状态
      setDataFilters(newDataFilters);
      setProportionFilter(newProportionFilter);
      setShowTable(false);

      // 构建图表标题
      let chartTitleText = `${suggestion.xAxis || ''} ${(suggestion.chartType === 'histogram' || suggestion.resultType === 'proportion_summary')
        ? (suggestion.chartType === 'histogram' ? '分布' : '比例')
        : ('vs ' + (suggestion.yAxis || ''))}`;

      if (suggestion.resultType === 'proportion_summary' && suggestion.proportionFilter) {
        chartTitleText = `${suggestion.proportionFilter.filterColumn} 比例分析`;
      }

      // 关闭加载提示
      toast.dismiss();
      toast.success(`LLM 建议已应用`);

      // 记录图表生成参数，便于调试
      console.log('图表生成参数:', {
        xAxis,
        yAxis,
        chartType: newChartType,
        aggregation,
        sortFilter,
        customTopN,
        binCount,
        dataFilters: newDataFilters,
        proportionFilter: newProportionFilter,
        resultType: suggestion.resultType
      });

      // 生成图表
      handleGenerateChart(
        xAxis,
        yAxis,
        newChartType,
        aggregation,
        sortFilter,
        customTopN,
        binCount,
        showDataLabels,
        suggestion.resultType,
        newDataFilters,
        newProportionFilter
      );

      // 设置图表标题
      setChartTitle(chartTitleText);

    } catch (error) {
      console.error("LLM 请求或解析错误:", error);
      toast.error(`LLM 交互错误: ${error instanceof Error ? error.message : '未知错误'}`);
      setDataFilters([]);
      setProportionFilter(null);
    }
  };

  // 处理清除条件
  const handleClearConditions = () => {
    setChartData(null);
    setDataFilters([]);
    setProportionFilter(null);
    toast.info('已清除所有图表生成条件');
  };

  // 准备图表数据
  const prepareChartData = (
    data: Record<string, any>[],
    xKey: string,
    yKey: string,
    chartType: ChartType,
    aggregation: AggregationType = 'none',
    sortFilter: SortFilterType = 'none',
    customTopN: number = 5,
    binCount: number = 10,
    dataFilters: DataFilter[] = [],
    proportionFilter: ProportionFilter | null = null,
    resultType: string | null = null
  ): ChartDataConfig | null => {
    if (!data || data.length === 0) return null;
    let baseData = applyDataFilters(data, dataFilters);
    if (baseData.length === 0 && dataFilters.length > 0) {
      toast.info('应用常规筛选条件后无可用数据。');
      return null;
    }

    // 初始化 suggestedYMax
    let suggestedYMax: number | undefined = undefined;

    // 处理比例汇总（饼图）
    if (resultType === 'proportion_summary' && chartType === 'pie' && proportionFilter) {
      const { filterColumn, operator, value, labelForMatch = '符合条件', labelForNonMatch = '不符合条件' } = proportionFilter;
      if (!filterColumn || !operator || value === undefined) {
        toast.error('比例筛选参数不完整。');
        return null;
      }

      // 如果应用了数据筛选器，但筛选后数据为空，我们仍然可以显示一个空的比例图
      if (baseData.length === 0) {
        console.log('应用筛选后数据为空，但仍然显示比例图');
        return {
          labels: [labelForMatch, labelForNonMatch],
          datasets: [{
            data: [0, 0],
            backgroundColor: generateColors(2, 0.8),
            hoverOffset: 4
          }]
        };
      }

      let matchingCount = 0;
      let nonMatchingCount = 0;
      let totalProcessed = 0;

      // 处理原始数据，不应用额外的筛选
      // 确保我们使用完整的数据集，而不是已经筛选过的数据
      const dataToProcess = jsonData;
      dataToProcess.forEach(row => {
        const cellValue = row[filterColumn];
        if (cellValue === undefined || cellValue === null) {
          nonMatchingCount++;
          return;  // 跳过此行
        }
        let match = false;
        const numericCellValue = parseFloat(String(cellValue).replace(/[, $£€]/g, ''));
        const numericFilterValue = parseFloat(String(value).replace(/[, $£€]/g, ''));

        if (!isNaN(numericCellValue) && !isNaN(numericFilterValue)) {
          switch (operator) {
            case 'greaterThan': match = numericCellValue > numericFilterValue; break;
            case 'lessThan': match = numericCellValue < numericFilterValue; break;
            case 'equals': match = numericCellValue === numericFilterValue; break;
            case 'greaterThanOrEquals': match = numericCellValue >= numericFilterValue; break;
            case 'lessThanOrEquals': match = numericCellValue <= numericFilterValue; break;
            default: nonMatchingCount++; return;
          }
        } else {
          const cellValueStr = String(cellValue).toLowerCase();
          const filterValueStr = String(value).toLowerCase();
          switch (operator) {
            case 'equals': match = cellValueStr === filterValueStr; break;
            case 'contains': match = cellValueStr.includes(filterValueStr); break;
            default: nonMatchingCount++; return;
          }
        }
        if (match) matchingCount++; else nonMatchingCount++;
        totalProcessed++;
      });

      console.log(`比例图统计: 匹配=${matchingCount}, 不匹配=${nonMatchingCount}, 总计=${totalProcessed}`);
      // 即使没有匹配的数据，也返回图表
      return {
        labels: [labelForMatch, labelForNonMatch],
        datasets: [{
          data: [matchingCount, nonMatchingCount],
          backgroundColor: generateColors(2, 0.8),
          hoverOffset: 4
        }]
        // suggestedYMax 不适用于饼图
      };
    }

    // 处理直方图
    if (chartType === 'histogram') {
      const values = baseData
        .map(item => parseFloat(String(item[xKey]).replace(/[, $£€]/g, '')))
        .filter(v => !isNaN(v));

      if (values.length === 0) {
        toast.error('选择的列没有有效的数值数据用于生成直方图。');
        return null;
      }

      values.sort((a, b) => a - b);
      const minVal = values[0];
      const maxVal = values[values.length - 1];
      let numBins = binCount;

      if (isNaN(numBins) || numBins < 2) {
        numBins = Math.max(2, Math.ceil(Math.sqrt(values.length)));
      }

      if (minVal === maxVal) numBins = 1;
      const binWidth = numBins === 1 ? 1 : (maxVal - minVal) / numBins;
      const bins = new Array(numBins).fill(0);
      const binLabels = new Array(numBins);

      for (let i = 0; i < numBins; i++) {
        const binStart = minVal + i * binWidth;
        const binEnd = minVal + (i + 1) * binWidth;
        if (numBins === 1) {
          binLabels[i] = `${binStart.toLocaleString(undefined, {maximumFractionDigits:1})}`;
        } else if (i === numBins - 1) {
          binLabels[i] = `${binStart.toLocaleString(undefined, {maximumFractionDigits:1})} - ${maxVal.toLocaleString(undefined, {maximumFractionDigits:1})}`;
        } else {
          binLabels[i] = `${binStart.toLocaleString(undefined, {maximumFractionDigits:1})} - ${binEnd.toLocaleString(undefined, {maximumFractionDigits:1})}`;
        }
      }

      if (numBins === 1 && minVal === maxVal) {
        bins[0] = values.length;
      } else {
        values.forEach(value => {
          if (value === maxVal) {
            bins[numBins - 1]++;
          } else {
            const binIndex = Math.floor((value - minVal) / binWidth);
            if (binIndex >= 0 && binIndex < numBins) bins[binIndex]++;
          }
        });
      }

      return {
        labels: binLabels,
        datasets: [{
          label: `频率 (${xKey})`,
          data: bins,
          backgroundColor: generateColors(numBins, 0.75),
          borderColor: generateColors(numBins, 1),
          borderWidth: 1
        }],
        // suggestedYMax 不适用于直方图的频率轴
      };
    }

    // 处理其他图表类型
    let processedData = [...baseData];

    if (['bar', 'line', 'scatter', 'pie', 'doughnut'].includes(chartType)) {
      processedData.forEach(item => {
        const val = String(item[yKey]).replace(/[, $£€]/g, '');
        item[yKey] = parseFloat(val);
      });
    }

    if (chartType === 'scatter') {
      processedData.forEach(item => {
        const val = String(item[xKey]).replace(/[, $£€]/g, '');
        item[xKey] = parseFloat(val);
      });
    }

    let workingData: { label: string; value: number }[] = [];

    if (aggregation !== 'none' && chartType !== 'scatter') {
      const aggregatedMap = new Map<string, { sum: number; count: number; values: number[] }>();

      processedData.forEach(item => {
        const xValue = String(item[xKey]);
        const yValue = item[yKey];

        if (xValue === undefined || yValue === undefined || (typeof yValue === 'number' && isNaN(yValue))) return;

        if (!aggregatedMap.has(xValue)) {
          aggregatedMap.set(xValue, { sum: 0, count: 0, values: [] });
        }

        const entry = aggregatedMap.get(xValue)!;
        if (typeof yValue === 'number') {
          entry.sum += yValue;
          entry.values.push(yValue);
        }
        entry.count++;
      });

      aggregatedMap.forEach((aggValue, label) => {
        let finalValue;
        switch (aggregation) {
          case 'sum': finalValue = aggValue.sum; break;
          case 'average': finalValue = aggValue.count > 0 ? aggValue.sum / aggValue.count : 0; break;
          case 'count': finalValue = aggValue.count; break;
          case 'min': finalValue = aggValue.values.length > 0 ? Math.min(...aggValue.values) : 0; break;
          case 'max': finalValue = aggValue.values.length > 0 ? Math.max(...aggValue.values) : 0; break;
          default: finalValue = aggValue.sum;
        }
        workingData.push({ label: label, value: finalValue });
      });

      if (workingData.length === 0) return null;
    } else {
      if (chartType === 'scatter') {
        const scatterPoints = processedData
          .map(item => ({
            x: item[xKey],
            y: item[yKey]
          }))
          .filter(p => typeof p.x === 'number' && !isNaN(p.x) && typeof p.y === 'number' && !isNaN(p.y));

        if (scatterPoints.length === 0) return null;

        // 计算散点图的 suggestedYMax
        const yValuesScatter = scatterPoints.map(p => p.y);
        if (yValuesScatter.length > 0) {
          const maxValScatter = Math.max(...yValuesScatter);
          if (isFinite(maxValScatter)) {
             suggestedYMax = Math.ceil(maxValScatter > 0 ? maxValScatter * 1.1 : (maxValScatter === 0 ? 10 : maxValScatter * 0.9));
          }
        }

        return {
          datasets: [{
            label: `${yKey} vs ${xKey}`,
            data: scatterPoints,
            backgroundColor: 'rgba(255, 99, 132, 0.7)',
            borderColor: 'rgba(255, 99, 132, 1)',
          }],
          suggestedYMax // 添加到返回对象
        };
      } else {
        const uniqueLabels = [...new Set(processedData.map(item => String(item[xKey])))];
        workingData = uniqueLabels.map(label => {
          const foundItem = processedData.find(item => String(item[xKey]) === label);
          const yValue = foundItem ? foundItem[yKey] : NaN;
          return {
            label: label,
            value: (typeof yValue === 'number' && !isNaN(yValue) ? yValue : 0)
          };
        });
      }
    }

    // 对于非散点图，应用排序和筛选
    if (chartType !== 'scatter' as ChartType) {
      if (sortFilter === 'asc') {
        workingData.sort((a, b) => a.value - b.value);
      } else if (sortFilter === 'desc' || sortFilter.startsWith('top')) {
        workingData.sort((a, b) => b.value - a.value);
      }

      if (sortFilter === 'top1') {
        workingData = workingData.slice(0, 1);
      } else if (sortFilter === 'top3') {
        workingData = workingData.slice(0, 3);
      } else if (sortFilter === 'top5') {
        workingData = workingData.slice(0, 5);
      } else if (sortFilter === 'top10') {
        workingData = workingData.slice(0, 10);
      } else if (sortFilter === 'topN_custom') {
        if (customTopN > 0) {
          workingData = workingData.slice(0, customTopN);
        }
      }

      if (workingData.length === 0 && sortFilter.startsWith('top')) return null;
    }

    const finalLabels = workingData.map(d => d.label);
    const finalValues = workingData.map(d => d.value);

    if (chartType === 'bar' || chartType === 'line') {
      // 计算条形图/折线图的 suggestedYMax
      if (finalValues.length > 0) {
        const maxVal = Math.max(...finalValues);
        if (isFinite(maxVal)) {
          suggestedYMax = Math.ceil(maxVal > 0 ? maxVal * 1.1 : (maxVal === 0 ? 10 : maxVal * 0.9));
        }
      }
      return {
        labels: finalLabels,
        datasets: [{
          label: `${yKey} (${aggregation === 'none' ? '原始值' : aggregation})`,
          data: finalValues,
          backgroundColor: chartType === 'bar' ? generateColors(finalLabels.length, 0.7) : 'rgba(54, 162, 235, 0.6)',
          borderColor: chartType === 'bar' ? generateColors(finalLabels.length, 1) : 'rgba(54, 162, 235, 1)',
          borderWidth: 1.5,
          fill: chartType === 'line' ? false : undefined,
          tension: chartType === 'line' ? 0.1 : undefined
        }],
        suggestedYMax // 添加到返回对象
      };
    } else if (chartType === 'pie' || chartType === 'doughnut') {
      return {
        labels: finalLabels,
        datasets: [{
          data: finalValues,
          backgroundColor: generateColors(finalLabels.length, 0.8),
          hoverOffset: 4
        }],
        // suggestedYMax 不适用于饼图/甜甜圈图
      };
    }

    return null;
  };

  return (
    <div className="flex flex-col md:flex-row gap-6 p-4 md:p-6 w-full max-w-[1600px] mx-auto" style={{ minWidth: 0 }}>
      {/* 左侧面板 - 固定宽度和高度 */}
      <div className="w-full md:w-[450px] flex-shrink-0 flex flex-col gap-6 md:h-[600px] overflow-auto">
        {/* 文件上传区域 */}
        <Card>
          <CardContent className="pt-6">
            <Label htmlFor="excelFile" className="block mb-2">
              上传Excel文件:
            </Label>
            <div className="flex items-center gap-2">
              <Input
                ref={fileInputRef}
                id="excelFile"
                type="file"
                accept=".xlsx"
                onChange={handleFileChange}
                className="hidden"
              />
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="w-full flex items-center justify-center gap-2 h-20 border-dashed border-primary/50 hover:border-primary"
              >
                <FileSpreadsheet className="h-6 w-6 text-primary" />
                <div className="flex flex-col items-start">
                  <span>选择Excel文件</span>
                  <span className="text-xs text-muted-foreground">
                    {file ? file.name : '支持.xlsx和.xls格式'}
                  </span>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 控制面板 */}
        {headers.length > 0 && (
          <VisualizationControls
            headers={headers}
            onGenerateChart={handleGenerateChart}
            onGetLLMSuggestion={handleGetLLMSuggestion}
            onClearConditions={handleClearConditions}
            dataFilters={dataFilters}
            proportionFilter={proportionFilter}
          />
        )}
      </div>

      {/* 右侧面板 - 可滚动区域 */}
      <div className="flex-1 min-h-[600px] flex flex-col w-full md:max-w-[calc(100%-450px-1.5rem)]"
        style={{
          minWidth: 0,
          overflow: 'hidden', // 改为hidden，让内部组件处理滚动
          position: 'relative'
        }}>
        {chartData ? (
          showTable ? (
            <TableDisplay
              headers={headers}
              data={jsonData}
              title="Excel数据预览（前50行）"
              onToggleChartView={() => setShowTable(false)}
            />
          ) : (
            <ChartDisplay
              chartData={chartData}
              chartType={chartType}
              title={chartTitle}
              showDataLabels={showDataLabels}
              onToggleTableView={() => setShowTable(true)}
            />
          )
        ) : jsonData.length > 0 ? (
          <TableDisplay
            headers={headers}
            data={jsonData}
            title="Excel数据预览（前50行）"
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-muted/20 rounded-lg border border-border">
            <div className="text-center p-6">
              <FileSpreadsheet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">暂无数据</h3>
              <p className="text-muted-foreground mb-4">
                上传Excel文件后，将在此处显示数据预览
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExcelVisualization;
