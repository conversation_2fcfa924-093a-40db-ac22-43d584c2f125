"use client"

import React, { useRef, useState, useEffect } from 'react';
import { Chart, registerables, ChartType as ChartJsType } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { ChartDataConfig, ChartType } from './VisualizationUtils';
import styles from './visualization.module.css';
import { Button } from "@/components/ui/button";
import { Download, Maximize, Minimize, Table2 } from 'lucide-react';

// 注册Chart.js组件和插件
Chart.register(...registerables, ChartDataLabels);

interface ChartDisplayProps {
  chartData: ChartDataConfig | null;
  chartType: ChartType;
  title: string;
  showDataLabels: boolean;
  onToggleTableView?: () => void;
}

const ChartDisplay: React.FC<ChartDisplayProps> = ({
  chartData,
  chartType,
  title,
  showDataLabels,
  onToggleTableView
}) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstanceRef = useRef<Chart | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 切换全屏/正常显示模式
  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  // 下载图表为图片
  const downloadChart = () => {
    if (!chartRef.current) return;

    try {
      // 创建一个临时链接元素
      const link = document.createElement('a');

      // 设置下载文件名
      const fileName = `图表_${new Date().toISOString().slice(0, 10)}.png`;
      link.download = fileName;

      // 将canvas转换为PNG图片URL
      link.href = chartRef.current.toDataURL('image/png');

      // 模拟点击下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下载图表失败:', error);
    }
  };

  // 渲染图表
  useEffect(() => {
    if (!chartRef.current || !chartData) return;

    // 如果已有图表实例，先销毁
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // 配置图表选项
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: title,
          font: { size: 16 },
          padding: {
            top: 30, // 增加顶部间距，为按钮留出空间
            bottom: 10,
            right: 80 // 为右侧按钮组预留空间，确保标题居中感
          },
          position: 'top' as const // 明确标题位置
        },
        legend: {
          position: (chartType === 'pie' || chartType === 'doughnut' || chartType === 'histogram') ? 'top' : 'bottom' as const
        },
        tooltip: {
          callbacks: {
            label: function(context: any) {
              // 处理散点图
              if (chartType === 'scatter') {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.x !== null) {
                  label += `(${context.parsed.x.toLocaleString()}, ${context.parsed.y.toLocaleString()})`;
                }
                return label;
              }

              // 处理饼图和环形图
              if (chartType === 'pie' || chartType === 'doughnut') {
                const value = context.parsed;
                const total = context.dataset.data.reduce((sum: number, val: number) => sum + val, 0);
                const percentage = total > 0 ? (value * 100 / total).toFixed(1) : '0.0';
                return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
              }

              // 处理直方图
              if (chartType === 'histogram') {
                return `${context.label}: ${context.parsed.y.toLocaleString()}`;
              }

              // 处理其他图表类型
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed && context.parsed.y !== null && context.parsed.y !== undefined) {
                label += context.parsed.y.toLocaleString();
              } else if (typeof context.parsed === 'number') {
                label += context.parsed.toLocaleString();
              }
              return label;
            }
          }
        },
        datalabels: {
          display: showDataLabels,
          anchor: (context: any) => {
            if (chartType === 'pie' || chartType === 'doughnut') {
              return 'center';
            }
            return 'end';
          },
          align: (context: any) => {
            if (chartType === 'pie' || chartType === 'doughnut') {
              return 'center';
            }
            if (chartType === 'bar' || chartType === 'histogram') {
              return context.dataset.data[context.dataIndex] >= 0 ? 'end' : 'start';
            }
            return 'center';
          },
          formatter: (value: any, context: any) => {
            if (value === null || value === undefined) return '';

            if (chartType === 'scatter') {
              if (typeof value === 'object' && value !== null && value.x !== undefined && value.y !== undefined) {
                return `(${value.x.toLocaleString(undefined, {maximumFractionDigits: 1})}, ${value.y.toLocaleString(undefined, {maximumFractionDigits: 1})})`;
              }
              return '';
            }

            if (chartType === 'pie' || chartType === 'doughnut') {
              try {
                // 计算总和，使用更安全的方式
                const total = context.chart.getDatasetMeta(0).total ||
                  context.dataset.data.reduce((sum: number, val: number) => {
                    const numVal = typeof val === 'number' ? val : parseFloat(String(val));
                    return isNaN(numVal) ? sum : sum + numVal;
                  }, 0);

                if (total === 0) return '0%';

                // 确保value是数字
                const numValue = typeof value === 'number' ? value : parseFloat(String(value));
                if (isNaN(numValue)) return '';

                const percentage = (numValue / total * 100);

                // 如果百分比太小且有很多数据点，不显示标签以避免拥挤
                if (percentage < 3 && context.dataset.data.length > 5) return null;

                // 格式化百分比
                return (percentage < 1 && percentage > 0)
                  ? percentage.toFixed(2) + '%'
                  : percentage.toFixed(1) + '%';
              } catch (error) {
                console.error('饼图标签格式化错误:', error);
                return '';
              }
            }

            if (chartType === 'histogram' || chartType === 'bar') {
              if (typeof value === 'number') {
                return value.toLocaleString(undefined, { maximumFractionDigits: 0 });
              }
            }

            if (typeof value === 'number') {
              return value.toLocaleString(undefined, { maximumFractionDigits: 2 });
            }
            return String(value);
          },
          color: '#000000',
          font: {
            weight: 'bold',
            size: 10
          },
          offset: (context: any) => {
            if (chartType === 'pie' || chartType === 'doughnut') {
              return 0;
            }
            if (chartType === 'bar' || chartType === 'histogram') {
              return context.dataset.data[context.dataIndex] >= 0 ? 4 : -10;
            }
            if (chartType === 'scatter') {
              return 8;
            }
            return 4;
          },
          padding: 0,
          borderRadius: 4,
          backgroundColor: null
        }
      },
    };

    // 为非饼图类型添加坐标轴配置
    if (chartType === 'scatter') {
      (options as any).scales = {
        x: {
          type: 'linear',
          position: 'bottom',
          title: {
            display: true,
            text: chartData.xAxisTitle || 'X轴'
          }
        },
        y: {
          type: 'linear',
          position: 'left',
          beginAtZero: true, // 确保Y轴从0开始，除非数据包含负值
          max: chartData.suggestedYMax, // 使用建议的Y轴最大值
          title: {
            display: true,
            text: chartData.yAxisTitle || 'Y轴'
          }
        }
      };
    } else if (chartType !== 'pie' && chartType !== 'doughnut') {
      (options as any).scales = {
        x: {
          title: {
            display: !!chartData.xAxisTitle,
            text: chartData.xAxisTitle
          },
          ticks: {
            maxRotation: 45,
            minRotation: 0,
            autoSkip: false, // 禁用自动跳过，让所有标签都显示
            maxTicksLimit: undefined // 移除刻度限制，让所有数据点都显示
          }
        },
        y: {
          beginAtZero: true, // 确保Y轴从0开始，除非数据包含负值
          max: chartData.suggestedYMax, // 使用建议的Y轴最大值
          title: {
            display: !!chartData.yAxisTitle,
            text: chartData.yAxisTitle
          },
          ticks: {
             callback: function(value: any) {
                if (Number.isInteger(value) || chartType === 'histogram') {
                    return value.toLocaleString();
                }
                // 对于非整数，保留两位小数
                return parseFloat(value).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 2 });
            }
          }
        }
      };
    }

    // 创建图表实例
    const chartJsType = (chartType === 'histogram') ? 'bar' : chartType;
    chartInstanceRef.current = new Chart(ctx, {
      type: chartJsType as ChartJsType,
      data: chartData,
      options: options as any
    });

    // 当组件卸载时销毁图表
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
    };
  }, [chartData, chartType, title, showDataLabels]);

  // 当全屏状态改变时，调整图表大小
  useEffect(() => {
    if (chartInstanceRef.current) {
      setTimeout(() => {
        try {
          chartInstanceRef.current?.resize();
        } catch (error) {
          console.error('调整图表大小失败:', error);
        }
      }, 300); // 等待过渡动画完成
    }
  }, [isFullscreen]);

  if (!chartData) return null;

  return (
    <div
      ref={containerRef}
      className={`${styles.chartContainer} ${isFullscreen ? styles.fullscreen : ''}`}
      style={{
        width: '100%',
        maxWidth: isFullscreen ? '100vw' : '100%',
        overflow: isFullscreen ? 'auto' : 'hidden',
        height: isFullscreen ? '100vh' : 'auto',
        position: isFullscreen ? 'fixed' : 'relative',
        zIndex: isFullscreen ? 100 : 'auto'
      }}
    >
      <div className={isFullscreen ? styles.fullscreenControls : styles.chartControls}>
        {onToggleTableView && (
          <Button
            variant="outline"
            size="icon"
            onClick={onToggleTableView}
            title="切换到表格视图"
            className={isFullscreen ? "h-8 w-8" : "h-7 w-7"}
          >
            <Table2 className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          </Button>
        )}
        <Button
          variant="outline"
          size="icon"
          onClick={downloadChart}
          title="下载图表"
          className={isFullscreen ? "h-8 w-8" : "h-7 w-7"}
        >
          <Download className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={toggleFullscreen}
          title={isFullscreen ? "退出全屏" : "全屏显示"}
          className={isFullscreen ? "h-8 w-8" : "h-7 w-7"}
        >
          {isFullscreen ? (
            <Minimize className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          ) : (
            <Maximize className={isFullscreen ? "h-4 w-4" : "h-3.5 w-3.5"} />
          )}
        </Button>
      </div>
      <div style={{
        width: '100%',
        height: isFullscreen ? 'calc(100vh - 80px)' : '600px',
        overflow: isFullscreen ? 'auto' : 'hidden',
        display: 'flex',
        flexDirection: 'column',
        margin: isFullscreen ? '20px 0' : '0',
      }}>
        {/* 横向滚动容器 */}
        <div style={{
          width: '100%',
          height: '100%',
          overflowX: isFullscreen ? 'visible' : 'auto',
          overflowY: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: isFullscreen ? 'center' : 'flex-start',
        }}>
          <canvas ref={chartRef} style={{
            maxWidth: isFullscreen ? '95vw' : 'none',
            maxHeight: isFullscreen ? '90vh' : '100%',
            width: isFullscreen ? 'auto' : (() => {
              // 根据数据点数量动态计算图表宽度
              const dataPointCount = chartData?.labels?.length || 0;
              const minWidth = 800; // 最小宽度
              const widthPerPoint = 60; // 每个数据点的宽度
              const calculatedWidth = Math.max(minWidth, dataPointCount * widthPerPoint);
              return `${calculatedWidth}px`;
            })(),
            height: isFullscreen ? 'auto' : '100%',
            marginTop: '20px',
            padding: '0 10px',
            minWidth: isFullscreen ? 'auto' : '800px' // 确保图表有最小宽度
          }} />
        </div>
        {/* 滚动提示 */}
        {!isFullscreen && (
          <div className="text-xs text-center text-muted-foreground opacity-60 py-1">
            ← 图表可左右滚动查看完整内容 →
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartDisplay;
