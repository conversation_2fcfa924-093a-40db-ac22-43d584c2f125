.chartContainer {
  width: 100%;
  background-color: hsl(var(--card));
  border-radius: var(--radius);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid hsl(var(--border) / 0.3);
  min-height: 600px;
  height: 100%;
}

.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background-color: hsl(var(--background));
  padding: 20px;
  box-sizing: border-box;
  border-radius: 0;
  overflow: auto;
  display: flex;
  flex-direction: column;
  max-width: 100vw !important;
  max-height: 100vh !important;
}

.chartControls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10000;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  background-color: hsl(var(--card) / 0.8);
  padding: 5px;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 全屏模式下的控制按钮 - 位于右下角 */
.fullscreenControls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  background-color: hsl(var(--card) / 0.9);
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid hsl(var(--border) / 0.5);
}

.controlRow {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 14px;
}

.controlGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  flex-basis: calc(50% - 6px);
  flex-grow: 1;
  min-width: 150px;
}

.filterInfo {
  font-size: 0.85rem;
  color: hsl(var(--muted-foreground));
  margin-top: 5px;
  padding: 8px;
  background-color: hsl(var(--secondary));
  border-radius: var(--radius);
}

.actionButtonsContainer {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

@media (max-width: 900px) {
  .actionButtonsContainer {
    flex-direction: column;
  }

  .actionButtonsContainer button {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .controlGroup {
    flex-basis: 100%;
    margin-bottom: 10px;
  }
}

/* 表格样式增强 */
.tableContainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.tableScrollArea {
  overflow: auto;
  flex: 1;
  border-radius: 0 0 var(--radius) var(--radius);
  position: relative;
  max-width: 100%;
  width: 100%;
}

.tableScrollArea::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.tableScrollArea::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.5);
  border-radius: 5px;
}

.tableScrollArea::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 5px;
}

.tableScrollArea::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}
