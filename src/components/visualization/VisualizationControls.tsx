"use client"

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { AggregationType, ChartType, DataFilter, ProportionFilter, SortFilterType } from './VisualizationUtils';
import styles from './visualization.module.css';

interface VisualizationControlsProps {
  headers: string[];
  onGenerateChart: (
    xAxis: string,
    yAxis: string,
    chartType: ChartType,
    aggregation: AggregationType,
    sortFilter: SortFilterType,
    customTopN: number,
    binCount: number,
    showDataLabels: boolean
  ) => void;
  onGetLLMSuggestion: (query: string) => void;
  onClearConditions: () => void;
  dataFilters: DataFilter[];
  proportionFilter: ProportionFilter | null;
}

const VisualizationControls: React.FC<VisualizationControlsProps> = ({
  headers,
  onGenerateChart,
  onGetLLMSuggestion,
  onClearConditions,
  dataFilters,
  proportionFilter
}) => {
  const [xAxis, setXAxis] = useState<string>('');
  const [yAxis, setYAxis] = useState<string>('');
  const [chartType, setChartType] = useState<ChartType>('bar');
  const [aggregation, setAggregation] = useState<AggregationType>('none');
  const [sortFilter, setSortFilter] = useState<SortFilterType>('none');
  const [customTopN, setCustomTopN] = useState<number>(5);
  const [binCount, setBinCount] = useState<number>(10);
  const [showDataLabels, setShowDataLabels] = useState<boolean>(true);
  const [userQuery, setUserQuery] = useState<string>('');
  
  // 当headers变化时更新X轴和Y轴选择
  useEffect(() => {
    if (headers.length > 0) {
      setXAxis(headers[0]);
      if (headers.length > 1) {
        setYAxis(headers[1]);
      } else {
        setYAxis(headers[0]);
      }
    }
  }, [headers]);

  // 处理图表类型变化
  const handleChartTypeChange = (value: string) => {
    const newChartType = value as ChartType;
    setChartType(newChartType);
    
    const isHistogram = newChartType === 'histogram';
    if (isHistogram) {
      setAggregation('count');
      setSortFilter('none');
    }
  };

  // 处理生成图表按钮点击
  const handleGenerateChart = () => {
    onGenerateChart(
      xAxis,
      yAxis,
      chartType,
      aggregation,
      sortFilter,
      customTopN,
      binCount,
      showDataLabels
    );
  };

  // 处理获取LLM建议按钮点击
  const handleGetLLMSuggestion = () => {
    onGetLLMSuggestion(userQuery);
  };

  // 渲染筛选信息
  const renderFilterInfo = () => {
    const filterTexts: string[] = [];
    
    if (dataFilters.length > 0) {
      filterTexts.push(dataFilters.map(f => `"${f.column}" ${f.operator} "${f.value}"`).join(' AND '));
    }
    
    if (proportionFilter) {
      filterTexts.push(`比例筛选: "${proportionFilter.filterColumn}" ${proportionFilter.operator} "${proportionFilter.value}"`);
    }
    
    if (filterTexts.length > 0) {
      return (
        <div className={styles.filterInfo}>
          当前筛选: {filterTexts.join('; ')}
        </div>
      );
    }
    
    return null;
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <div className={styles.controlRow}>
          <div className={styles.controlGroup}>
            <Label htmlFor="xAxis">X 轴 (类别/标签):</Label>
            <Select value={xAxis} onValueChange={setXAxis}>
              <SelectTrigger id="xAxis">
                <SelectValue placeholder="选择X轴" />
              </SelectTrigger>
              <SelectContent>
                {headers.map(header => (
                  <SelectItem key={header} value={header}>
                    {header}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className={styles.controlGroup}>
            <Label htmlFor="yAxis">Y 轴 (数值):</Label>
            <Select 
              value={yAxis} 
              onValueChange={setYAxis}
              disabled={chartType === 'histogram'}
            >
              <SelectTrigger id="yAxis">
                <SelectValue placeholder="选择Y轴" />
              </SelectTrigger>
              <SelectContent>
                {headers.map(header => (
                  <SelectItem key={header} value={header}>
                    {header}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className={styles.controlRow}>
          <div className={styles.controlGroup}>
            <Label htmlFor="aggregation">聚合方式:</Label>
            <Select 
              value={aggregation} 
              onValueChange={(value) => setAggregation(value as AggregationType)}
              disabled={chartType === 'histogram'}
            >
              <SelectTrigger id="aggregation">
                <SelectValue placeholder="选择聚合方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无</SelectItem>
                <SelectItem value="sum">求和 (Sum)</SelectItem>
                <SelectItem value="average">平均值 (Average)</SelectItem>
                <SelectItem value="count">计数 (Count)</SelectItem>
                <SelectItem value="min">最小值 (Min)</SelectItem>
                <SelectItem value="max">最大值 (Max)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className={styles.controlGroup}>
            <Label htmlFor="sortFilter">排序/筛选:</Label>
            <Select 
              value={sortFilter} 
              onValueChange={(value) => setSortFilter(value as SortFilterType)}
              disabled={chartType === 'histogram'}
            >
              <SelectTrigger id="sortFilter">
                <SelectValue placeholder="选择排序/筛选方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无</SelectItem>
                <SelectItem value="asc">升序 (Y轴)</SelectItem>
                <SelectItem value="desc">降序 (Y轴)</SelectItem>
                <SelectItem value="top1">Top 1 (Y轴)</SelectItem>
                <SelectItem value="top3">Top 3 (Y轴)</SelectItem>
                <SelectItem value="top5">Top 5 (Y轴)</SelectItem>
                <SelectItem value="top10">Top 10 (Y轴)</SelectItem>
                <SelectItem value="topN_custom">Top N (自定义)</SelectItem>
              </SelectContent>
            </Select>
            {sortFilter === 'topN_custom' && (
              <Input
                type="number"
                value={customTopN}
                onChange={(e) => setCustomTopN(parseInt(e.target.value) || 5)}
                min={1}
                className="w-20"
              />
            )}
          </div>
        </div>

        {renderFilterInfo()}

        <div className={styles.controlRow}>
          <div className={styles.controlGroup}>
            <Label htmlFor="chartType">图表类型:</Label>
            <Select value={chartType} onValueChange={handleChartTypeChange}>
              <SelectTrigger id="chartType">
                <SelectValue placeholder="选择图表类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">柱状图</SelectItem>
                <SelectItem value="line">折线图</SelectItem>
                <SelectItem value="pie">饼图</SelectItem>
                <SelectItem value="doughnut">甜甜圈图</SelectItem>
                <SelectItem value="scatter">散点图</SelectItem>
                <SelectItem value="histogram">直方图</SelectItem>
              </SelectContent>
            </Select>
            {chartType === 'histogram' && (
              <>
                <Label htmlFor="binCount">分箱数:</Label>
                <Input
                  id="binCount"
                  type="number"
                  value={binCount}
                  onChange={(e) => setBinCount(parseInt(e.target.value) || 10)}
                  min={2}
                  max={50}
                  className="w-20"
                />
              </>
            )}
          </div>
          <div className={styles.controlGroup}>
            <div className="flex items-center space-x-2">
              {/* <Checkbox 
                id="showDataLabels" 
                checked={showDataLabels} 
                onCheckedChange={(checked) => setShowDataLabels(checked === true)}
              /> */}
              {/* <Label htmlFor="showDataLabels">在图表上显示数值</Label> */}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <Label htmlFor="userQuery">您的分析需求 (可选, 例如: "工资最高的3名员工"):</Label>
          <Textarea
            id="userQuery"
            placeholder="输入您想分析的内容"
            value={userQuery}
            onChange={(e) => setUserQuery(e.target.value)}
            rows={2}
            className="mt-1"
          />
        </div>

        <div className={styles.actionButtonsContainer}>
          <Button onClick={handleGetLLMSuggestion} className="flex-1">
            获取 LLM 建议
          </Button>
          <Button onClick={handleGenerateChart} className="flex-1">
            手动生成图表
          </Button>
        </div>

        <Button 
          variant="outline" 
          onClick={onClearConditions} 
          className="w-full mt-3"
        >
          清除生成条件
        </Button>
      </CardContent>
    </Card>
  );
};

export default VisualizationControls;
