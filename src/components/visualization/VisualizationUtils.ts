import { toast } from "sonner";

// 图表类型定义
export type ChartType = 'bar' | 'line' | 'pie' | 'doughnut' | 'scatter' | 'histogram';

// 聚合方式定义
export type AggregationType = 'none' | 'sum' | 'average' | 'count' | 'min' | 'max';

// 排序/筛选方式定义
export type SortFilterType = 'none' | 'asc' | 'desc' | 'top1' | 'top3' | 'top5' | 'top10' | 'topN_custom';

// 数据筛选器定义
export interface DataFilter {
  column: string;
  operator: 'equals' | 'contains';
  value: string | number;
}

// 比例筛选器定义
export interface ProportionFilter {
  filterColumn: string;
  operator: 'greaterThan' | 'lessThan' | 'equals' | 'greaterThanOrEquals' | 'lessThanOrEquals' | 'contains';
  value: string | number;
  labelForMatch: string;
  labelForNonMatch: string;
}

// 图表数据配置
export interface ChartDataConfig {
  labels?: string[];
  datasets: {
    label?: string;
    data: number[] | { x: number; y: number }[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
    tension?: number;
    hoverOffset?: number;
  }[];
  xAxisTitle?: string; // 添加x轴标题
  yAxisTitle?: string; // 添加y轴标题
  suggestedYMax?: number; // 建议的Y轴最大值
}

// 列类型推断
export function inferColumnTypes(colHeaders: string[], dataSample: Record<string, any>[]) {
  const types: Record<string, string> = {};
  if (!dataSample || dataSample.length === 0) return types;

  colHeaders.forEach(header => {
    let numericCount = 0;
    let dateCount = 0;
    const values = dataSample
      .map(row => row[header])
      .filter(val => val !== null && val !== undefined && String(val).trim() !== "");

    if (values.length === 0) {
      types[header] = "Empty";
      return;
    }

    values.forEach(val => {
      if (val instanceof Date && !isNaN(val.getTime())) {
        dateCount++;
      } else if (!isNaN(parseFloat(String(val).replace(/[, $£€]/g, ''))) && isFinite(Number(String(val).replace(/[, $£€]/g, '')))) {
        numericCount++;
      }
    });

    if (dateCount / values.length > 0.7) {
      types[header] = "Date";
    } else if (numericCount / values.length > 0.7) {
      types[header] = "Numeric";
    } else {
      const uniqueValues = new Set(values.map(v => String(v).toLowerCase()));
      if (uniqueValues.size / values.length < 0.5 && uniqueValues.size <= 20) {
        types[header] = "Categorical";
      } else {
        types[header] = "Text";
      }
    }
  });
  return types;
}

// 应用数据筛选器
export function applyDataFilters(data: Record<string, any>[], filters: DataFilter[]) {
  if (!filters || filters.length === 0) {
    return data;
  }
  return data.filter(row => {
    return filters.every(filter => {
      const rowValue = row[filter.column];
      const filterValue = filter.value;
      if (rowValue === undefined || rowValue === null) return false;

      const rowValueStr = String(rowValue).toLowerCase();
      const filterValueStr = String(filterValue).toLowerCase();

      switch (filter.operator) {
        case 'equals':
          return rowValueStr === filterValueStr;
        case 'contains':
          return rowValueStr.includes(filterValueStr);
        default:
          return true;
      }
    });
  });
}

// 生成颜色
export function generateColors(count: number, alpha = 0.7): string[] {
  const colors: string[] = [];
  const baseColors = [
    [120, 94, 240], // 主题紫色
    [255, 99, 132],
    [54, 162, 235],
    [255, 206, 86],
    [153, 102, 255],
    [255, 159, 64],
    [75, 192, 192],
    [199, 199, 199],
    [83, 102, 255]
  ];

  // 如果count超过了baseColors长度，则通过调整亮度生成更多颜色
  if (count > baseColors.length) {
    const originalColors = [...baseColors];
    for (let i = 0; i < Math.ceil(count / originalColors.length) - 1; i++) {
      originalColors.forEach(color => {
        // 通过调整每个颜色通道来创建略微不同的颜色
        baseColors.push([
          Math.min(255, Math.max(0, color[0] + 30 * (i + 1) * (Math.random() > 0.5 ? 1 : -1))),
          Math.min(255, Math.max(0, color[1] + 30 * (i + 1) * (Math.random() > 0.5 ? 1 : -1))),
          Math.min(255, Math.max(0, color[2] + 30 * (i + 1) * (Math.random() > 0.5 ? 1 : -1)))
        ]);
      });
    }
  }

  for (let i = 0; i < count; i++) {
    const color = baseColors[i % baseColors.length];
    colors.push(`rgba(${color[0]}, ${color[1]}, ${color[2]}, ${alpha})`);
  }
  return colors;
}

// 显示消息提示
export function showMessage(msg: string, type: 'info' | 'error' | 'loading' = 'info', id?: string) {
  // 先关闭所有现有的loading消息
  if (type === 'loading') {
    // 生成唯一ID以便后续可以引用这个toast
    const toastId = id || `loading-${Date.now()}`;
    return toast.loading(msg, { id: toastId });
  } else {
    // 对于非loading消息，先关闭所有toast
    toast.dismiss();

    switch (type) {
      case 'error':
        return toast.error(msg);
      case 'info':
      default:
        return toast.info(msg);
    }
  }
}
