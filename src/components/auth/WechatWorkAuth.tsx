"use client";

import { useEffect, useState, createContext, useContext, ReactNode } from "react";
import { initWechatWorkService, WechatWorkService } from "@/lib/wechatWorkService";

// 配置项接口
interface WechatWorkAuthConfig {
  corpId: string;
  agentId: string;
  appSecret?: string;
  redirectUri?: string;
}

// 用户信息接口
interface WechatWorkUserInfo {
  userId: string;
  name: string;
  avatar: string;
  department: number[];
  email?: string;
  mobile?: string;
  position?: string;
  gender?: string;
  [key: string]: any;
}

// 认证上下文接口
interface WechatWorkAuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  userInfo: WechatWorkUserInfo | null;
  error: Error | null;
  logout: () => void;
}

// 创建认证上下文
const WechatWorkAuthContext = createContext<WechatWorkAuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  userInfo: null,
  error: null,
  logout: () => {},
});

// 认证提供者组件属性
interface WechatWorkAuthProviderProps {
  children: ReactNode;
  config: WechatWorkAuthConfig;
  onAuthSuccess?: (userInfo: WechatWorkUserInfo) => void;
  onAuthFailure?: (error: Error) => void;
}

/**
 * 企业微信认证提供者组件
 */
export function WechatWorkAuthProvider({
  children,
  config,
  onAuthSuccess,
  onAuthFailure,
}: WechatWorkAuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userInfo, setUserInfo] = useState<WechatWorkUserInfo | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [service, setService] = useState<WechatWorkService | null>(null);

  useEffect(() => {
    // 先检查URL参数
    const searchParams = new URLSearchParams(window.location.search);
    const userInfoParam = searchParams.get('userInfo');
    const errorParam = searchParams.get('error');

    const handleAuth = async () => {
      try {
        if (errorParam) {
          throw new Error(decodeURIComponent(errorParam));
        }

        if (userInfoParam) {
          const decodedUserInfo = JSON.parse(decodeURIComponent(userInfoParam));
          setUserInfo(decodedUserInfo);
          setIsAuthenticated(true);
          onAuthSuccess?.(decodedUserInfo);
          // 存入缓存
          const wechatService = initWechatWorkService(config);
          wechatService.saveUserToCache(decodedUserInfo);
          // 清除URL参数
          window.history.replaceState({}, '', window.location.pathname);
          return;
        }

        // 如果没有URL参数，尝试从缓存或进行认证
        const wechatService = initWechatWorkService(config);
        setService(wechatService);
        const user = await wechatService.init();
        
        if (user) {
          setUserInfo(user);
          setIsAuthenticated(true);
          onAuthSuccess?.(user);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
        onAuthFailure?.(err instanceof Error ? err : new Error(String(err)));
        // 清除URL参数（如果有）
        window.history.replaceState({}, '', window.location.pathname);
      } finally {
        setIsLoading(false);
      }
    };

    handleAuth();
  }, []); // 只在组件挂载时执行一次

  // 登出方法
  const logout = () => {
    if (service) {
      service.clearCache();
      setIsAuthenticated(false);
      setUserInfo(null);
    }
  };

  // 提供认证上下文
  const contextValue: WechatWorkAuthContextType = {
    isAuthenticated,
    isLoading,
    userInfo,
    error,
    logout,
  };

  return (
    <WechatWorkAuthContext.Provider value={contextValue}>
      {children}
    </WechatWorkAuthContext.Provider>
  );
}

/**
 * 使用企业微信认证的自定义Hook
 */
export function useWechatWorkAuth() {
  const context = useContext(WechatWorkAuthContext);

  if (context === undefined) {
    throw new Error("useWechatWorkAuth must be used within a WechatWorkAuthProvider");
  }

  return context;
}

/**
 * 企业微信认证组件 - 显示登录状态或子组件
 */
interface WechatWorkAuthProps {
  children: ReactNode;
  config: WechatWorkAuthConfig;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
}

export default function WechatWorkAuth({
  children,
  config,
  loadingComponent,
  errorComponent,
}: WechatWorkAuthProps) {
  return (
    <WechatWorkAuthProvider config={config}>
      <WechatWorkAuthContent
        loadingComponent={loadingComponent}
        errorComponent={errorComponent}
      >
        {children}
      </WechatWorkAuthContent>
    </WechatWorkAuthProvider>
  );
}

/**
 * 企业微信认证内容组件 - 根据认证状态显示不同内容
 */
interface WechatWorkAuthContentProps {
  children: ReactNode;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
}

function WechatWorkAuthContent({
  children,
  loadingComponent,
  errorComponent,
}: WechatWorkAuthContentProps) {
  const { isLoading, error } = useWechatWorkAuth();

  if (isLoading) {
    return loadingComponent || <div>加载中...</div>;
  }

  if (error) {
    return errorComponent || (
      <div className="p-4 text-red-500">
        认证失败: {error.message}
      </div>
    );
  }

  return <>{children}</>;
} 