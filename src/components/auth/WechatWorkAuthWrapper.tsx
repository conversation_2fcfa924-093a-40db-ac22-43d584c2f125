"use client";

import dynamic from "next/dynamic";

// 动态导入企业微信认证组件
const WechatWorkAuth = dynamic(() => import("./WechatWorkAuth"), {
  ssr: false,
});

interface WechatWorkAuthWrapperProps {
  children: React.ReactNode;
  config: {
    corpId: string;
    agentId: string;
    redirectUri?: string;
  };
  loadingComponent?: React.ReactNode;
}

export default function WechatWorkAuthWrapper({
  children,
  config,
  loadingComponent,
}: WechatWorkAuthWrapperProps) {
  return (
    <WechatWorkAuth config={config} loadingComponent={loadingComponent}>
      {children}
    </WechatWorkAuth>
  );
} 