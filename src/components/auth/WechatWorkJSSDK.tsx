"use client";

import { useState, useEffect, createContext, useContext, ReactNode } from "react";

// 全局状态，用于跟踪配置获取尝试状态
// 如果获取失败过一次，就不再尝试获取
let globalConfigFailure = false;

// 定义JS-SDK上下文类型
interface WechatWorkJSSDKContextType {
  isReady: boolean;
  isLoading: boolean;
  error: Error | null;
  jsApiList: string[];
  configWx: (apiList?: string[]) => Promise<boolean>;
  resetConfigFailure: () => void; // 添加重置失败状态的方法
}

// 创建上下文
const WechatWorkJSSDKContext = createContext<WechatWorkJSSDKContextType>({
  isReady: false,
  isLoading: false,
  error: null,
  jsApiList: [],
  configWx: async () => false,
  resetConfigFailure: () => {},
});

// 上下文提供者组件属性
interface WechatWorkJSSDKProviderProps {
  children: ReactNode;
  autoInit?: boolean;
  defaultApiList?: string[];
  backendUrl?: string;
  skipConfigOnFailure?: boolean; // 添加是否在失败后跳过配置的选项
}

/**
 * 企业微信JS-SDK提供者组件
 */
export function WechatWorkJSSDKProvider({
  children,
  autoInit = true,
  defaultApiList = ['checkJsApi', 'showToast', 'showModal'],
  backendUrl = 'https://chat-ai.groupama-sdig.com:8080/wx_config',
  // backendUrl = 'http://localhost:8080/wx_config',
  skipConfigOnFailure = true // 默认开启全局失败跳过
}: WechatWorkJSSDKProviderProps) {
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [jsApiList, setJsApiList] = useState<string[]>(defaultApiList);

  // 加载企业微信JS-SDK脚本
  const loadWechatScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查是否已加载
      if (window.wx) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.2.6.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load WeChat Work SDK'));
      document.head.appendChild(script);
    });
  };

  // 重置全局失败状态
  const resetConfigFailure = () => {
    globalConfigFailure = false;
  };

  // 配置企业微信JS-SDK
  const configWx = async (apiList?: string[]): Promise<boolean> => {
    try {
      // 如果之前获取配置失败过且启用了失败跳过，则不再尝试获取
      if (skipConfigOnFailure && globalConfigFailure) {
        console.warn('JS-SDK配置已曾经失败，跳过配置');
        return false;
      }

      setIsLoading(true);
      setError(null);

      // 确保SDK已加载
      await loadWechatScript();

      if (!window.wx) {
        throw new Error('企业微信JS-SDK未加载');
      }

      // 获取当前URL（去除hash部分）
      const url = window.location.href.split('#')[0];

      try {
        // 从后端获取配置
        const response = await fetch(`${backendUrl}?url=${encodeURIComponent(url)}`);
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`获取JS-SDK配置失败: ${errorText}`);
        }

        // 解析配置信息
        const config = await response.json();

        // 使用传入的apiList或默认值
        const finalApiList = apiList || jsApiList;
        setJsApiList(finalApiList);

        // 配置JS-SDK
        window.wx.config({
          beta: true,
          debug: config.debug || false,
          appId: config.appId,
          timestamp: config.timestamp,
          nonceStr: config.nonceStr,
          signature: config.signature,
          jsApiList: finalApiList
        });

        // 监听ready事件
        return new Promise((resolve) => {
          window.wx.ready(() => {
            setIsReady(true);
            setIsLoading(false);
            resolve(true);
          });

          window.wx.error((res: any) => {
            const errorMsg = `JS-SDK配置失败: ${res.errMsg || JSON.stringify(res)}`;
            setError(new Error(errorMsg));
            setIsLoading(false);
            console.error(errorMsg);
            
            // 标记全局失败状态
            if (skipConfigOnFailure) {
              globalConfigFailure = true;
            }
            
            resolve(false);
          });
        });
      } catch (err) {
        // 后端请求失败，标记全局失败状态
        if (skipConfigOnFailure) {
          globalConfigFailure = true;
        }
        throw err;
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      setError(new Error(errorMsg));
      setIsLoading(false);
      console.error('配置JS-SDK失败:', errorMsg);
      return false;
    }
  };

  // 自动初始化
  useEffect(() => {
    if (autoInit && typeof window !== 'undefined' && !globalConfigFailure) {
      configWx();
    }
  }, [autoInit]);

  // 暴露接口
  const contextValue: WechatWorkJSSDKContextType = {
    isReady,
    isLoading,
    error,
    jsApiList,
    configWx,
    resetConfigFailure
  };

  return (
    <WechatWorkJSSDKContext.Provider value={contextValue}>
      {children}
    </WechatWorkJSSDKContext.Provider>
  );
}

/**
 * 使用企业微信JS-SDK钩子
 */
export function useWechatWorkJSSDK() {
  return useContext(WechatWorkJSSDKContext);
}

// 添加全局类型声明
declare global {
  interface Window {
    wx: any;
  }
}

export default function WechatWorkJSSDK({
  children,
  autoInit = true,
  defaultApiList,
  backendUrl,
  skipConfigOnFailure = true // 默认开启全局失败跳过
}: WechatWorkJSSDKProviderProps) {
  return (
    <WechatWorkJSSDKProvider 
      autoInit={autoInit} 
      defaultApiList={defaultApiList}
      backendUrl={backendUrl}
      skipConfigOnFailure={skipConfigOnFailure}
    >
      {children}
    </WechatWorkJSSDKProvider>
  );
} 