"use client";

import { useWechatWorkAuth } from "./WechatWorkAuth";

export default function WechatWorkUserInfo() {
  const { userInfo, isAuthenticated, logout } = useWechatWorkAuth();

  if (!isAuthenticated || !userInfo) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 p-2 rounded-md border border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 shadow-sm">
      <div className="flex-shrink-0">
        {userInfo.avatar ? (
          <div className="relative">
            <img
              src={userInfo.avatar}
              alt={userInfo.name}
              className="h-8 w-8 md:h-10 md:w-10 rounded-full border-2 border-blue-200 dark:border-blue-700"
            />
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-8 w-8 md:h-10 md:w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm font-medium shadow-sm">
            {userInfo.name?.charAt(0) || "U"}
          </div>
        )}
      </div>
      <div className="flex-1 min-w-0 max-w-[120px] md:max-w-none">
        <p className="font-medium text-sm md:text-base truncate">
          <span className="gradient-text-safe gradient-text-fallback">
            {userInfo.name}
          </span>
        </p>
        <p className="text-xs text-muted-foreground truncate">
          {userInfo.position || userInfo.userId}
        </p>
      </div>
      {/* <button
        onClick={logout}
        className="text-xs px-2 py-1 border rounded hover:bg-secondary/50"
      >
        登出
      </button> */}
    </div>
  );
} 