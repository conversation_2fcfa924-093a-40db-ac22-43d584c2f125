"use client"

import React from 'react';
import { DocFeatureCard } from './DocFeatureCard';
import docTools, { DocTool } from '@/config/docTools';

export interface DocFeatureCardsProps {
  category: string;
  onSelectFeature: (id: string) => void;
  isMobile?: boolean;
}

/**
 * A component that renders document processing feature cards for a specific category
 * Responsive design for both mobile and desktop views
 */
export function DocFeatureCards({ category, onSelectFeature, isMobile = false }: DocFeatureCardsProps) {
  // Filter tools by category
  const filteredTools = docTools.filter(tool => tool.category === category);

  return (
    <div className="w-full">
      {/* Mobile view with grid layout */}
      <div className={`${!isMobile ? 'md:hidden' : ''} w-full pb-4`}>
        <div className="grid grid-cols-1 gap-4">
          {filteredTools.map((tool) => (
            <DocFeatureCard
              key={tool.id}
              id={tool.id}
              title={tool.title}
              description={tool.description}
              icon={tool.icon}
              colorClass={tool.colorClass}
              iconColorClass={tool.iconColorClass}
              onSelect={onSelectFeature}
              category={category}
              compact={isMobile}
            />
          ))}
        </div>
      </div>

      {/* Desktop view with grid layout */}
      <div className={`${isMobile ? 'hidden' : 'hidden md:grid'} grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 w-full`}>
        {filteredTools.map((tool) => (
          <DocFeatureCard
            key={tool.id}
            id={tool.id}
            title={tool.title}
            description={tool.description}
            icon={tool.icon}
            colorClass={tool.colorClass}
            iconColorClass={tool.iconColorClass}
            onSelect={onSelectFeature}
            category={category}
          />
        ))}
      </div>
    </div>
  );
}
