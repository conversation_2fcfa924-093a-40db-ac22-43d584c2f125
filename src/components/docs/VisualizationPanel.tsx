"use client"

import React, { useEffect } from 'react';
import ExcelVisualization from '@/components/visualization/ExcelVisualization';
import { FeatureCards } from '@/components/chat/FeatureCards';

interface VisualizationPanelProps {
  onSendMessage?: (message: string) => void;
}

export default function VisualizationPanel({ onSendMessage }: VisualizationPanelProps) {
  // 创建一个全局函数，用于处理示例卡片的点击事件
  useEffect(() => {
    if (onSendMessage) {
      // 将 onSendMessage 函数挂载到 window 对象上，以便在全局范围内访问
      (window as any).__handleFeatureCardClick = (message: string) => {
        onSendMessage(message);
      };

      // 清理函数
      return () => {
        delete (window as any).__handleFeatureCardClick;
      };
    }
  }, [onSendMessage]);

  return (
    <div className="flex flex-col h-full bg-background" style={{ position: 'relative', zIndex: 10 }}>
      <div className="p-6 md:p-8 border-b bg-background/80 backdrop-blur shadow-sm">
        <h2 className="text-2xl md:text-3xl font-bold mb-2 text-center">Excel 数据可视化工具</h2>
        {/* <p className="text-center text-muted-foreground">
          上传 Excel 文件，分析数据并生成图表
        </p> */}
      </div>

      <div className="flex-1 overflow-auto" style={{ position: 'relative' }}>
        <ExcelVisualization />

        {/* 隐藏的消息处理器，用于支持示例卡片点击 */}
        {onSendMessage && (
          <div className="hidden">
            <FeatureCards onSendMessage={onSendMessage} />
          </div>
        )}
      </div>
    </div>
  );
}