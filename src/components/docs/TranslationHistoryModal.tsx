"use client"

import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";
import { Download, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface TranslationRecord {
  id: string;
  task_id: string;
  original_filename: string;
  translated_filename: string;
  translation_progress: number;
  translation_status: string;
  translated_file_ftp_url: string;
  start_time: string;
  summary_progress?: number;
  summary_status?: string;
  summary_content?: string;
}

interface PaginationInfo {
  total_count: number;
  total_pages: number;
  current_page: number;
  page_size: number;
}

interface TranslationHistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: string;
  mode?: 'translate' | 'summary' | 'convert';
}

// 获取API URL和API Key
const API_URL = process.env.NEXT_PUBLIC_API_URL || '';
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || '';

export default function TranslationHistoryModal({
  open,
  onOpenChange,
  userId,
  mode = 'translate'
}: TranslationHistoryModalProps) {
  const [records, setRecords] = useState<TranslationRecord[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total_count: 0,
    total_pages: 0,
    current_page: 1,
    page_size: 5
  });
  const [loading, setLoading] = useState(false);
  const [downloadingId, setDownloadingId] = useState<string | null>(null);

  // 获取进度值的辅助函数
  const getProgressValue = (record: TranslationRecord) => {
    return mode === 'summary' ? 
      (record.summary_progress !== undefined ? record.summary_progress : 0) : 
      (record.translation_progress !== undefined ? record.translation_progress : 0);
  };

  // 获取状态值的辅助函数
  const getStatusValue = (record: TranslationRecord) => {
    return mode === 'summary' ? 
      (record.summary_status || 'pending') : 
      (record.translation_status || 'pending');
  };

  // 获取翻译历史记录 - 使用 useCallback 包装以便在依赖项中使用
  const fetchTranslationHistory = useCallback(async (page: number = 1) => {
    console.log('fetchTranslationHistory 被调用，页码:', page, '用户ID:', userId, '模式:', mode);
    try {
      setLoading(true);
      
      // 确保 userId 有效
      if (!userId) {
        console.error('用户ID为空，无法获取历史记录');
        toast.error('用户ID为空，无法获取历史记录');
        setLoading(false);
        return;
      }
      
      // 根据mode选择不同的API端点
      let apiEndpoint = '/document/translate/history';
      if (mode === 'summary') {
        apiEndpoint = '/document/summary/history';
      }
      // convert模式使用与translate相同的接口
      
      // 构建完整的API URL
      const apiUrl = `${API_URL}${apiEndpoint}`;
      const queryParams = new URLSearchParams({
        user_id: userId,
        page: page.toString(),
        page_size: pagination.page_size.toString()
      });
      
      const fullUrl = `${apiUrl}?${queryParams.toString()}`;
      console.log('正在请求历史数据:', fullUrl);
      
      // 设置请求头
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      // 如果有API Key，添加到请求头
      if (API_KEY) {
        headers['Authorization'] = `Bearer ${API_KEY}`;
      }
      
      const response = await fetch(fullUrl, { headers });
      
      console.log('历史接口响应状态:', response.status);
      
      if (!response.ok) {
        throw new Error(`获取历史记录失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('历史接口响应数据:', data);
      
      if (data.code === 0 && data.message === "success") {
        setRecords(data.data.records);
        setPagination(data.data.pagination);
      } else {
        throw new Error(data.message || "获取历史记录失败");
      }
    } catch (error) {
      console.error('获取历史记录出错:', error);
      toast.error(`获取历史记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  }, [userId, pagination.page_size, mode]);

  // 当模态框打开时获取历史记录
  useEffect(() => {
    console.log('TranslationHistoryModal useEffect 触发，open:', open, 'userId:', userId, 'mode:', mode);
    if (open && userId) {
      console.log(`模态框打开，开始获取${mode === 'summary' ? '摘要' : mode === 'convert' ? '转换' : '翻译'}历史，用户ID:`, userId);
      fetchTranslationHistory(1); // 显式传入页码 1，确保每次打开模态框都从第一页开始
    }
  }, [open, userId, fetchTranslationHistory, mode]);

  // 处理页码变化
  const handlePageChange = useCallback((page: number) => {
    console.log('页码变更为:', page);
    fetchTranslationHistory(page);
  }, [fetchTranslationHistory]);

  // 下载翻译文件
  const handleDownload = useCallback(async (record: TranslationRecord) => {
    if (!record.task_id) {
      toast.error('任务ID不可用，无法下载文件');
      return;
    }

    try {
      setDownloadingId(record.id);
      
      // 根据mode选择不同的API端点
      let downloadEndpoint = '/document/translate/download/';
      if (mode === 'summary') {
        downloadEndpoint = '/document/summary/download/';
      }
      // convert模式使用与translate相同的接口
      
      // 构建下载API URL
      const downloadUrl = `${API_URL}${downloadEndpoint}${record.task_id}`;
      console.log(`正在下载${mode === 'summary' ? '摘要' : mode === 'convert' ? '转换' : '翻译'}文件，任务ID:`, record.task_id);
      console.log('下载URL:', downloadUrl);

      const headers: HeadersInit = {
        'Cache-Control': 'no-cache'
      };
      
      if (API_KEY) {
        headers['Authorization'] = `Bearer ${API_KEY}`;
      }

      const response = await fetch(downloadUrl, {
        headers,
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 使用translated_filename作为下载文件名，如果没有则使用原始文件名或任务ID
      const filename = record.translated_filename || record.original_filename || `${mode === 'summary' ? 'summary' : mode === 'convert' ? 'converted' : 'translated'}_document_${record.task_id}.docx`;
      link.download = filename;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('文档下载成功');
    } catch (error) {
      console.error('下载文件时出错:', error);
      toast.error(`下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setDownloadingId(null);
    }
  }, [API_URL, API_KEY, mode]);

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'processing':
        return '处理中';
      case 'translating':
        return mode === 'summary' ? '分析中' : mode === 'convert' ? '转换中' : '翻译中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'error':
        return '失败';
      default:
        return status || '-';
    }
  };

  // 格式化时间
  const formatDateTime = (dateTimeStr: string | null) => {
    if (!dateTimeStr) return '-';
    try {
      const date = new Date(dateTimeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return dateTimeStr;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex flex-col items-center">
          <DialogTitle className="text-2xl font-bold mb-4">
            {mode === 'summary' ? '摘要历史记录' : mode === 'convert' ? '转换历史记录' : '翻译历史记录'}
          </DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">加载中...</span>
          </div>
        ) : records.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            暂无历史记录
          </div>
        ) : (
          <>
            <div className="overflow-x-auto flex-1">
              <Table className="min-w-[600px]">
                <TableHeader>
                  <TableRow>
                    <TableHead className="hidden md:table-cell">任务ID</TableHead>
                    <TableHead>原文件名</TableHead>
                    {mode === 'summary' && <TableHead className="w-[200px]">摘要内容</TableHead>}
                    <TableHead className="w-[60px] text-center">进度</TableHead>
                    <TableHead className="w-[70px]">状态</TableHead>
                    <TableHead className="hidden sm:table-cell">开始时间</TableHead>
                    {mode !== 'summary' && <TableHead className="w-[80px] text-center">操作</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-mono hidden md:table-cell">
                        <div 
                          className="cursor-help truncate max-w-[120px]" 
                          title={record.task_id}
                        >
                          {record.task_id.length > 8 
                            ? `${record.task_id.substring(0, 8)}...` 
                            : record.task_id}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div 
                          className="cursor-help truncate max-w-[140px] sm:max-w-[200px]" 
                          title={`${record.original_filename}\n开始时间: ${formatDateTime(record.start_time)}`}
                        >
                          {record.original_filename}
                        </div>
                        <div className="text-xs text-muted-foreground sm:hidden mt-1">
                          {formatDateTime(record.start_time)}
                        </div>
                      </TableCell>
                      {mode === 'summary' && (
                        <TableCell>
                          <div 
                            className="cursor-help truncate max-w-[180px]" 
                            title={record.summary_content || '无摘要内容'}
                          >
                            {record.summary_content || '无摘要内容'}
                          </div>
                        </TableCell>
                      )}
                      <TableCell className="text-center">{getProgressValue(record)}%</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs whitespace-nowrap
                          ${getStatusValue(record) === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 
                           getStatusValue(record) === 'error' || getStatusValue(record) === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : 
                           'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'}
                        `}>
                          {getStatusText(getStatusValue(record))}
                        </span>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">{formatDateTime(record.start_time)}</TableCell>
                      {mode !== 'summary' && (
                        <TableCell className="text-center">
                          {getStatusValue(record) === 'completed' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDownload(record)}
                              disabled={downloadingId === record.id}
                              className="h-8 px-2 sm:px-3"
                            >
                              {downloadingId === record.id ? (
                                <>
                                  <Loader2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 sm:mr-1 animate-spin" />
                                  <span className="hidden sm:inline">下载中</span>
                                </>
                              ) : (
                                <>
                                  <Download className="h-3.5 w-3.5 sm:h-4 sm:w-4 sm:mr-1" />
                                  <span className="hidden sm:inline">下载</span>
                                </>
                              )}
                            </Button>
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {pagination.total_pages > 1 && (
              <Pagination className="mt-4 py-2">
                <PaginationContent className="flex flex-wrap justify-center gap-1">
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => pagination.current_page > 1 && handlePageChange(pagination.current_page - 1)}
                      aria-disabled={pagination.current_page <= 1}
                      className={`h-8 w-8 p-0 sm:h-9 sm:w-auto sm:px-2.5 ${pagination.current_page <= 1 ? 'pointer-events-none opacity-50' : ''}`}
                    >
                      <span className="sr-only sm:not-sr-only">上一页</span>
                    </PaginationPrevious>
                  </PaginationItem>
                  
                  {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                    // 显示当前页附近的页码
                    let pageToShow;
                    if (pagination.total_pages <= 5) {
                      pageToShow = i + 1;
                    } else {
                      const start = Math.max(1, pagination.current_page - 2);
                      const end = Math.min(pagination.total_pages, start + 4);
                      pageToShow = start + i;
                      if (pageToShow > end) return null;
                    }
                    
                    return (
                      <PaginationItem key={pageToShow}>
                        <PaginationLink
                          isActive={pageToShow === pagination.current_page}
                          onClick={() => handlePageChange(pageToShow)}
                          className="h-8 w-8 sm:h-9"
                        >
                          {pageToShow}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}
                  
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => pagination.current_page < pagination.total_pages && handlePageChange(pagination.current_page + 1)}
                      aria-disabled={pagination.current_page >= pagination.total_pages}
                      className={`h-8 w-8 p-0 sm:h-9 sm:w-auto sm:px-2.5 ${pagination.current_page >= pagination.total_pages ? 'pointer-events-none opacity-50' : ''}`}
                    >
                      <span className="sr-only sm:not-sr-only">下一页</span>
                    </PaginationNext>
                  </PaginationItem>
                  
                  <PaginationItem className="hidden sm:block">
                    <span className="text-xs text-muted-foreground ml-2 flex items-center">
                      （每页显示5条）
                    </span>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
