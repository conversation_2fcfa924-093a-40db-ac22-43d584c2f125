"use client"

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import docTools, { docCategories } from '@/config/docTools';
import { cn } from '@/lib/utils';
import { renderIcon } from '@/config/iconMap';
import { Button } from "@/components/ui/button";

import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { AlertCircle, AlertTriangle, ArrowLeft, ArrowRight, Check, ChevronDown, Download, FileText, Loader2, Upload, PanelLeft, Menu, FileSpreadsheet } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { DocFeatureCards } from '../DocFeatureCards';
import { useWechatWorkAuth } from '@/components/auth/WechatWorkAuth';
import TranslationHistoryModal from '../TranslationHistoryModal';
import { useDocProcessor } from './DocProcessorContext';

// 定义翻译状态类型
export type TranslationStatus = 'idle' | 'uploading' | 'translating' | 'completed' | 'error';

// 定义翻译任务信息
export interface TranslationTask {
  task_id: string;
  filename: string;
  output_filename: string;
}

// 定义进度信息
export interface ProgressInfo {
  status: string;
  progress: number;
  message: string;
}

// 定义文件就绪信息
export interface FileReadyInfo {
  status: string;
  progress: number;
  message: string; // 下载文件的URL路径
}

// 定义摘要结果类型
export interface SummaryResult {
  summary: string;
}

// 获取API URL和API Key
export const API_URL = process.env.NEXT_PUBLIC_API_URL || '';
export const API_KEY = process.env.NEXT_PUBLIC_API_KEY || '';

// 基础文档处理组件的Props类型
export interface DocBaseProcessingProps {
  children: (props: {
    selectedCategory: string;
    selectedFeature: string;
    isMobile: boolean;
    isPollingActive: React.MutableRefObject<boolean>;
    handleBackToFeatures: () => void;
    currentMode: 'translate' | 'summary' | 'convert';
    setCurrentMode: React.Dispatch<React.SetStateAction<'translate' | 'summary' | 'convert'>>;
    setIsHistoryModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  }) => ReactNode;
}

export function DocBaseProcessing({ children }: DocBaseProcessingProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('word');
  const [selectedFeature, setSelectedFeature] = useState<string>('');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false); // 侧边栏折叠状态
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false); // 翻译历史模态框状态
  const { userInfo } = useWechatWorkAuth(); // 获取用户信息
  const isPollingActive = useRef<boolean>(true); // 控制轮询是否激活
  const pathname = usePathname(); // 获取当前路由
  const { resetState } = useDocProcessor();

  // 添加检测移动设备的状态
  const [isMobile, setIsMobile] = useState(false);

  // 添加当前功能模式状态
  const [currentMode, setCurrentMode] = useState<'translate' | 'summary' | 'convert'>('translate');



  // 检测设备类型并设置相应的状态
  useEffect(() => {
    const checkIsMobile = () => {
      const mobile = window.innerWidth < 768; // 768px是常用的移动设备断点
      setIsMobile(mobile);
      
      // 在移动设备上自动折叠侧边栏
      if (mobile && !isSidebarCollapsed) {
        setIsSidebarCollapsed(true);
      }
    };

    // 初始检查
    checkIsMobile();

    // 监听窗口大小变化
    window.addEventListener('resize', checkIsMobile);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, [isSidebarCollapsed]);

  // 监听页面可见性变化
  useEffect(() => {
    // 处理页面可见性变化
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面不可见时暂停轮询
        isPollingActive.current = false;
      } else {
        // 页面再次可见时
        isPollingActive.current = true;
        console.log('页面重新可见，轮询状态:', isPollingActive.current);
      }
    };

    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 组件卸载时移除事件监听器
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 监听路由变化，并在路由变化时停止轮询
  useEffect(() => {
    if (pathname) {
      isPollingActive.current = false;
    }
  }, [pathname]);

  // 在翻译历史模态框状态变化时处理轮询
  useEffect(() => {
    if (isHistoryModalOpen) {
      // 打开历史模态框时暂停轮询
      isPollingActive.current = false;
    }
  }, [isHistoryModalOpen]);

  // 处理功能卡片选择
  const handleFeatureSelect = (featureId: string) => {
    // 只有在切换到不同类别的功能时才重置轮询状态和Context状态
    const currentCategory = selectedCategory;
    const newCategory = featureId.startsWith('word') ? 'word' :
                       featureId.startsWith('pdf') ? 'pdf' :
                       featureId.startsWith('ppt') ? 'ppt' : 'excel';

    if (currentCategory !== newCategory) {
      // 重置轮询状态
      isPollingActive.current = false;
      // 重置Context状态
      resetState();
    }

    setSelectedFeature(featureId);

    // 根据功能ID设置当前模式
    if (featureId.includes('summary')) {
      setCurrentMode('summary');
    } else if (featureId === 'word-to-pdf') {
      setCurrentMode('convert');
    } else {
      setCurrentMode('translate');
    }

    // 在移动设备上选择功能后自动折叠侧边栏
    if (isMobile && !isSidebarCollapsed) {
      setIsSidebarCollapsed(true);
    }
  };

  // 返回功能卡片选择界面
  const handleBackToFeatures = () => {
    // 重置轮询状态
    isPollingActive.current = false;
    
    setSelectedFeature('');
    resetState();
  };

  // 切换侧边栏折叠状态
  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };



  return (
    <div className="flex h-full overflow-hidden bg-gray-50 dark:bg-gray-900/10" data-tour="doc-processing-panel">
      {/* 左侧分类菜单栏 - 折叠状态 */}
      {isSidebarCollapsed ? (
        <div className="w-16 border-r flex-shrink-0 bg-white dark:bg-gray-800/50 shadow-sm flex flex-col items-center py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="h-10 w-10 rounded-full hover:bg-primary/10 mb-6 bg-white dark:bg-gray-800 border-2 border-primary/30 dark:border-primary/20 shadow-sm"
            title="展开侧边栏"
          >
            <ChevronDown className="h-5 w-5 text-primary dark:text-primary" />
          </Button>
          {docCategories.map((category) => (
            <Button
              key={category.id}
              variant="ghost"
              size="icon"
              onClick={() => {
                if (selectedCategory === category.id && selectedFeature) {
                  // 如果当前已在该分类下且处于功能子页面，先回到功能列表
                  handleBackToFeatures();
                } else if (selectedCategory !== category.id) {
                  // 如果切换到不同的分类，重置状态并切换
                  // 重置轮询状态
                  isPollingActive.current = false;
                  
                  // 重置所有状态
                  setSelectedFeature('');
                  resetState();
                  
                  // 切换到新分类
                  setSelectedCategory(category.id);
                }
                // 如果是移动端，点击后自动关闭侧边栏
                if (isMobile) {
                  setIsSidebarCollapsed(true);
                }
              }}
              className={cn(
                "h-10 w-10 rounded-full hover:bg-muted/50 mb-3 flex items-center justify-center group",
                selectedCategory === category.id 
                  ? `bg-primary hover:bg-primary/90 text-white shadow-md` 
                  : `bg-white dark:bg-gray-800 border-2 border-primary/30 dark:border-primary/20 hover:border-primary/50`
              )}
              title={category.name}
            >
              <div className={cn(
                "flex items-center justify-center",
                selectedCategory === category.id ? "text-white" : category.iconColorClass
              )}>
                {renderIcon(category.icon, { className: "h-5 w-5" })}
              </div>
              {/* 添加悬停提示 */}
              <span className="fixed left-16 ml-2 scale-0 rounded bg-gray-800 p-2 text-xs text-white group-hover:scale-100 z-50 whitespace-nowrap shadow-lg transition-all duration-200">
                {category.name}
              </span>
            </Button>
          ))}
        </div>
      ) : (
        /* 左侧分类菜单栏 - 展开状态 */
        <div className="w-64 border-r flex-shrink-0 bg-white dark:bg-gray-800/30 shadow-sm">
          <div className="p-4 border-b bg-white/80 dark:bg-gray-800/50 flex justify-between items-center">
            <h2 className="text-xl font-bold">功能分类</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="h-8 w-8 rounded-full hover:bg-muted/50"
              title="折叠侧边栏"
            >
              <PanelLeft className="h-4 w-4" />
            </Button>
          </div>
          <ScrollArea className="h-[calc(100%-65px)]">
            <div className="p-3" data-tour="doc-categories">
            {docCategories.map((category) => (
              <button
                key={category.id}
                className={cn(
                  "flex items-center w-full p-3 mb-3 rounded-lg text-left transition-colors",
                  selectedCategory === category.id
                    ? `${category.colorClass} font-bold`
                    : "hover:bg-muted/50 font-semibold"
                )}
                onClick={() => {
                  // 判断是否为当前已选择的分类且有选择功能
                  if (selectedCategory === category.id && selectedFeature) {
                    // 如果点击的是当前选中的类别且有选择的功能，则返回功能列表
                    handleBackToFeatures();
                  } else if (selectedCategory !== category.id) {
                    // 如果点击的是不同的类别，则先重置状态，再切换类别
                    // 重置轮询状态
                    isPollingActive.current = false;
                    
                    // 重置所有状态
                    setSelectedFeature('');
                    resetState();
                    
                    // 切换类别
                    setSelectedCategory(category.id);
                  }
                  
                  // 在移动端自动折叠侧边栏
                  if (isMobile) {
                    setIsSidebarCollapsed(true);
                  }
                }}
              >
                <div className={cn("mr-3 p-2 rounded-md", category.colorClass)}>
                  {renderIcon(category.icon, { className: cn("h-5 w-5", category.iconColorClass) })}
                </div>
                <span className="text-base">{category.name}</span>
                {selectedCategory === category.id && (
                  <span className="ml-auto text-sm font-medium bg-primary/20 dark:bg-primary/30 px-2 py-0.5 rounded-full">
                    ✓
                  </span>
                )}
              </button>
            ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* 右侧内容区域 */}
      <div className={cn(
        "flex-1 flex flex-col overflow-hidden bg-white/50 dark:bg-gray-800/20",
        isSidebarCollapsed && "border-l border-gray-200 dark:border-gray-700"
      )}>
        <div className="p-6 md:p-8 border-b bg-white dark:bg-gray-800/30 shadow-sm">
          <div className="flex items-center justify-center">
            <h2 className="text-2xl md:text-3xl font-bold text-center">
              {selectedCategory === 'word' ? 'Word文档处理' :
               selectedCategory === 'ppt' ? 'PPT文档处理' :
               selectedCategory === 'excel' ? 'Excel文档处理' : 'PDF文档处理'}
            </h2>
          </div>
          {selectedFeature && (
            <p className="text-center text-muted-foreground mt-2">
              {docTools.find(tool => tool.id === selectedFeature)?.title}
            </p>
          )}
        </div>

        {/* 内容区域 */}
        <div className="flex-1 flex flex-col p-6 overflow-auto">
          {/* 如果没有选择功能，显示功能卡片 */}
          {!selectedFeature && (
            <div className="flex-1" data-tour="doc-feature-selection">
              <h3 className="text-xl font-medium mb-6">选择功能</h3>
              <DocFeatureCards
                category={selectedCategory}
                onSelectFeature={handleFeatureSelect}
                isMobile={isMobile}
              />
            </div>
          )}

          {/* 渲染处理器组件 */}
          {selectedFeature && typeof children === 'function' && children({
            selectedCategory,
            selectedFeature,
            isMobile,
            isPollingActive,
            handleBackToFeatures,
            currentMode,
            setCurrentMode,
            setIsHistoryModalOpen
          })}
        </div>
      </div>

      {/* 翻译历史模态框 */}
      {userInfo && (
        <TranslationHistoryModal
          open={isHistoryModalOpen}
          onOpenChange={setIsHistoryModalOpen}
          userId={userInfo.userId}
          mode={currentMode}
        />
      )}

    </div>
  );
}

export default DocBaseProcessing; 