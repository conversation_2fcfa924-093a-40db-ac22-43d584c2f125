"use client"

import React, { createContext, useContext, useState, useRef, useEffect, ReactNode } from 'react';
import { toast } from "sonner";
import FileUploadCard from '../FileUploadCard';
import { TranslationStatus } from '../utils/docProcessUtils';

interface DocProcessorContextType {
  file: File | null;
  setFile: React.Dispatch<React.SetStateAction<File | null>>;
  fileName: string;
  setFileName: React.Dispatch<React.SetStateAction<string>>;
  translationStatus: TranslationStatus;
  setTranslationStatus: React.Dispatch<React.SetStateAction<TranslationStatus>>;
  translationProgress: number;
  setTranslationProgress: React.Dispatch<React.SetStateAction<number>>;
  actualProgress: number;
  setActualProgress: React.Dispatch<React.SetStateAction<number>>;
  translationMessage: string;
  setTranslationMessage: React.Dispatch<React.SetStateAction<string>>;
  taskId: string;
  setTaskId: React.Dispatch<React.SetStateAction<string>>;
  downloadUrl: string;
  setDownloadUrl: React.Dispatch<React.SetStateAction<string>>;
  isDownloading: boolean;
  setIsDownloading: React.Dispatch<React.SetStateAction<boolean>>;
  sourceLanguage: string;
  setSourceLanguage: React.Dispatch<React.SetStateAction<string>>;
  targetLanguage: string;
  setTargetLanguage: React.Dispatch<React.SetStateAction<string>>;
  summaryResult: string;
  setSummaryResult: React.Dispatch<React.SetStateAction<string>>;
  downloadLinkRef: React.MutableRefObject<HTMLAnchorElement | null>;
  pollTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>;
  summaryTextareaRef: React.MutableRefObject<HTMLTextAreaElement | null>;
  reminderCount: number;
  setReminderCount: React.Dispatch<React.SetStateAction<number>>;
  resetState: () => void;
  isMobile: boolean;
}

const DocProcessorContext = createContext<DocProcessorContextType | undefined>(undefined);

export function useDocProcessor() {
  const context = useContext(DocProcessorContext);
  if (context === undefined) {
    throw new Error('useDocProcessor must be used within a DocProcessorProvider');
  }
  return context;
}

interface DocProcessorProviderProps {
  children: ReactNode;
}

export function DocProcessorProvider({ children }: DocProcessorProviderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [translationStatus, setTranslationStatus] = useState<TranslationStatus>('idle');
  const [translationProgress, setTranslationProgress] = useState(0);
  const [actualProgress, setActualProgress] = useState(0);
  const [translationMessage, setTranslationMessage] = useState('');
  const [taskId, setTaskId] = useState<string>('');
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [sourceLanguage, setSourceLanguage] = useState<string>('zh');
  const [targetLanguage, setTargetLanguage] = useState<string>('en');
  const [summaryResult, setSummaryResult] = useState<string>('');
  const [reminderCount, setReminderCount] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  
  const downloadLinkRef = useRef<HTMLAnchorElement | null>(null);
  const pollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const summaryTextareaRef = useRef<HTMLTextAreaElement | null>(null);

  // 检测移动设备
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // 初始检查
    checkIsMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkIsMobile);
    
    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // 平滑更新进度的效果
  useEffect(() => {
    let animationFrameId: number;
    let timer: NodeJS.Timeout;

    // 只有在翻译状态下才启动进度动画
    if (translationStatus === 'translating' && translationProgress < actualProgress) {
      // 使用setTimeout来控制更新频率，避免更新过快
      timer = setTimeout(() => {
        // 使用requestAnimationFrame来保证动画的流畅性
        animationFrameId = requestAnimationFrame(() => {
          // 每次增加1%，直到达到实际进度
          setTranslationProgress(prev => Math.min(prev + 1, actualProgress));
        });
      }, 50); // 50ms的延迟，可以根据需要调整
    }

    return () => {
      // 清理定时器和动画帧
      if (timer) clearTimeout(timer);
      if (animationFrameId) cancelAnimationFrame(animationFrameId);
    };
  }, [translationProgress, actualProgress, translationStatus]);

  // 创建下载链接元素
  useEffect(() => {
    // 创建下载链接元素
    if (!downloadLinkRef.current) {
      const link = document.createElement('a');
      link.style.display = 'none';
      document.body.appendChild(link);
      downloadLinkRef.current = link;
    }

    // 组件卸载时移除链接元素
    return () => {
      if (downloadLinkRef.current) {
        document.body.removeChild(downloadLinkRef.current);
        downloadLinkRef.current = null;
      }
    };
  }, []);

  // 组件卸载时清理所有资源
  useEffect(() => {
    return () => {
      // 清理下载链接
      if (downloadLinkRef.current) {
        document.body.removeChild(downloadLinkRef.current);
        downloadLinkRef.current = null;
      }
      
      // 清理轮询定时器
      if (pollTimeoutRef.current) {
        clearTimeout(pollTimeoutRef.current);
        pollTimeoutRef.current = null;
        console.log('用户离开页面，停止轮询任务进度');
      }
    };
  }, []);

  // 监听进度变化，当进度达到100%时切换状态
  useEffect(() => {
    if (translationProgress >= 100 && translationStatus === 'translating') {
      // 确保下载URL已设置
      if (downloadUrl) {
        setTranslationStatus('completed');
      }
    }
  }, [translationProgress, translationStatus, downloadUrl]);

  // 重置状态函数
  const resetState = () => {
    setFile(null);
    setFileName('');
    setTranslationStatus('idle');
    setTranslationProgress(0);
    setActualProgress(0);
    setTranslationMessage('');
    setTaskId('');
    setDownloadUrl('');
    setSummaryResult('');
    setReminderCount(0);
    // 不重置语言选择，保持用户上次设置
  };

  const value = {
    file,
    setFile,
    fileName,
    setFileName,
    translationStatus,
    setTranslationStatus,
    translationProgress,
    setTranslationProgress,
    actualProgress,
    setActualProgress,
    translationMessage,
    setTranslationMessage,
    taskId,
    setTaskId,
    downloadUrl,
    setDownloadUrl,
    isDownloading,
    setIsDownloading,
    sourceLanguage,
    setSourceLanguage,
    targetLanguage,
    setTargetLanguage,
    summaryResult,
    setSummaryResult,
    downloadLinkRef,
    pollTimeoutRef,
    summaryTextareaRef,
    reminderCount,
    setReminderCount,
    resetState,
    isMobile
  };

  return (
    <DocProcessorContext.Provider value={value}>
      {children}
    </DocProcessorContext.Provider>
  );
}

// 上传文件卡片组件
export interface UploadCardProps {
  acceptedFileTypes?: string;
  maxSizeMB?: number;
  title?: string;
  iconColor?: string;
  isMobile?: boolean;
  onViewHistory?: () => void;
}

export function DocUploadCard({ 
  acceptedFileTypes = ".docx,.pdf,.pptx,.xlsx", 
  maxSizeMB = 100, 
  title = "上传文档",
  iconColor = "text-blue-500",
  isMobile = false,
  onViewHistory
}: UploadCardProps) {
  const { 
    setFile, 
    setFileName, 
    setTranslationStatus 
  } = useDocProcessor();

  return (
    <FileUploadCard
      onFileSelected={(selectedFile) => {
        setFile(selectedFile);
        setFileName(selectedFile.name);
        setTranslationStatus('uploading');

        // 模拟上传过程
        setTimeout(() => {
          setTranslationStatus('idle');
          toast.success('文件上传成功');
        }, 500);
      }}
      onViewHistory={onViewHistory}
      acceptedFileTypes={acceptedFileTypes}
      maxSizeMB={maxSizeMB}
      title={title}
      iconColor={iconColor}
      isMobile={isMobile}
    />
  );
} 