"use client"

import React from 'react';
import { DocBaseProcessing } from './base/DocBaseProcessing';
import { DocProcessorProvider } from './base/DocProcessorContext';
import WordDocProcessor from './processors/WordDocProcessor';
import PDFDocProcessor from './processors/PDFDocProcessor';
import PPTDocProcessor from './processors/PPTDocProcessor';
import ExcelDocProcessor from './processors/ExcelDocProcessor';

export default function DocProcessingPanel() {
  return (
    <DocProcessorProvider>
      <DocBaseProcessing>
        {(props) => {
          // 根据选择的类别渲染对应的处理器组件
          const { selectedCategory, selectedFeature } = props;
          
          if (selectedCategory === 'word' && selectedFeature) {
            return <WordDocProcessor {...props} />;
          }
          
          if (selectedCategory === 'pdf' && selectedFeature) {
            return <PDFDocProcessor {...props} />;
          }
          
          if (selectedCategory === 'ppt' && selectedFeature) {
            return <PPTDocProcessor {...props} />;
          }
          
          if (selectedCategory === 'excel' && selectedFeature) {
            return <ExcelDocProcessor {...props} />;
          }
          
          // 如果没有选择特定功能，则不渲染任何处理器
          return null;
        }}
      </DocBaseProcessing>
    </DocProcessorProvider>
  );
}
