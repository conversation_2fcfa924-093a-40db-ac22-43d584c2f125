"use client"

import React, { useCallback, useRef } from 'react';
import { toast } from "sonner";
import { DocUploadCard, useDocProcessor } from '../base/DocProcessorContext';
import { 
  FileUploadedView, 
  ProcessingProgressView, 
  ProcessingErrorView, 
  TranslationCompletedView,
  SummaryResultView
} from './DocCommonComponents';
import { API_URL, API_KEY, retryDownloadWithBackoff, setupPolling } from '../utils/docProcessUtils';
import { useWechatWorkAuth } from '@/components/auth/WechatWorkAuth';

type PPTProcessorProps = {
  selectedFeature: string;
  isMobile?: boolean;
  isPollingActive: React.MutableRefObject<boolean>;
  handleBackToFeatures: () => void;
  setIsHistoryModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function PPTDocProcessor({ 
  selectedFeature,
  isMobile = false,
  isPollingActive,
  handleBackToFeatures,
  setIsHistoryModalOpen
}: PPTProcessorProps) {
  // 为PPT处理器创建独立的轮询控制
  const pptPollingActive = useRef<boolean>(true);

  const {
    file,
    fileName,
    setFileName,
    translationStatus,
    setTranslationStatus,
    setTranslationProgress,
    setActualProgress,
    setTranslationMessage,
    taskId,
    setTaskId,
    downloadUrl,
    setDownloadUrl,
    sourceLanguage,
    targetLanguage,
    setIsDownloading,
    downloadLinkRef,
    pollTimeoutRef,
    resetState,
    reminderCount,
    setReminderCount,
    setSummaryResult
  } = useDocProcessor();

  const { userInfo } = useWechatWorkAuth();

  // 处理翻译文档
  const handleProcessDocument = useCallback(async () => {
    if (!file) return;

    // 清理可能存在的轮询定时器
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
      pollTimeoutRef.current = null;
    }
    
    // 重新激活轮询
    pptPollingActive.current = true;
    // 重置提醒次数
    setReminderCount(0);

    try {
      setTranslationStatus('translating');
      setTranslationProgress(0);
      setActualProgress(0);
      setTranslationMessage('准备处理文档...');

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('source_language', sourceLanguage);
      formData.append('target_language', targetLanguage);
      // 添加用户ID
      if (userInfo && userInfo.userId) {
        formData.append('user_id', userInfo.userId);
      }

      // 发起翻译请求
      const response = await fetch(`${API_URL}/document/translate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`处理请求失败: ${response.status}`);
      }

      // 解析响应获取task_id
      const responseData = await response.json();
      if (responseData.code !== 0) {
        throw new Error(`处理请求失败: ${responseData.message || '未知错误'}`);
      }
      
      const data = responseData.data;
      const taskId = data.task_id;
      console.log('收到任务ID:', taskId);
      setTaskId(taskId);

      // 如果已经提供了输出文件名，立即保存
      if (data.output_filename) {
        setFileName(data.output_filename);
      }
      
      setTranslationMessage(`开始处理文档: ${data.filename || file.name}`);
      
      // 设置轮询检查任务进度
      await setupPolling(
        taskId,
        '/document/translate/progress',
        pptPollingActive,
        pollTimeoutRef,
        setActualProgress,
        setTranslationMessage,
        (progressData) => {
          // 处理完成的文档，传入当前的taskId
          handleProcessComplete(progressData.message || progressData.download_url, taskId);
          return true;
        },
        (error) => {
          console.error('轮询过程中出错:', error);
          setTranslationStatus('error');
          setTranslationMessage(`处理失败: ${error.message}`);
          toast.error('文档处理失败，请稍后重试');
        },
        reminderCount,
        setReminderCount
      );

    } catch (error) {
      console.error('处理文档时出错:', error);
      setTranslationStatus('error');
      setTranslationMessage('文档翻译失败，请稍后重试');
      toast.error('文档翻译失败，请稍后重试');
    }
  }, [file, fileName, sourceLanguage, targetLanguage, pollTimeoutRef, reminderCount]);

  // 处理摘要生成
  const handleGenerateSummary = useCallback(async () => {
    if (!file) return;

    // 清理可能存在的轮询定时器
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
      pollTimeoutRef.current = null;
    }
    
    // 重新激活轮询
    pptPollingActive.current = true;
    // 重置提醒次数
    setReminderCount(0);

    try {
      setTranslationStatus('translating');
      setTranslationProgress(0);
      setActualProgress(0);
      setTranslationMessage('准备生成摘要...');
      setSummaryResult('');

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      // 添加用户ID
      if (userInfo && userInfo.userId) {
        formData.append('user_id', userInfo.userId);
      }

      // 发起摘要请求
      const response = await fetch(`${API_URL}/document/summary`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`处理请求失败: ${response.status}`);
      }

      // 解析响应获取task_id
      const responseData = await response.json();
      if (responseData.code !== 0) {
        throw new Error(`处理请求失败: ${responseData.message || '未知错误'}`);
      }
      
      const data = responseData.data;
      const taskId = data.task_id;
      console.log('收到任务ID:', taskId);
      setTaskId(taskId);
      
      setTranslationMessage(`开始生成摘要: ${data.filename || file.name}`);
      
      // 设置轮询检查任务进度
      await setupPolling(
        taskId,
        '/document/summary/progress',
        pptPollingActive,
        pollTimeoutRef,
        setActualProgress,
        setTranslationMessage,
        (progressData) => {
          // 直接从progressData.result获取摘要结果
          setSummaryResult(progressData.result.summary || '未能获取到摘要结果');
          
          // 强制设置实际进度为100%，确保动画会完成
          setActualProgress(100);
          
          // 使用一个更可靠的方式来检测进度完成
          const completeTimeout = setTimeout(() => {
            // 直接设置进度为100%并更新状态，确保UI正确显示
            setTranslationProgress(100);
            setTranslationStatus('completed');
            toast.success('摘要生成完成！');
          }, 1000); // 给足够的时间让动画完成
          
          return true;
        },
        (error) => {
          console.error('轮询过程中出错:', error);
          setTranslationStatus('error');
          setTranslationMessage(`处理失败: ${error.message}`);
          toast.error('摘要生成失败，请稍后重试');
        },
        reminderCount,
        setReminderCount
      );

    } catch (error) {
      console.error('生成摘要时出错:', error);
      setTranslationStatus('error');
      setTranslationMessage('摘要生成失败，请稍后重试');
      toast.error('摘要生成失败，请稍后重试');
    }
  }, [file, fileName, pollTimeoutRef, reminderCount]);

  // 处理完成的文档
  const handleProcessComplete = useCallback((url: string, completedTaskId: string) => {
    // 使用传入的taskId构建下载链接，确保有斜杠分隔
    const downloadEndpoint = completedTaskId ? `/document/translate/download/${completedTaskId}` : '';
    if (!completedTaskId) {
      console.error('无法构建下载链接：taskId为空');
      return;
    }
    console.log('构建下载链接，taskId:', completedTaskId, '下载地址:', `${API_URL}${downloadEndpoint}`);
    const fullUrl = `${API_URL}${downloadEndpoint}`;
    setDownloadUrl(fullUrl);

    // 强制设置实际进度为100%，确保动画会完成
    setActualProgress(100);

    // 使用一个更可靠的方式来检测进度完成
    const completeTimeout = setTimeout(() => {
      // 直接设置进度为100%并更新状态，确保UI正确显示
      setTranslationProgress(100);
      setTranslationStatus('completed');
      toast.success('文档处理完成！');
    }, 1000); // 给足够的时间让动画完成

    // 清理函数会在组件卸载或者依赖项变化时执行
    return () => {
      clearTimeout(completeTimeout);
    };
  }, []);

  // 文件下载函数
  const downloadFile = useCallback(async () => {
    if (!downloadUrl) {
      toast.error('下载链接不可用');
      return;
    }

    if (!taskId) {
      toast.error('任务ID不存在，无法下载');
      console.error('下载失败：taskId为空');
      return;
    }

    try {
      setIsDownloading(true);

      // 确保使用正确的下载链接
      const downloadEndpoint = `/document/translate/download/${taskId}`;
      const downloadLink = `${API_URL}${downloadEndpoint}`;
      console.log('准备下载文件，使用URL:', downloadLink);

      // 使用带有重试功能的请求
      toast.info('正在准备下载文件...');
      const response = await retryDownloadWithBackoff(downloadLink);
      toast.info('文件已准备好，正在下载...');

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const blob = await response.blob();

      // 使用预先创建的下载链接
      if (downloadLinkRef.current) {
        const url = window.URL.createObjectURL(blob);
        // 优先使用服务器返回的文件名
        const suggestedFilename = fileName || `translated_document_${taskId}.pptx`;

        downloadLinkRef.current.href = url;
        downloadLinkRef.current.download = suggestedFilename;
        downloadLinkRef.current.click();

        // 释放对象URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 100);

        toast.success('文档已下载');
      }
    } catch (error) {
      console.error('下载文件时出错:', error);
      toast.error(`下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsDownloading(false);
    }
  }, [downloadUrl, taskId, API_KEY, fileName]);

  const handleBackToBackgroundProcessing = () => {
    // 将任务转到后台执行
    if (pollTimeoutRef.current) {
      clearTimeout(pollTimeoutRef.current);
      pollTimeoutRef.current = null;
    }
    // 显示提示信息
    toast.success('处理任务已转入后台执行，可在"历史记录"中查看进度');
    
    // 重置界面状态，但不影响后台翻译进程
    resetState();
  };

  // 渲染PPT翻译组件
  if (selectedFeature === 'ppt-translate') {
    return (
      <>
        {translationStatus === 'idle' && !file && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8">
              <DocUploadCard 
                acceptedFileTypes=".pptx"
                maxSizeMB={100}
                title="上传PPT文档"
                iconColor="text-red-500"
                isMobile={isMobile}
                onViewHistory={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </div>
        )}

        {translationStatus === 'uploading' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8">
              <DocUploadCard 
                acceptedFileTypes=".pptx"
                maxSizeMB={100}
                title="上传PPT文档"
                iconColor="text-red-500"
                isMobile={isMobile}
                onViewHistory={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </div>
        )}

        {file && translationStatus === 'idle' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <FileUploadedView 
                onProcess={handleProcessDocument} 
                showLanguageSelect={true}
                processButtonText="开始翻译"
                fileIcon="document"
              />
            </div>
          </div>
        )}

        {translationStatus === 'translating' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <ProcessingProgressView backToBackground={handleBackToBackgroundProcessing} />
            </div>
          </div>
        )}

        {translationStatus === 'error' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <ProcessingErrorView />
            </div>
          </div>
        )}

        {translationStatus === 'completed' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <TranslationCompletedView downloadFile={downloadFile} />
            </div>
          </div>
        )}
      </>
    );
  }
  
  // 渲染PPT摘要组件
  if (selectedFeature === 'ppt-summary') {
    return (
      <>
        {translationStatus === 'idle' && !file && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8">
              <DocUploadCard 
                acceptedFileTypes=".pptx"
                maxSizeMB={100}
                title="上传PPT文档"
                iconColor="text-red-500"
                isMobile={isMobile}
                onViewHistory={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </div>
        )}

        {translationStatus === 'uploading' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8">
              <DocUploadCard 
                acceptedFileTypes=".pptx"
                maxSizeMB={100}
                title="上传PPT文档"
                iconColor="text-red-500"
                isMobile={isMobile}
                onViewHistory={() => setIsHistoryModalOpen(true)}
              />
            </div>
          </div>
        )}

        {file && translationStatus === 'idle' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <FileUploadedView 
                onProcess={handleGenerateSummary} 
                showLanguageSelect={false}
                processButtonText="生成摘要"
                fileIcon="document"
              />
            </div>
          </div>
        )}

        {translationStatus === 'translating' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <ProcessingProgressView backToBackground={handleBackToBackgroundProcessing} />
            </div>
          </div>
        )}

        {translationStatus === 'error' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <ProcessingErrorView />
            </div>
          </div>
        )}

        {translationStatus === 'completed' && (
          <div className="flex-1 flex items-center justify-center">
            <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 md:px-8 flex flex-col items-center">
              <SummaryResultView />
            </div>
          </div>
        )}
      </>
    );
  }

  return null;
} 