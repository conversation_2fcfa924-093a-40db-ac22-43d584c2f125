"use client"

import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { FileText, Loader2, Download, AlertTriangle, Check, Upload, FileSpreadsheet } from 'lucide-react';
import { useDocProcessor } from '../base/DocProcessorContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// 文件上传成功组件
export function FileUploadedView({ 
  onProcess, 
  showLanguageSelect = true, 
  processButtonText = "开始处理",
  fileIcon = "document"
}: { 
  onProcess: () => void, 
  showLanguageSelect?: boolean, 
  processButtonText?: string,
  fileIcon?: "document" | "spreadsheet"
}) {
  const { 
    fileName, 
    setFile, 
    setFileName, 
    sourceLanguage, 
    setSourceLanguage, 
    targetLanguage, 
    setTargetLanguage,
    isMobile 
  } = useDocProcessor();

  return (
    <div className="w-full p-8 border-2 border-purple-200 dark:border-purple-800/30 rounded-xl bg-gradient-to-br from-purple-50/50 to-indigo-50/50 dark:from-purple-950/20 dark:to-indigo-950/20 shadow-lg backdrop-blur-sm" data-tour="doc-upload">
      <div className="flex flex-col items-center mb-6">
        {/* 现代化文件图标容器 */}
        <div className="mb-4 flex items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg w-16 h-16">
          {fileIcon === "document" ? (
            <FileText className="h-8 w-8 text-white" />
          ) : (
            <FileSpreadsheet className="h-8 w-8 text-white" />
          )}
        </div>
        <h3 className="text-xl font-semibold mb-3 text-center bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">文件已上传成功</h3>
      </div>
      <div className="flex justify-between items-center mb-4 p-4 bg-gradient-to-r from-white/80 to-purple-50/50 dark:from-gray-800/50 dark:to-purple-950/30 border-2 border-purple-200/50 dark:border-purple-700/30 rounded-xl shadow-inner backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <span className="font-semibold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">文件名称:</span>
          <span className="text-sm text-muted-foreground">{fileName}</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="border-2 border-purple-300 hover:border-purple-400 hover:bg-purple-50 dark:hover:bg-purple-950/20 transition-all duration-300"
          onClick={() => {
            setFile(null);
            setFileName('');
          }}
        >
          更换文件
        </Button>
      </div>
      
      {/* 语言选择区域 */}
      {showLanguageSelect && (
        <div className="p-4 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/20 dark:to-purple-950/20 border-2 border-indigo-200/50 dark:border-indigo-700/30 rounded-xl mb-6 shadow-inner backdrop-blur-sm" data-tour="doc-settings">
          <h4 className="font-semibold mb-4 text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">语言设置</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="source-language" className="text-sm font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                原文语言
              </label>
              <Select
                value={sourceLanguage}
                onValueChange={setSourceLanguage}
              >
                <SelectTrigger id="source-language" className="w-full border-2 border-indigo-200 dark:border-indigo-700/30 focus:border-indigo-500 transition-all duration-300">
                  <SelectValue placeholder="选择原文语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">英文</SelectItem>
                  <SelectItem value="fr">法语</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="target-language" className="text-sm font-semibold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                目标语言
              </label>
              <Select
                value={targetLanguage}
                onValueChange={setTargetLanguage}
              >
                <SelectTrigger id="target-language" className="w-full border-2 border-indigo-200 dark:border-indigo-700/30 focus:border-indigo-500 transition-all duration-300">
                  <SelectValue placeholder="选择目标语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="en">英文</SelectItem>
                  <SelectItem value="fr">法语</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      <Button
        className="w-full mt-4 py-4 text-base bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 shadow-lg transition-all duration-300 font-semibold"
        onClick={onProcess}
        data-tour="doc-start-process">
      >
        {processButtonText}
      </Button>
    </div>
  );
}

// 处理进度组件
export function ProcessingProgressView({
  backToBackground
}: {
  backToBackground: () => void
}) {
  const {
    translationProgress,
    translationMessage,
    isMobile
  } = useDocProcessor();

  return (
    <div className="w-full p-8 border-2 border-gradient-to-r from-blue-200 to-purple-200 dark:from-blue-800/30 dark:to-purple-800/30 rounded-xl bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20 shadow-lg backdrop-blur-sm" data-tour="doc-progress">
      {/* Logo图标和标题 */}
      <div className="flex flex-col items-center mb-6">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 animate-pulse">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            className="w-6 h-6 text-white"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="3" y="11" width="18" height="10" rx="2"></rect>
            <circle cx="12" cy="5" r="2"></circle>
            <path d="M12 7v4"></path>
            <line x1="8" y1="16" x2="8" y2="16"></line>
            <line x1="16" y1="16" x2="16" y2="16"></line>
          </svg>
        </div>
        <h3 className={cn(
          "font-semibold text-center gradient-text-safe gradient-text-fallback",
          isMobile ? "text-lg" : "text-xl"
        )}>文档处理中，请稍等</h3>
      </div>

      {/* 现代化进度条 */}
      <div className="relative mb-4">
        <Progress
          value={translationProgress}
          className={cn(
            "mb-3 bg-gray-200 dark:bg-gray-700",
            isMobile ? "h-3" : "h-4"
          )}
          indicatorColor="bg-gradient-to-r from-blue-500 to-purple-600"
        />
        {/* 进度条上的动画光效 */}
        <div
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full animate-pulse"
          style={{ width: `${translationProgress}%` }}
        />
      </div>

      <p className={cn(
        "text-center font-medium gradient-text-safe gradient-text-fallback",
        isMobile ? "text-sm" : "text-base"
      )}>
        已完成 {Math.round(translationProgress)}%
      </p>

      {translationMessage && (
        <div className="flex items-center justify-center mt-3">
          {/* 加载动画点 */}
          <div className="flex items-center gap-1 mr-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
          <p className="text-center text-sm text-muted-foreground">
            {translationMessage}
          </p>
        </div>
      )}

      <div className="flex justify-center mt-6">
        <Button
          variant="outline"
          className={cn(
            "px-6 py-2 h-auto border-gradient-to-r from-blue-300 to-purple-300 hover:from-blue-400 hover:to-purple-400 transition-all duration-300",
            isMobile ? "text-sm" : "text-base"
          )}
          onClick={backToBackground}
        >
          后台执行
        </Button>
      </div>
    </div>
  );
}

// 处理失败组件
export function ProcessingErrorView() {
  const {
    resetState,
    isMobile
  } = useDocProcessor();

  return (
    <div className="w-full p-8 border-2 border-red-200 dark:border-red-800/30 rounded-xl bg-gradient-to-br from-red-50/50 to-orange-50/50 dark:from-red-950/20 dark:to-orange-950/20 shadow-lg backdrop-blur-sm">
      <div className="flex flex-col items-center justify-center">
        <div className={cn(
          "mb-4 flex items-center justify-center rounded-xl bg-gradient-to-br from-red-500 to-orange-500 shadow-lg",
          isMobile ? "w-12 h-12" : "w-16 h-16"
        )}>
          <AlertTriangle className={cn(
            "text-white",
            isMobile ? "h-6 w-6" : "h-8 w-8"
          )} />
        </div>
        <h3 className={cn(
          "font-semibold mb-2 text-center bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent",
          isMobile ? "text-lg" : "text-xl"
        )}>处理失败</h3>
        <p className={cn(
          "text-center text-muted-foreground mb-6",
          isMobile ? "text-sm" : "text-base"
        )}>
          很抱歉，处理过程中遇到了问题，请稍后重试
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-2 w-full">
          <Button
            variant="default"
            className={cn(
              "flex items-center gap-2",
              isMobile ? "px-4 py-2 h-auto text-sm" : "px-6 py-2 h-auto text-base"
            )}
            onClick={resetState}
          >
            <Upload className={isMobile ? "h-3.5 w-3.5" : "h-4 w-4"} />
            重新上传文档
          </Button>
        </div>
      </div>
    </div>
  );
}

// 翻译完成组件
export function TranslationCompletedView({
  downloadFile
}: {
  downloadFile: () => void
}) {
  const {
    fileName,
    resetState,
    isDownloading,
    isMobile
  } = useDocProcessor();

  return (
    <div className="w-full p-8 border-2 border-green-200 dark:border-green-800/30 rounded-xl bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20 shadow-lg backdrop-blur-sm" data-tour="doc-result">
      <div className="flex flex-col items-center mb-6">
        <div className={cn(
          "mb-4 flex items-center justify-center rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg",
          isMobile ? "w-12 h-12" : "w-16 h-16"
        )}>
          <FileText className={cn(
            "text-white",
            isMobile ? "h-6 w-6" : "h-8 w-8"
          )} />
        </div>
        <h3 className={cn(
          "font-semibold mb-3 text-center bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",
          isMobile ? "text-lg" : "text-xl"
        )}>文档处理完成</h3>
        <p className="text-center text-muted-foreground mb-2">文件名称: {fileName}</p>
      </div>
      <div className="p-4 mb-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 border-2 border-amber-200 dark:border-amber-800/30 rounded-xl flex flex-col items-center text-center shadow-sm">
        <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-lg flex items-center justify-center mb-3">
          <AlertTriangle className="h-4 w-4 text-white" />
        </div>
        <p className={cn(
          "text-amber-700 dark:text-amber-300 font-medium",
          isMobile ? "text-xs" : "text-sm"
        )}>
          翻译内容为AI翻译，下载后请仔细核对内容并检查排版，如有问题请手动修改。
        </p>
      </div>
      <div className={cn(
        "flex justify-center gap-4",
        isMobile && "flex-col"
      )}>
        <Button
          variant="outline"
          className={cn(
            "px-6 py-2 h-auto border-2 border-green-300 hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-950/20 transition-all duration-300",
            isMobile ? "text-sm" : "text-base"
          )}
          onClick={resetState}
        >
          处理新文档
        </Button>
        <Button
          className={cn(
            "flex items-center gap-2 h-auto px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 shadow-lg transition-all duration-300",
            isMobile ? "text-sm" : "text-base"
          )}
          onClick={downloadFile}
          disabled={isDownloading}
        >
          {isDownloading ? (
            <>
              <Loader2 className={isMobile ? "h-3.5 w-3.5 animate-spin" : "h-4 w-4 animate-spin"} />
              正在下载...
            </>
          ) : (
            <>
              <Download className={isMobile ? "h-3.5 w-3.5" : "h-4 w-4"} />
              下载处理结果
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

// 摘要结果组件
export function SummaryResultView() {
  const {
    fileName,
    resetState,
    summaryResult,
    summaryTextareaRef,
    isMobile
  } = useDocProcessor();

  return (
    <div className="w-full p-8 border-2 border-blue-200 dark:border-blue-800/30 rounded-xl bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 shadow-lg backdrop-blur-sm">
      <div className="flex flex-col items-center mb-6">
        <div className={cn(
          "mb-4 flex items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg",
          isMobile ? "w-12 h-12" : "w-16 h-16"
        )}>
          <FileText className={cn(
            "text-white",
            isMobile ? "h-6 w-6" : "h-8 w-8"
          )} />
        </div>
        <h3 className={cn(
          "font-semibold mb-3 text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",
          isMobile ? "text-lg" : "text-xl"
        )}>摘要生成完成</h3>
        <p className="text-center text-muted-foreground mb-2">文件名称: {fileName}</p>
      </div>

      <div className="p-4 bg-gradient-to-br from-white/80 to-blue-50/50 dark:from-gray-800/50 dark:to-blue-950/30 border-2 border-blue-200/50 dark:border-blue-700/30 rounded-xl mb-6 shadow-inner backdrop-blur-sm">
        <h4 className="font-semibold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">摘要内容：</h4>
        <textarea
          className="w-full p-4 border-2 border-blue-200 dark:border-blue-700/30 rounded-lg bg-white/90 dark:bg-gray-800/70 text-gray-800 dark:text-gray-200 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 backdrop-blur-sm"
          value={summaryResult}
          readOnly
          ref={summaryTextareaRef}
          style={{
            height: isMobile ? '150px' : '256px',
            fontSize: isMobile ? '14px' : '16px'
          }}
        />
      </div>
      
      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          className={cn(
            "px-6 py-2 h-auto border-2 border-blue-300 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-all duration-300",
            isMobile ? "text-sm" : "text-base"
          )}
          onClick={resetState}
        >
          处理新文档
        </Button>
        <Button
          className={cn(
            "flex items-center gap-2 h-auto px-6 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg transition-all duration-300",
            isMobile ? "text-sm" : "text-base"
          )}
          onClick={() => {
            // 获取文本框元素
            const textarea = summaryTextareaRef.current;
            if (textarea) {
              // 选中文本框中的所有内容
              textarea.focus();
              textarea.select();
              
              try {
                // 尝试使用document.execCommand执行复制命令
                const successful = document.execCommand('copy');
                if (successful) {
                  toast.success('已自动复制选中摘要内容');
                } else {
                  // 如果execCommand不成功，提示用户手动复制
                  toast.info('请按Ctrl+C复制已选中的摘要内容');
                }
              } catch (err) {
                // 如果执行出错，提示用户手动复制
                console.error('复制操作失败:', err);
                toast.info('请手动选择并复制摘要内容');
              }
            } else {
              toast.error('无法找到摘要内容框');
            }
          }}
        >
          复制摘要
        </Button>
      </div>
    </div>
  );
} 