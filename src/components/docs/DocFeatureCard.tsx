"use client"

import React from 'react';
import { cn } from "@/lib/utils";
import { renderIcon, IconName } from "@/config/iconMap";
import { recordToolClick } from '@/lib/chatService';
import { docCategories } from '@/config/docTools';

export interface DocFeatureCardProps {
  id: string;
  title: string;
  description: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
  onSelect: (id: string) => void;
  category: string;
  compact?: boolean;
}

/**
 * A component that renders a document feature card with an icon, title and description
 * Responsive design for both mobile and desktop views
 */
export function DocFeatureCard({
  id,
  title,
  description,
  icon,
  colorClass,
  iconColorClass,
  onSelect,
  category,
  compact = false
}: DocFeatureCardProps) {
  const handleClick = async () => {
    try {
      let userId = '';
      try {
        const userInfoJson = localStorage.getItem('wechat_work_user');
        if (userInfoJson) {
          const userInfo = JSON.parse(userInfoJson);
          userId = userInfo.userId;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }

      let toolType = '文档处理';
      
      if (category) {
        const categoryObj = docCategories.find(cat => cat.id === category);
        if (categoryObj) {
          toolType = categoryObj.name;
        }
      }
      
      if (userId) {
        await recordToolClick(userId, title, toolType);
      }
    } catch (error) {
      console.error('记录工具点击失败:', error);
    } finally {
      onSelect(id);
    }
  };

  return (
    <button
      className={cn(
        "flex flex-col h-full rounded-xl border border-muted/30 bg-white dark:bg-slate-800/90 hover:bg-gray-50 dark:hover:bg-slate-700/90 transition-all text-left shadow-sm hover:shadow-md hover:scale-[1.02] cursor-pointer",
        compact ? "p-4" : "p-6"
      )}
      onClick={handleClick}
      data-feature-card={id}
    >
      <div className="flex items-start mb-2">
        <div className={cn(
          "mr-4 rounded-lg overflow-hidden flex items-center justify-center shadow-sm border border-gray-200 dark:border-gray-600", 
          "bg-white dark:bg-gray-700", 
          compact ? "h-10 w-10" : "h-14 w-14"
        )}>
          {renderIcon(icon, { 
            className: cn(
              "h-7 w-7", 
              iconColorClass,
              compact && "h-5 w-5"
            ) 
          })}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={cn(
              "font-bold truncate",
              compact ? "text-base" : "text-lg"
            )}>{title}</h4>
          </div>
        </div>
      </div>
      <p className={cn(
        "text-muted-foreground",
        compact ? "text-xs line-clamp-2" : "text-sm line-clamp-3",
        "mt-2"
      )}>{description}</p>
    </button>
  );
}
