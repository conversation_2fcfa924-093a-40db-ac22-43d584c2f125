"use client"

import React, { useEffect, useRef } from 'react';
import { Button } from "@/components/ui/button";

interface ChatChartPanelProps {
  onSendMessage?: (message: string) => void;
}

export default function ChatChartPanel({ onSendMessage }: ChatChartPanelProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);



  useEffect(() => {
    // 当组件挂载时，可以在这里添加任何初始化逻辑
    if (onSendMessage) {
      // 将 onSendMessage 函数挂载到 window 对象上，以便在全局范围内访问
      (window as any).__handleFeatureCardClick = (message: string) => {
        onSendMessage(message);
      };

      // 清理函数
      return () => {
        delete (window as any).__handleFeatureCardClick;
      };
    }
  }, [onSendMessage]);



  return (
    <div className="flex flex-col h-full bg-background" style={{ position: 'relative', zIndex: 10 }} data-tour="visualization-panel">
      <div className="flex-1 overflow-hidden" style={{ position: 'relative' }}>
        <iframe
          ref={iframeRef}
          src="/ChatChart.html"
          className="w-full h-full border-0"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            overflow: 'auto'
          }}
          title="Excel数据可视化助手"
          sandbox="allow-scripts allow-same-origin allow-forms allow-downloads allow-modals allow-popups"
        />
      </div>

    </div>
  );
}
