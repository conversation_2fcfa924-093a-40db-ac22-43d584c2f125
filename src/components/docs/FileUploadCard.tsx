"use client"

import React, { useState, useRef, DragEvent, ChangeEvent } from 'react';
import { cn } from "@/lib/utils";
import { FileText, Upload, X, Loader2, History } from "lucide-react";
import { Button } from "@/components/ui/button";

interface FileUploadCardProps {
  onFileSelected: (file: File) => void;
  acceptedFileTypes?: string;
  maxSizeMB?: number;
  className?: string;
  isUploading?: boolean;
  onViewHistory?: () => void;
  title?: string; // 添加标题属性
  iconColor?: string; // 添加图标颜色属性
  isMobile?: boolean; // 添加移动端标识
}

export default function FileUploadCard({
  onFileSelected,
  acceptedFileTypes = ".docx",
  maxSizeMB = 10,
  className,
  isUploading = false,
  onViewHistory,
  title = "上传Word文档", // 默认标题为上传Word文档
  iconColor = "text-blue-500", // 默认图标颜色为蓝色
  isMobile = false // 默认非移动端
}: FileUploadCardProps) {
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
  };

  const validateFile = (file: File): boolean => {
    const fileType = file.name.split('.').pop()?.toLowerCase();
    const acceptedTypes = acceptedFileTypes.split(',').map(type =>
      type.startsWith('.') ? type.substring(1) : type
    );

    if (!acceptedTypes.includes(fileType || '')) {
      setError(`不支持的文件类型，请上传 ${acceptedFileTypes} 格式的文件`);
      return false;
    }

    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSizeMB) {
      setError(`文件大小超过限制，最大支持 ${maxSizeMB}MB`);
      return false;
    }

    setError(null);
    return true;
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        onFileSelected(file);
      }
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        onFileSelected(file);
      }
      e.target.value = '';
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center border-2 border-dashed rounded-xl transition-all duration-300 shadow-lg backdrop-blur-sm",
        isDraggingOver
          ? "border-blue-400 bg-gradient-to-br from-blue-50/80 to-purple-50/80 dark:from-blue-950/40 dark:to-purple-950/40 shadow-xl"
          : "border-blue-300/50 dark:border-blue-700/30 bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-950/10 dark:to-purple-950/10",
        isMobile ? "p-4 sm:p-6" : "p-8 md:p-12", // 移动端使用更小的内边距
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isUploading ? (
        <div className="flex flex-col items-center gap-4">
          {/* 现代化loading动画 */}
          <div className="relative">
            <div className={cn(
              "bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",
              isMobile ? "w-12 h-12" : "w-16 h-16"
            )}>
              <Loader2 className={cn(
                "text-white animate-spin",
                isMobile ? "h-6 w-6" : "h-8 w-8"
              )} />
            </div>
            {/* 外圈脉冲动画 */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl animate-ping opacity-20",
              isMobile ? "w-12 h-12" : "w-16 h-16"
            )}></div>
          </div>

          {/* 加载文字和动画点 */}
          <div className="flex items-center gap-2">
            <p className="text-center gradient-text-safe gradient-text-fallback font-medium">
              正在上传文件，请稍候
            </p>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* 现代化文件图标容器 */}
          <div className={cn(
            "mb-6 flex items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg",
            isMobile ? "w-16 h-16" : "w-20 h-20"
          )}>
            <FileText className={cn(
              "text-white",
              isMobile ? "h-8 w-8" : "h-10 w-10"
            )} />
          </div>

          <h3 className={cn(
            "font-semibold mb-3 text-center gradient-text-safe gradient-text-fallback",
            isMobile ? "text-lg" : "text-xl" // 移动端使用更小的标题
          )}>{title}</h3>
          <p className="text-center text-muted-foreground mb-6 max-w-md">
            {isMobile ? "点击下方按钮选择文件" : "拖放文件到此处，或点击下方按钮选择文件"}
          </p>
          <div className="flex gap-3 flex-wrap justify-center">
            <Button
              onClick={handleButtonClick}
              className={cn(
                "gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-lg transition-all duration-300",
                isMobile ? "px-4 py-2 h-auto text-sm" : "px-6 py-5 h-auto text-base" // 移动端使用更小的按钮
              )}
            >
              <Upload className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
              选择文件
            </Button>
            {onViewHistory && (
              <Button
                onClick={onViewHistory}
                variant="outline"
                className={cn(
                  "gap-2 border-2 border-blue-300 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-all duration-300",
                  isMobile ? "px-4 py-2 h-auto text-sm" : "px-6 py-5 h-auto text-base" // 移动端使用更小的按钮
                )}
              >
                <History className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
                历史记录
              </Button>
            )}
          </div>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept={acceptedFileTypes}
            onChange={handleFileChange}
          />
          <p className={cn(
            "text-muted-foreground mt-4",
            isMobile ? "text-xs" : "text-sm" // 移动端使用更小的文字
          )}>
            支持的文件格式: {acceptedFileTypes.replace(/\./g, '')} · 最大文件大小: {maxSizeMB}MB
          </p>
          {error && (
            <div className="mt-4 p-3 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/30 dark:to-orange-950/30 border-2 border-red-200 dark:border-red-800/30 rounded-xl flex items-center gap-3 shadow-sm">
              <div className="w-6 h-6 bg-gradient-to-br from-red-500 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                <X className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent font-medium">{error}</span>
            </div>
          )}
        </>
      )}
    </div>
  );
}
