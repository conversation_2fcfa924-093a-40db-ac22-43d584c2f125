export const API_URL = process.env.NEXT_PUBLIC_API_URL || '';
export const API_KEY = process.env.NEXT_PUBLIC_API_KEY || '';

// 定义翻译状态类型
export type TranslationStatus = 'idle' | 'uploading' | 'translating' | 'completed' | 'error';

// 定义翻译任务信息
export interface TranslationTask {
  task_id: string;
  filename: string;
  output_filename: string;
}

// 定义进度信息
export interface ProgressInfo {
  status: string;
  progress: number;
  message: string;
}

// 定义文件就绪信息
export interface FileReadyInfo {
  status: string;
  progress: number;
  message: string; // 下载文件的URL路径
}

// 定义摘要结果类型
export interface SummaryResult {
  summary: string;
}

// 自动重试下载功能
export const retryDownloadWithBackoff = async (url: string, maxRetries = 3) => {
  let retryCount = 0;
  let lastError;
  
  while (retryCount < maxRetries) {
    try {
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Cache-Control': 'no-cache'
        },
        cache: 'no-store'
      });
      
      if (response.ok) {
        return response;
      }
      
      throw new Error(`服务器响应错误: ${response.status}`);
    } catch (error) {
      console.warn(`下载重试 ${retryCount + 1}/${maxRetries} 失败:`, error);
      lastError = error;
      retryCount++;
      
      // 指数退避策略
      const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

// 轮询控制函数
export const setupPolling = async (
  taskId: string, 
  pollUrl: string,
  isPollingActive: React.MutableRefObject<boolean>,
  pollTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>,
  setActualProgress: (progress: number) => void,
  setTranslationMessage: (message: string) => void,
  handleCompleted: (data: any) => boolean,
  handleError: (error: Error) => void,
  reminderCount: number,
  setReminderCount: (count: number) => void
) => {
  // 检查任务进度
  const checkProgress = async () => {
    try {
      const progressResponse = await fetch(`${API_URL}${pollUrl}/${taskId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        }
      });

      if (!progressResponse.ok) {
        throw new Error(`查询进度失败: ${progressResponse.status}`);
      }

      const progressJson = await progressResponse.json();
      
      if (progressJson.code !== 0) {
        throw new Error(`查询进度失败: ${progressJson.message || '未知错误'}`);
      }
      
      const progressData = progressJson.data;
      
      // 更新进度
      setActualProgress(progressData.progress);
      setTranslationMessage(progressData.message || '正在处理...');

      // 如果任务完成
      if (progressData.status === 'completed') {
        // 清理轮询定时器
        if (pollTimeoutRef.current) {
          clearTimeout(pollTimeoutRef.current);
          pollTimeoutRef.current = null;
        }
        
        // 停止轮询
        isPollingActive.current = false;
        
        // 处理完成回调
        return handleCompleted(progressData);
      } else if (progressData.status === 'error') {
        // 清理轮询定时器
        if (pollTimeoutRef.current) {
          clearTimeout(pollTimeoutRef.current);
          pollTimeoutRef.current = null;
        }
        
        // 停止轮询
        isPollingActive.current = false;
        
        throw new Error(progressData.message || '处理过程中发生错误');
      }

      return false; // 继续轮询
    } catch (error) {
      handleError(error instanceof Error ? error : new Error('Unknown error'));
      return true; // 停止轮询
    }
  };

  // 开始轮询
  const pollInterval = 2000; // 2秒轮询一次
  const maxAttempts = 150; // 最多轮询150次（5分钟）
  let attempts = 0;

  const poll = async () => {
    try {
      // 如果轮询已停用，则不再继续
      if (!isPollingActive.current) {
        console.log('轮询已停用，不再继续查询进度');
        return;
      }
      
      const isCompleted = await checkProgress();
      attempts++;

      if (isCompleted) {
        return; // 完成，停止轮询
      }

      if (attempts >= maxAttempts) {
        // 不再抛出错误，而是提示用户并继续轮询
        if (attempts === maxAttempts || (attempts > maxAttempts && (attempts - maxAttempts) % 30 === 0)) {
          // 显示提醒
          setReminderCount(reminderCount + 1);
          console.log(`已轮询${attempts}次，提醒用户第${reminderCount + 1}次`);
        }
      }

      // 继续轮询，保存定时器ID便于清理
      pollTimeoutRef.current = setTimeout(() => {
        // 定时器触发时再次检查是否应该继续轮询
        if (isPollingActive.current) {
          poll();
        } else {
          console.log('轮询已在定时器中停用，不再继续');
        }
      }, pollInterval);
    } catch (error) {
      console.error('轮询过程中出错:', error);
      handleError(error instanceof Error ? error : new Error('Unknown error'));
    }
  };

  // 启动轮询
  poll();
}; 