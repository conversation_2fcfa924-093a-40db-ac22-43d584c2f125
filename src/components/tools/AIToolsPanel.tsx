"use client"

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, X, PanelLeft, ChevronDown, Menu } from 'lucide-react';
import { AIToolCard } from './AIToolCard';
import aiTools, { AITool, toolCategories, ToolCategory } from '@/config/aiTools';
import { cn } from '@/lib/utils';
import { renderIcon } from '@/config/iconMap';
import { Button } from "@/components/ui/button";


export default function AIToolsPanel() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [filteredTools, setFilteredTools] = useState<AITool[]>(aiTools);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false); // 侧边栏折叠状态
  const [isMobile, setIsMobile] = useState(false); // 添加移动设备检测状态



  // 检测设备类型并设置相应的状态
  useEffect(() => {
    const checkIsMobile = () => {
      const mobile = window.innerWidth < 768; // 768px是常用的移动设备断点
      setIsMobile(mobile);
      
      // 在移动设备上自动折叠侧边栏
      if (mobile && !isSidebarCollapsed) {
        setIsSidebarCollapsed(true);
      }
    };

    // 初始检查
    checkIsMobile();

    // 监听窗口大小变化
    window.addEventListener('resize', checkIsMobile);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, [isSidebarCollapsed]);

  // Filter tools based on search query and selected category
  useEffect(() => {
    let filtered = aiTools;

    // Filter by category first
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category.split(',').includes(selectedCategory));
    }

    // Then filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tool =>
        tool.title.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
    }

    setFilteredTools(filtered);
    
    // 在移动设备上选择类别后自动折叠侧边栏
    if (isMobile && !isSidebarCollapsed) {
      setIsSidebarCollapsed(true);
    }
  }, [searchQuery, selectedCategory, isMobile, isSidebarCollapsed]);

  // 切换侧边栏折叠状态
  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };



  return (
    <div className="flex h-full overflow-hidden bg-gray-50 dark:bg-gray-900/10" data-tour="ai-tools-panel">
      {/* 左侧分类菜单栏 - 折叠状态 */}
      {isSidebarCollapsed ? (
        <div className="w-16 border-r flex-shrink-0 bg-white dark:bg-gray-800/50 shadow-sm flex flex-col items-center py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="h-10 w-10 rounded-full hover:bg-primary/10 mb-6 bg-white dark:bg-gray-800 border-2 border-primary/30 dark:border-primary/20 shadow-sm"
            title="展开侧边栏"
          >
            <ChevronDown className="h-5 w-5 text-primary dark:text-primary" />
          </Button>
          {toolCategories.map((category) => (
            <Button
              key={category.id}
              variant="ghost"
              size="icon"
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                "h-10 w-10 rounded-full hover:bg-muted/50 mb-3 flex items-center justify-center",
                selectedCategory === category.id 
                  ? `bg-primary hover:bg-primary/90 text-white shadow-md` 
                  : "bg-white dark:bg-gray-800 border-2 border-primary/30 dark:border-primary/20 hover:border-primary/50"
              )}
              title={category.name}
            >
              <div className={cn(
                "flex items-center justify-center",
                selectedCategory === category.id ? "text-white" : "text-primary"
              )}>
                {renderIcon(category.iconUrl, { className: "h-5 w-5" })}
              </div>
            </Button>
          ))}
        </div>
      ) : (
        /* 左侧分类菜单栏 - 展开状态 */
        <div className="w-64 border-r flex-shrink-0 bg-white dark:bg-gray-800/30 shadow-sm">
          <div className="p-4 border-b bg-white/80 dark:bg-gray-800/50 flex justify-between items-center">
            <h2 className="text-xl font-bold">功能分类</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="h-8 w-8 rounded-full hover:bg-muted/50"
              title="折叠侧边栏"
            >
              <PanelLeft className="h-4 w-4" />
            </Button>
          </div>
          <ScrollArea className="h-[calc(100%-65px)]">
            <div className="p-3" data-tour="tools-categories">
            {toolCategories.map((category) => (
              <button
                key={category.id}
                className={cn(
                  "flex items-center w-full p-3 mb-3 rounded-lg text-left transition-colors",
                  selectedCategory === category.id
                    ? `${category.colorClass} font-bold`
                    : "hover:bg-muted/50 font-semibold"
                )}
                onClick={() => setSelectedCategory(category.id)}
              >
                <div className={cn("mr-3 p-2 rounded-md", category.colorClass)}>
                  {renderIcon(category.iconUrl, { className: cn("h-5 w-5", category.iconUrlColorClass) })}
                </div>
                <span className="text-base">{category.name}</span>
                {selectedCategory === category.id && (
                  <span className="ml-auto text-sm font-medium bg-primary/20 dark:bg-primary/30 px-2 py-0.5 rounded-full">
                    ✓
                  </span>
                )}
              </button>
            ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* 右侧内容区域 */}
      <div className={cn(
        "flex-1 flex flex-col overflow-hidden bg-white/50 dark:bg-gray-800/20",
        isSidebarCollapsed && "border-l border-gray-200 dark:border-gray-700"
      )}>
        <div className="p-6 md:p-8 border-b bg-white dark:bg-gray-800/30 shadow-sm">
          <h2 className="text-2xl md:text-3xl font-bold mb-6 text-center">AI工具推荐</h2>

          {/* Search input */}
          <div className="relative mb-4 max-w-3xl mx-auto" data-tour="tools-search">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="输入关键词搜索AI工具..."
              className="pl-12 pr-12 py-6 text-lg rounded-xl"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Tools grid */}
        <ScrollArea className="flex-1 px-4 sm:px-6 py-6">
          {filteredTools.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              没有找到匹配的AI工具
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 max-w-7xl mx-auto" data-tour="tool-card">
              {filteredTools.map((tool) => (
                <AIToolCard
                  key={tool.id}
                  id={tool.id}
                  title={tool.title}
                  description={tool.description}
                  url={tool.url}
                  icon={undefined}
                  iconUrl={tool.iconUrl}
                  colorClass={tool.colorClass}
                  iconColorClass={tool.iconUrlColorClass}
                  category={selectedCategory}
                  compact={isMobile}
                  hasCheckIn={tool.hasCheckIn}
                  isFree={tool.isFree}
                  isLimitedFree={tool.isLimitedFree}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

    </div>
  );
}
