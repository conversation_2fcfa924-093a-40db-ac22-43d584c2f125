"use client"

import React, { useContext } from 'react';
import Image from 'next/image';
import { cn } from "@/lib/utils";
import { renderIcon, IconName } from "@/config/iconMap";
import { ExternalLink } from 'lucide-react';
import { recordToolClick } from '@/lib/chatService';
import { toolCategories } from '@/config/aiTools';

export interface AIToolCardProps {
  id: string;
  title: string;
  description: string;
  url: string;
  icon?: IconName;
  iconUrl?: string;
  colorClass: string;
  iconColorClass: string;
  category?: string;
  compact?: boolean;
  hasCheckIn?: boolean;
  isFree?: boolean;
  isLimitedFree?: boolean;
}

/**
 * A component that renders an AI tool card with an icon, title, description and link
 */
export function AIToolCard({
  id,
  title,
  description,
  url,
  icon,
  iconUrl,
  colorClass,
  iconColorClass,
  category,
  compact = false,
  hasCheckIn = false,
  isFree = false,
  isLimitedFree = false
}: AIToolCardProps) {
  const handleClick = async () => {
    try {
      // 获取用户信息
      let userId = '';
      try {
        const userInfoJson = localStorage.getItem('wechat_work_user');
        if (userInfoJson) {
          const userInfo = JSON.parse(userInfoJson);
          userId = userInfo.userId;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }

      // 确定工具类型
      let toolType = '全部工具'; 
      
      // 从URL或当前路径判断基本位置
      const pathname = window.location.pathname;
      let baseLocation = '';
      if (pathname.includes('/chat')) {
        baseLocation = '聊天侧边栏-';
      } else if (pathname.includes('/tools')) {
        baseLocation = '工具页面-';
      }
      
      // 如果有传入的category，则查找对应的分类名称
      if (category) {
        // 查找当前分类的名称
        const categoryObj = toolCategories.find(cat => cat.id === category);
        if (categoryObj) {
          toolType = baseLocation + categoryObj.name;
        }
      } else {
        // 尝试从URL中获取当前选中的分类
        const urlParams = new URLSearchParams(window.location.search);
        const categoryParam = urlParams.get('category');
        
        if (categoryParam) {
          const categoryObj = toolCategories.find(cat => cat.id === categoryParam);
          if (categoryObj) {
            toolType = baseLocation + categoryObj.name;
          }
        } else {
          // 如果没有category参数，尝试从localStorage中获取最后选择的分类
          try {
            const lastCategory = localStorage.getItem('lastSelectedToolCategory');
            if (lastCategory) {
              const categoryObj = toolCategories.find(cat => cat.id === lastCategory);
              if (categoryObj) {
                toolType = baseLocation + categoryObj.name;
              }
            }
          } catch (e) {
            console.error('获取最后选择的分类失败:', e);
          }
        }
      }
      
      // 记录工具点击
      if (userId) {
        await recordToolClick(userId, title, toolType);
      }
    } catch (error) {
      console.error('记录工具点击失败:', error);
    } finally {
      // 无论记录是否成功，都打开URL
      window.open(url, '_blank');
    }
  };

  return (
    <button
      className={cn(
        "flex flex-col h-full rounded-xl border border-muted/30 bg-white dark:bg-slate-800/90 hover:bg-gray-50 dark:hover:bg-slate-700/90 transition-all text-left shadow-sm hover:shadow-md hover:scale-[1.02] cursor-pointer relative",
        compact ? "p-4" : "p-6"
      )}
      onClick={handleClick}
    >
      {/* 右上角图标组 */}
      <div className="absolute top-2 right-2 flex flex-col gap-1">
        {/* 签到图标 */}
        {hasCheckIn && (
          <div className="bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-sm">
            签
          </div>
        )}
        {/* 免费图标 */}
        {isFree && (
          <div className="bg-green-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-sm">
            免
          </div>
        )}
        {/* 限时免费图标 */}
        {isLimitedFree && (
          <div className="bg-orange-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-sm">
            限
          </div>
        )}
      </div>
      <div className="flex items-start mb-2">
        <div className={cn(
          "mr-4 rounded-lg overflow-hidden flex items-center justify-center shadow-sm border border-gray-200 dark:border-gray-600", 
          "bg-white dark:bg-gray-700", 
          compact ? "h-10 w-10" : "h-14 w-14"
        )}>
          {iconUrl ? (
            <img
              src={iconUrl}
              alt={title}
              className={cn(
                "object-contain p-1 bg-white dark:bg-transparent",
                compact ? "w-8 h-8" : "w-12 h-12"
              )}
            />
          ) : icon ? (
            renderIcon(icon, { 
              className: cn(
                compact ? "h-5 w-5" : "h-7 w-7", 
                iconColorClass
              ) 
            })
          ) : null}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={cn(
              "font-bold truncate",
              compact ? "text-base" : "text-lg"
            )}>{title}</h4>
            <ExternalLink className={cn(
              "text-muted-foreground flex-shrink-0 ml-1",
              compact ? "h-3 w-3" : "h-4 w-4"
            )} />
          </div>
        </div>
      </div>
      <p className={cn(
        "text-muted-foreground",
        compact ? "text-xs line-clamp-2" : "text-sm line-clamp-3",
        "mt-2"
      )}>{description}</p>
    </button>
  );
}
