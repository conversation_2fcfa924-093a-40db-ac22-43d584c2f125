"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, X } from 'lucide-react';
import { AIToolCard } from './AIToolCard';
import aiTools, { AITool } from '@/config/aiTools';

interface AIToolsRecommendationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AIToolsRecommendation({ open, onOpenChange }: AIToolsRecommendationProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTools, setFilteredTools] = useState<AITool[]>(aiTools);

  // Filter tools based on search query
  useEffect(() => {
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      const filtered = aiTools.filter(tool => 
        tool.title.toLowerCase().includes(query) || 
        tool.description.toLowerCase().includes(query) ||
        tool.category.toLowerCase().includes(query)
      );
      
      setFilteredTools(filtered);
    } else {
      setFilteredTools(aiTools);
    }
  }, [searchQuery]);

  // Clear search when dialog closes
  useEffect(() => {
    if (!open) {
      setSearchQuery('');
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl">AI工具推荐</DialogTitle>
        </DialogHeader>
        
        {/* Search input */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索AI工具..."
            className="pl-9 pr-9"
          />
          {searchQuery && (
            <button 
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        
        {/* Tools grid */}
        <ScrollArea className="flex-1 pr-4">
          {filteredTools.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              没有找到匹配的AI工具
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
              {filteredTools.map((tool) => (
                <AIToolCard
                  key={tool.id}
                  id={tool.id}
                  title={tool.title}
                  description={tool.description}
                  url={tool.url}
                  iconUrl={tool.iconUrl}
                  colorClass={tool.colorClass}
                  iconColorClass={tool.iconUrlColorClass}
                  category={tool.category}
                  hasCheckIn={tool.hasCheckIn}
                  isFree={tool.isFree}
                  isLimitedFree={tool.isLimitedFree}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
