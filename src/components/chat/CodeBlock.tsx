"use client"

import React, { useState, useRef, useEffect, useMemo } from "react"
import { cn } from "@/lib/utils"
import { Copy, Check, Maximize2, Minimize2, Search, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { PrismAsyncLight as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import hljs from 'highlight.js';
import javascript from 'highlight.js/lib/languages/javascript';

// 注册JavaScript语言
hljs.registerLanguage('javascript', javascript);

// 自定义代码块组件
const CodeBlock = React.memo(({ language, children }: { language: string; children: string | React.ReactNode }) => {
  const [copied, setCopied] = useState(false)
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [searchMatches, setSearchMatches] = useState<number[]>([])
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // 确保代码内容是字符串
  let codeText = '';

  // 递归提取文本内容的函数
  const extractTextContent = (node: any): string => {
    // 如果是字符串，直接返回
    if (typeof node === 'string') {
      // 确保特殊字符不被转义
      return node;
    }

    // 如果是数字或布尔值，转换为字符串
    if (typeof node === 'number' || typeof node === 'boolean') return String(node);

    // 如果是null或undefined，返回空字符串
    if (node === null || node === undefined) return '';

    // 如果是数组，递归处理每个元素并连接
    if (Array.isArray(node)) {
      return node.map(extractTextContent).join('');
    }

    // 如果是React元素或有props属性的对象
    if (typeof node === 'object') {
      // 如果有props.children属性
      if ('props' in node && node.props) {
        // 如果有value属性，优先使用
        if ('value' in node.props && typeof node.props.value === 'string') {
          return node.props.value;
        }
        return extractTextContent(node.props.children);
      }

      // 如果有value属性（如input元素）
      if ('value' in node && typeof node.value === 'string') {
        return node.value;
      }

      // 如果有data属性
      if ('data' in node && typeof node.data === 'string') {
        return node.data;
      }

      // 如果有children属性
      if ('children' in node) {
        return extractTextContent(node.children);
      }

      // 尝试使用valueOf方法
      if (typeof node.valueOf === 'function') {
        const value = node.valueOf();
        if (typeof value === 'string') {
          return value;
        }
      }

      // 如果有toString方法且不是默认的Object.prototype.toString
      if (typeof node.toString === 'function' && node.toString !== Object.prototype.toString) {
        const str = node.toString();
        if (str && str !== '[object Object]') {
          return str;
        }
      }

      // 如果以上都不适用，尝试使用JSON.stringify
      try {
        const jsonStr = JSON.stringify(node);
        if (jsonStr && jsonStr !== '{}' && jsonStr !== '[]') {
          // 如果不是空对象或空数组，返回格式化的JSON
          return jsonStr;
        }
      } catch (e) {
        // JSON序列化失败，忽略错误
      }
    }

    // 如果所有方法都失败，返回空字符串
    return '';
  };

  // 处理children
  if (children) {
    // 尝试直接获取原始字符串
    if (typeof children === 'string') {
      codeText = children.replace(/\n$/, '');
    } else {
      // 使用递归提取方法
      codeText = extractTextContent(children).replace(/\n$/, '');

      // 如果提取的内容为空或包含[object Object]，尝试其他方法
      if (!codeText.trim() || codeText.includes('[object Object]')) {
        console.warn('Failed to extract clean code content, trying alternative methods');

        // 尝试使用React.Children.toArray并连接
        if (Array.isArray(children)) {
          codeText = children.map(child => {
            if (typeof child === 'string') return child;
            if (child === null || child === undefined) return '';

            // 如果是对象且有type属性，可能是React元素
            if (typeof child === 'object') {
              // 尝试获取元素的文本内容
              if ('props' in child && child.props && 'children' in child.props) {
                if (typeof child.props.children === 'string') {
                  return child.props.children;
                }
              }
            }

            // 如果上述方法失败，尝试使用JSON.stringify
            try {
              return JSON.stringify(child);
            } catch (e) {
              return '';
            }
          }).join('');
        } else if (typeof children === 'object') {
          // 如果是单个对象，尝试直接序列化
          try {
            const jsonStr = JSON.stringify(children);
            if (jsonStr && jsonStr !== '{}') {
              codeText = jsonStr;
            }
          } catch (e) {
            // 如果序列化失败，使用基本的String转换
            codeText = String(children).replace(/\n$/, '');
          }
        } else {
          // 其他情况，使用基本的String转换
          codeText = String(children || '').replace(/\n$/, '');
        }
      }
    }
  }

  // 如果代码内容包含[object Object]，记录调试信息
  if (codeText.includes('[object Object]')) {
    console.warn('Code content still contains [object Object] after all extraction attempts');
    console.log('Original children type:', typeof children);
    if (typeof children === 'object') {
      console.log('Children is object, keys:', Object.keys(children as object));
    }
  }

  // 最后的安全检查，确保代码内容是字符串
  if (typeof codeText !== 'string') {
    console.error('codeText is not a string after processing:', codeText);
    codeText = String(codeText || '');
  }

  // 增强的复制功能，包含多种复制方法的回退机制
  const handleCopy = async () => {
    // 确定要复制的内容
    // 优先使用原始字符串，如果是字符串类型
    const textToCopy = typeof children === 'string' ? children : codeText;

    // 记录调试信息
    console.log('Attempting to copy code, length:', textToCopy.length);

    try {
      // 方法1: 使用现代的Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(textToCopy);
        console.log('Copied using Clipboard API');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        return;
      }
    } catch (err) {
      console.warn('Clipboard API failed:', err);
      // 继续尝试其他方法
    }

    try {
      // 方法2: 使用document.execCommand方法
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;

      // 将元素添加到DOM但不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // 选中并复制文本
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        console.log('Copied using execCommand');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        return;
      }
    } catch (err) {
      console.warn('execCommand copy failed:', err);
    }

    // 方法3: 如果上述方法都失败，提示用户手动复制
    try {
      // 创建一个模态框或提示，显示代码并建议用户手动复制
      console.warn('All copy methods failed, suggested manual copy');
    } catch (err) {
      console.error('Even the fallback alert failed:', err);
    }
  }

  const handleFullScreen = () => {
    setIsFullScreen(!isFullScreen)
  }

  const toggleSearch = () => {
    setShowSearch(!showSearch)
    if (!showSearch) {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 100)
    } else {
      setSearchText("")
      setSearchMatches([])
    }
  }

  // 计算高亮显示的代码
  const highlightedCode = useMemo(() => {
    // 确保代码内容是有效的
    if (!codeText || codeText.includes('[object Object]')) {
      console.warn('Invalid code content detected in highlightedCode calculation');
      // 如果代码内容无效，返回一个提示信息
      return '// 代码内容无法正确显示\n// 请检查代码格式';
    }

    // 如果没有搜索文本，直接返回代码
    if (!searchText.trim()) return codeText;

    // 将代码拆分为行
    const lines = codeText.split('\n');
    const matches: number[] = [];

    // 查找匹配的行号
    lines.forEach((line, index) => {
      if (line.toLowerCase().includes(searchText.toLowerCase())) {
        matches.push(index);
      }
    });

    setSearchMatches(matches);
    if (matches.length > 0 && currentMatchIndex >= matches.length) {
      setCurrentMatchIndex(0);
    }

    return codeText;
  }, [codeText, searchText, currentMatchIndex])

  // 处理搜索导航
  const navigateSearch = (direction: 'next' | 'prev') => {
    if (searchMatches.length === 0) return

    if (direction === 'next') {
      setCurrentMatchIndex((currentMatchIndex + 1) % searchMatches.length)
    } else {
      setCurrentMatchIndex((currentMatchIndex - 1 + searchMatches.length) % searchMatches.length)
    }
  }

  // 当搜索框显示时，聚焦搜索输入框
  useEffect(() => {
    if (showSearch) {
      searchInputRef.current?.focus()
    }
  }, [showSearch])

  // 当按下Escape键时退出全屏
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isFullScreen) {
          setIsFullScreen(false)
        } else if (showSearch) {
          setShowSearch(false)
          setSearchText("")
          setSearchMatches([])
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isFullScreen, showSearch])

  return (
    <div className={cn(
      "relative my-6 rounded-lg overflow-hidden bg-[#1e1e1e] border border-zinc-800 shadow-md",
      isFullScreen ? "fixed inset-0 z-50 rounded-none" : ""
    )}>
      <div className="flex items-center justify-between px-4 py-2 bg-zinc-900 border-b border-zinc-800">
        <div className="flex items-center">
          <span className="text-xs font-mono text-zinc-400">{language}</span>
          {searchMatches.length > 0 && (
            <span className="ml-2 text-xs text-zinc-500">
              {currentMatchIndex + 1}/{searchMatches.length} 匹配项
            </span>
          )}
        </div>
        <div className="flex space-x-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-zinc-400 hover:text-zinc-100 hover:bg-zinc-800"
                  onClick={toggleSearch}
                >
                  <Search className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">搜索</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-zinc-400 hover:text-zinc-100 hover:bg-zinc-800"
                  onClick={handleCopy}
                >
                  {copied ? <Check className="h-3.5 w-3.5" /> : <Copy className="h-3.5 w-3.5" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">{copied ? '已复制' : '复制代码'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-zinc-400 hover:text-zinc-100 hover:bg-zinc-800"
                  onClick={handleFullScreen}
                >
                  {isFullScreen ?
                    <Minimize2 className="h-3.5 w-3.5" /> :
                    <Maximize2 className="h-3.5 w-3.5" />
                  }
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p className="text-xs">{isFullScreen ? '退出全屏' : '全屏查看'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {showSearch && (
        <div className="flex items-center px-4 py-2 bg-zinc-800 border-b border-zinc-700">
          <input
            ref={searchInputRef}
            type="text"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder="搜索代码..."
            className="flex-1 bg-zinc-700 text-zinc-100 text-sm rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          <div className="flex ml-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-zinc-400 hover:text-zinc-100"
              onClick={() => navigateSearch('prev')}
              disabled={searchMatches.length === 0}
            >
              <span className="text-xs">↑</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-zinc-400 hover:text-zinc-100"
              onClick={() => navigateSearch('next')}
              disabled={searchMatches.length === 0}
            >
              <span className="text-xs">↓</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-zinc-400 hover:text-zinc-100"
              onClick={toggleSearch}
            >
              <X className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>
      )}

      <div className={cn(
        "overflow-auto",
        isFullScreen ? "h-[calc(100vh-88px)]" : "max-h-[500px]"
      )}>
        {codeText.includes('[object Object]') ? (
          // 如果代码内容包含[object Object]，显示错误提示
          <div className="p-4 text-red-500 bg-red-100 dark:bg-red-900/20 rounded">
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium">代码内容无法正确显示</p>
              <Button
                variant="outline"
                size="sm"
                className="text-xs flex items-center gap-1 h-7 bg-white/80 dark:bg-zinc-800/80"
                onClick={handleCopy}
              >
                {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                {copied ? '已复制' : '复制原始代码'}
              </Button>
            </div>
            <p className="text-sm mb-2">请检查代码格式或刷新页面重试</p>
            <pre className="mt-2 p-2 bg-black/10 rounded text-xs overflow-auto">
              {typeof children === 'string' ? children : JSON.stringify(children, null, 2)}
            </pre>
          </div>
        ) : (
          // 使用原始的children属性直接传递给SyntaxHighlighter
          // 如果是字符串，直接使用，否则使用处理后的highlightedCode
          <SyntaxHighlighter
            language={language}
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              padding: '1rem',
              fontSize: '0.875rem',
              lineHeight: '1.5',
              background: '#1e1e1e'
            }}
            lineProps={lineNumber => {
              const isHighlighted = searchMatches.includes(lineNumber) && searchText.trim() !== '';
              const isCurrentMatch = searchMatches[currentMatchIndex] === lineNumber && searchText.trim() !== '';
              return {
                style: {
                  display: 'block',
                  backgroundColor: isCurrentMatch ? 'rgba(59, 130, 246, 0.2)' :
                                  isHighlighted ? 'rgba(59, 130, 246, 0.1)' : undefined
                }
              };
            }}
            wrapLines={true}
            showLineNumbers={true}
            // 禁用内部转义，保留原始格式
            PreTag={({ children }: { children: React.ReactNode }) => (
              <pre style={{ margin: 0 }}>{children}</pre>
            )}
            // 添加数据属性以便于调试
            data-language={language}
            data-is-string={typeof children === 'string' ? 'true' : 'false'}
          >
            {typeof children === 'string' ? children : highlightedCode}
          </SyntaxHighlighter>
        )}
      </div>
    </div>
  )
});

export default CodeBlock;
