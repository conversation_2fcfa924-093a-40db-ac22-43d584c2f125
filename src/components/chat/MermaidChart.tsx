"use client"

import React, { useEffect, useRef, useState, memo, useLayoutEffect } from 'react'
import mermaid from 'mermaid'
import { Loader2, Download, ZoomIn, X } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogClose, DialogTitle, DialogPortal, DialogOverlay } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"

// 初始化mermaid配置的函数，在应用启动时调用
export const initializeMermaid = () => {
  try {
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      fontFamily: 'sans-serif',
      fontSize: 12, // 设置更小的默认字体大小
      themeVariables: {
        primaryColor: '#9370DB',
        primaryTextColor: '#2c3e50',
        primaryBorderColor: '#7B68EE',
        lineColor: '#666666',
        secondaryColor: '#006100',
        tertiaryColor: '#fff'
      },
      flowchart: {
        curve: 'basis',
        htmlLabels: true,
        padding: 15, // 减小节点间距
        diagramPadding: 10, // 减小图表整体内边距
        nodeSpacing: 30, // 减小节点间距
        rankSpacing: 30 // 减小层级间距
      },
      er: {
        layoutDirection: 'TB',
        entityPadding: 10 // 减小实体间距
      },
      sequence: {
        actorMargin: 40, // 减小参与者之间的空间
        messageMargin: 25, // 减小消息之间的空间
        mirrorActors: true, // 镜像参与者，节省空间
        boxMargin: 8 // 减小盒子边距
      },
      gantt: {
        leftPadding: 50 // 减小左边距
      }
    });
    console.log('Mermaid initialized successfully');
  } catch (error) {
    console.error('Mermaid initialization error:', error);
  }
};

// 全局缓存已渲染的图表SVG内容
const renderedCharts = new Map<string, string>();

// 生成图表内容的哈希值作为稳定ID
function generateChartId(content: string): string {
  // 使用简单的字符串哈希算法
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return `mermaid-${Math.abs(hash)}`;
}

interface MermaidChartProps {
  chart: string;
  onRenderComplete?: () => void;
}

// 清理mermaid代码，去除前后的```mermaid和```
function cleanMermaidCode(code: string): string {
  // 移除可能的markdown mermaid代码块标记
  return code
    .replace(/^```mermaid\n?/i, '')
    .replace(/```$/g, '')
    .trim();
}

// 使用React.memo包装组件，避免不必要的重新渲染
const MermaidChart: React.FC<MermaidChartProps> = memo(({ chart, onRenderComplete }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const cleanedCode = cleanMermaidCode(chart);
  const chartId = useRef(generateChartId(cleanedCode));
  const isRenderingRef = useRef(false);
  const hasRenderedRef = useRef(false);
  const renderAttempts = useRef(0);

  const [isDialogOpen, setIsDialogOpen] = useState(false); // 添加对话框状态

  // 下载图表为高清PNG文件
  const downloadSvg = () => {
    if (!containerRef.current) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (!svgElement) {
      console.warn('No SVG element found for download');
      return;
    }

    try {
      // 1. 首先处理SVG以确保适当的尺寸
      // 制作SVG的克隆以防止修改原始DOM
      const svgClone = svgElement.cloneNode(true) as SVGElement;

      // 获取SVG的尺寸信息
      const svgRect = svgElement.getBoundingClientRect();
      const viewBox = svgElement.getAttribute('viewBox');

      // 计算宽高比 - 优先使用viewBox，其次使用元素尺寸
      let originalWidth = svgRect.width;
      let originalHeight = svgRect.height;

      // 如果有viewBox，使用它来计算比例
      if (viewBox) {
        const viewBoxValues = viewBox.split(' ').map(Number);
        if (viewBoxValues.length === 4) {
          // viewBox格式: min-x min-y width height
          const viewBoxWidth = viewBoxValues[2];
          const viewBoxHeight = viewBoxValues[3];

          // 如果viewBox尺寸有效，则使用
          if (viewBoxWidth > 0 && viewBoxHeight > 0) {
            originalWidth = viewBoxWidth;
            originalHeight = viewBoxHeight;

            // 设置显式宽高以维持比例
            svgClone.setAttribute('width', String(originalWidth));
            svgClone.setAttribute('height', String(originalHeight));
          }
        }
      }

      // 2. 确保至少有最小尺寸，以防图表过小
      const minSize = 300;
      let scaleFactor = 2; // 默认输出放大2倍以获得更高分辨率

      // 如果图表尺寸太小，增加缩放因子
      if (originalWidth < minSize || originalHeight < minSize) {
        const scale = Math.max(minSize / originalWidth, minSize / originalHeight);
        scaleFactor = Math.max(scaleFactor, scale);
      }

      // 3. 准备SVG数据
      // 设置保持长宽比的属性
      svgClone.setAttribute('preserveAspectRatio', 'xMidYMid meet');

      // 移除任何限制最大高度的样式（可能在显示时添加的）
      svgClone.style.maxHeight = 'none';
      svgClone.style.width = '100%';
      svgClone.style.height = '100%';

      // 计算最终的输出尺寸
      const outputWidth = Math.round(originalWidth * scaleFactor);
      const outputHeight = Math.round(originalHeight * scaleFactor);

      // 确保SVG尺寸合理且不会超过浏览器限制
      const maxDimension = 8000; // 大多数浏览器的限制
      const widthScale = outputWidth > maxDimension ? maxDimension / outputWidth : 1;
      const heightScale = outputHeight > maxDimension ? maxDimension / outputHeight : 1;

      // 取最小缩放比例以确保不超过最大尺寸
      const finalScale = Math.min(widthScale, heightScale);
      const finalWidth = Math.round(outputWidth * finalScale);
      const finalHeight = Math.round(outputHeight * finalScale);

      // 4. 转换SVG为可用的数据URL
      const svgData = new XMLSerializer().serializeToString(svgClone);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // 5. 创建Canvas并绘制SVG
      const canvas = document.createElement('canvas');
      canvas.width = finalWidth;
      canvas.height = finalHeight;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法获取canvas上下文');
      }

      // 设置白色背景
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 加载SVG并绘制到Canvas
      const img = new Image();
      img.onload = () => {
        try {
          // 绘制SVG到整个Canvas
          ctx.drawImage(img, 0, 0, finalWidth, finalHeight);

          // 导出为高质量PNG
          const pngUrl = canvas.toDataURL('image/png', 1.0);

          // 下载文件
          const downloadLink = document.createElement('a');
          downloadLink.href = pngUrl;
          downloadLink.download = `${chartId.current || 'mermaid-chart'}.png`;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);

          // 成功提示
          console.log('PNG下载成功，尺寸:', finalWidth, 'x', finalHeight);
        } catch (error) {
          console.error('Canvas绘制错误:', error);

          // 降级处理：如果Canvas操作失败，直接下载SVG
          downloadSVGAsFallback(svgData);
        } finally {
          // 释放URL对象
          URL.revokeObjectURL(svgUrl);
        }
      };

      // 错误处理
      img.onerror = (error) => {
        console.error('SVG加载失败:', error);
        // 降级处理：直接下载SVG
        downloadSVGAsFallback(svgData);
        URL.revokeObjectURL(svgUrl);
      };

      // 设置源开始加载
      img.src = svgUrl;
    } catch (err) {
      console.error('下载PNG过程发生错误:', err);

      // 尝试从DOM获取SVG数据进行降级处理
      try {
        const svgData = new XMLSerializer().serializeToString(svgElement);
        downloadSVGAsFallback(svgData);
      } catch (fallbackErr) {
        console.error('SVG降级下载也失败:', fallbackErr);
      }
    }
  };

  // SVG降级下载功能
  const downloadSVGAsFallback = (svgData: string) => {
    try {
      // 确保SVG包含XML声明
      if (!svgData.startsWith('<?xml')) {
        svgData = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n' + svgData;
      }

      // 创建Blob和URL
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // 下载SVG文件
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `${chartId.current || 'mermaid-chart'}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // 释放URL
      URL.revokeObjectURL(svgUrl);

      console.log('已降级为SVG下载');
    } catch (err) {
      console.error('SVG降级下载失败:', err);
    }
  };



  // 使用useEffect确保客户端渲染
  useEffect(() => {
    // 检查缓存中是否有已渲染的SVG内容
    const cachedSvg = renderedCharts.get(cleanedCode);

    if (cachedSvg && containerRef.current) {
      // 如果有缓存的SVG，直接插入DOM
      console.log('Using cached SVG for:', chartId.current);
      containerRef.current.innerHTML = cachedSvg;
      setLoading(false);
      hasRenderedRef.current = true;

      // 调用渲染完成回调
      if (onRenderComplete) {
        setTimeout(onRenderComplete, 0);
      }
      return;
    }

    // 如果正在渲染，跳过本次渲染请求
    if (isRenderingRef.current) {
      return;
    }

    // 如果尝试次数过多，停止尝试
    if (renderAttempts.current > 2) {
      setError('图表渲染失败：尝试次数过多');
      setLoading(false);
      return;
    }

    const renderChart = async () => {
      if (!containerRef.current) return;

      // 设置渲染锁，防止重复渲染
      isRenderingRef.current = true;
      renderAttempts.current += 1;

      try {
        // 清除旧内容
        containerRef.current.innerHTML = '';

        // 创建新的容器元素，使用稳定的ID
        const container = document.createElement('div');
        container.id = chartId.current;
        container.className = 'mermaid';
        container.style.width = '100%'; // 恢复为100%，确保Mermaid能正常渲染
        container.textContent = cleanedCode;
        containerRef.current.appendChild(container);

        console.log('Rendering chart:', chartId.current);

        // 确保Mermaid已初始化
        if (typeof mermaid !== 'undefined') {
          try {
            await mermaid.run({
              nodes: [container],
              suppressErrors: false
            });

            const svgElement = container.querySelector('svg');
            if (svgElement) {
              // 设置SVG样式以支持更好的显示
              svgElement.style.width = '100%'; // 先设为100%确保正常显示
              svgElement.style.height = 'auto';
              svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet'); // 居中对齐，保持纵横比
              // 限制SVG高度，防止过高
              svgElement.style.maxHeight = '400px';
              // 设置合适的字体大小
              svgElement.style.fontSize = '13px';

              // 检查SVG的实际宽度，如果超过容器宽度则允许横向滚动
              const svgWidth = svgElement.getBBox ? svgElement.getBBox().width : svgElement.scrollWidth;
              const containerWidth = containerRef.current?.offsetWidth || 0;

              if (svgWidth > containerWidth) {
                svgElement.style.width = 'auto';
                svgElement.style.minWidth = '100%';
              }

              console.log('SVG rendered successfully');

              // 缓存已渲染的SVG内容
              const svgHtml = containerRef.current.innerHTML;
              if (svgHtml && svgHtml.includes('<svg')) {
                renderedCharts.set(cleanedCode, svgHtml);
              }
            } else {
              console.warn('SVG element not found after rendering');
            }

            setLoading(false);
            hasRenderedRef.current = true;

            // 调用渲染完成回调
            if (onRenderComplete) {
              onRenderComplete();
            }
          } catch (parseError) {
            console.error('Mermaid parse error:', parseError);
            setError(`图表语法错误: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
            setLoading(false);
          }
        } else {
          console.error('Mermaid library is undefined');
          setError('Mermaid库未加载');
          setLoading(false);
        }
      } catch (renderError) {
        console.error('Mermaid render error:', renderError);
        setError(`图表渲染错误: ${renderError instanceof Error ? renderError.message : String(renderError)}`);
        setLoading(false);
      } finally {
        // 释放渲染锁
        isRenderingRef.current = false;
      }
    };

    // 确保DOM已更新再渲染图表
    const timer = setTimeout(() => {
      renderChart();
    }, 10);

    return () => {
      clearTimeout(timer);
      isRenderingRef.current = false;
    };
  }, [cleanedCode, onRenderComplete]);

  return (
    <>
      <div
        className="relative my-2 bg-muted/20 p-1 rounded-md group mx-auto"
        style={{ width: '100%' }}
      >
        {/* 图表操作按钮 - 只在图表加载完成且没有错误时显示 */}
        {!loading && !error && (
          <div className="absolute top-0 right-0 flex gap-0.5 z-50 bg-muted/70 rounded-bl-md px-1 py-1 shadow-sm">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡，避免触发图表点击
                      downloadSvg();
                    }}
                    className="p-1 rounded-md hover:bg-background/80 text-muted-foreground hover:text-foreground transition-colors"
                    aria-label="下载图表"
                  >
                    <Download className="h-3.5 w-3.5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>下载图表</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡，避免触发图表点击
                      setIsDialogOpen(true);
                    }}
                    className="p-1 rounded-md hover:bg-background/80 text-muted-foreground hover:text-foreground transition-colors"
                    aria-label="放大查看"
                  >
                    <ZoomIn className="h-3.5 w-3.5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>放大查看</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/70 z-10">
          <div className="flex flex-col items-center gap-1">
            {/* <Loader2 className="h-4 w-4 animate-spin text-primary" /> */}
            {/* <span className="text-xs text-muted-foreground">正在渲染图表...</span> */}
          </div>
        </div>
      )}

      {error && (
        <div className="p-2 text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800 my-1 text-xs">
          <p className="font-medium">图表渲染失败</p>
          <pre className="mt-1 text-xs overflow-x-auto">{error}</pre>
          <details className="mt-1">
            <summary className="text-xs cursor-pointer">查看图表代码</summary>
            <pre className="mt-1 text-xs bg-gray-100 dark:bg-gray-800 p-1 rounded overflow-x-auto">{chart}</pre>
          </details>
        </div>
      )}

      {/* 添加一个外层滚动容器 */}
      <div className="overflow-x-auto overflow-y-hidden hover:bg-muted/30 rounded transition-colors" style={{ scrollbarWidth: 'thin' }}>
        {/* 添加水平滚动提示 */}
        {!loading && !error && (
          <div className="text-xs text-center text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity py-1">
            ← 如果图表过宽，可左右滚动查看完整内容 →
          </div>
        )}

        <div
          ref={containerRef}
          className="mermaid-chart w-full"
          style={{ overflow: 'visible', minWidth: '100%' }}
          data-chart-id={chartId.current}
        />

        {/* 添加放大提示 */}
        {!loading && !error && (
          <div className="text-xs text-center text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity py-1 flex items-center justify-center gap-1">
            <ZoomIn className="h-3 w-3" /> 点击右上角按钮可放大查看
          </div>
        )}
      </div>

      {/* 图表放大对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {/* 自定义对话框内容，不使用默认的DialogContent */}
        <DialogPortal>
          <DialogOverlay className="bg-black/50 backdrop-blur-sm fixed inset-0 z-[200] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
          <DialogPrimitive.Content
            className="fixed left-[50%] top-[50%] z-[200] w-full max-w-[95vw] translate-x-[-50%] translate-y-[-50%] p-0 shadow-none duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 添加对话框标题，但不显示（为了无障碍性） */}
            <DialogTitle className="sr-only">图表放大查看</DialogTitle>

            <div className="relative w-full h-full flex items-center justify-center p-1 sm:p-2">
              <div className="bg-white dark:bg-slate-900 rounded-lg shadow-xl p-3 w-full max-h-[95vh] overflow-auto relative">
                {containerRef.current && (
                  <div className="pt-6"> {/* 添加顶部间距，为标题和按钮留出空间 */}
                    <div
                      className="mermaid-chart-dialog w-full"
                      style={{
                        // 在放大模式下移除高度限制，让图表显示完整尺寸
                        maxHeight: 'none'
                      }}
                      dangerouslySetInnerHTML={{
                        __html: containerRef.current.innerHTML.replace(
                          /style="[^"]*"/g,
                          (match) => {
                            // 在放大模式下，移除SVG的高度限制，让图表显示更大
                            return match
                              .replace(/max-height:\s*\d+px/g, 'max-height: none')
                              .replace(/height:\s*auto/g, 'height: auto')
                              .replace(/width:\s*100%/g, 'width: auto')
                              .replace(/font-size:\s*\d+px/g, 'font-size: 16px'); // 放大模式下使用更大的字体
                          }
                        )
                      }}
                    />
                  </div>
                )}

                {/* 关闭按钮 */}
                <DialogClose className="absolute top-2 right-2 rounded-full p-1.5 bg-white/90 dark:bg-slate-800/90 shadow-md hover:bg-white dark:hover:bg-slate-800 transition-colors border border-gray-200 dark:border-gray-700">
                  <X className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                </DialogClose>
              </div>
            </div>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </div>
    </>

  );
}, (prevProps: MermaidChartProps, nextProps: MermaidChartProps) => {
  // 只有当清理后的chart内容真正变化时才重新渲染
  return cleanMermaidCode(prevProps.chart) === cleanMermaidCode(nextProps.chart);
});

// 添加显示名
MermaidChart.displayName = 'MermaidChart';

export default MermaidChart;