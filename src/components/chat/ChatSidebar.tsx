"use client"

import { useState, useEffect } from "react"
import { ConversationType } from "./ChatInterface"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  MoreVertical,
  Pencil,
  Trash,
  Trash2,
  X,
  PanelLeft,
  PanelRightClose,
  Menu,
  Sun,
  Moon,
  Sparkles,
  FileText,
  MessageSquare,
  Lightbulb,
  BarChart,
  Bot,
  HelpCircle
} from "lucide-react"
import { chatService } from "@/lib/apiService"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import Link from "next/link"
import { recordToolClick } from '@/lib/chatService'

interface ChatSidebarProps {
  conversations: ConversationType[]
  activeConversationId: string
  onNewConversation: () => void
  onSwitchConversation: (id: string) => void
  onDeleteConversation: (id: string) => void
  onRenameConversation: (id: string, newTitle: string) => void
  onClearMessages: () => void
  onClose?: () => void
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  showAITools?: boolean
  setShowAITools?: (show: boolean) => void
  showDocProcessing?: boolean
  setShowDocProcessing?: (show: boolean) => void
  showVisualization?: boolean
  setShowVisualization?: (show: boolean) => void

}

export default function ChatSidebar({
  conversations,
  activeConversationId,
  onNewConversation,
  onSwitchConversation,
  onDeleteConversation,
  onRenameConversation,
  onClearMessages,
  onClose,
  isCollapsed = false,
  onToggleCollapse,
  showAITools = false,
  setShowAITools,
  showDocProcessing = false,
  setShowDocProcessing,
  showVisualization = false,
  setShowVisualization,

}: ChatSidebarProps) {
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
  const [conversationToRename, setConversationToRename] = useState<string>("")
  const [newTitle, setNewTitle] = useState("")
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // 确保只在客户端渲染主题切换按钮，避免水合错误
  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const handleOpenRenameDialog = (id: string, currentTitle: string) => {
    setConversationToRename(id)
    setNewTitle(currentTitle)
    setIsRenameDialogOpen(true)
  }

  const handleRename = () => {
    if (newTitle.trim()) {
      onRenameConversation(conversationToRename, newTitle.trim())
      setIsRenameDialogOpen(false)
    }
  }

  // 如果侧边栏折叠，则渲染折叠视图
  if (isCollapsed) {
    return (
      <div className="flex flex-col h-full items-center py-4 px-1">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleCollapse}
          className="h-8 w-8 rounded-full hover:bg-muted/50"
          title="展开侧边栏"
        >
          <Menu className="h-4 w-4" />
        </Button>
        {mounted && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="h-8 w-8 rounded-full hover:bg-muted/50 mt-4"
            title={theme === 'dark' ? "切换到明亮模式" : "切换到暗黑模式"}
          >
            {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (showAITools && setShowAITools) setShowAITools(false);
            if (showDocProcessing && setShowDocProcessing) setShowDocProcessing(false);
            if (showVisualization && setShowVisualization) setShowVisualization(false);
            
            // 在移动端点击按钮后关闭侧边栏
            if (window.innerWidth < 768 && onClose) {
              onClose();
            }
          }}
          className={cn("h-8 w-8 rounded-full mt-4 border transition-all duration-200",
            !showAITools && !showDocProcessing && !showVisualization
              ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
              : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40")}
          title="AI聊天对话"
        >
          <MessageSquare className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (setShowAITools) setShowAITools(true);
            if (setShowDocProcessing) setShowDocProcessing(false);
            if (setShowVisualization) setShowVisualization(false);
            
            // 在移动端点击按钮后关闭侧边栏
            if (window.innerWidth < 768 && onClose) {
              onClose();
            }
          }}
          className={cn("h-8 w-8 rounded-full mt-4 border transition-all duration-200",
            showAITools
              ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
              : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40")}
          title="AI工具推荐"
        >
          <Sparkles className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            if (setShowDocProcessing) setShowDocProcessing(true);
            if (setShowAITools) setShowAITools(false);
            if (setShowVisualization) setShowVisualization(false);
            
            // 在移动端点击按钮后关闭侧边栏
            if (window.innerWidth < 768 && onClose) {
              onClose();
            }
          }}
          className={cn("h-8 w-8 rounded-full mt-4 border transition-all duration-200",
            showDocProcessing
              ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
              : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40")}
          title="文档处理"
        >
          <FileText className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={async () => {
            try {
              // 获取用户信息
              let userId = '';
              try {
                const userInfoJson = localStorage.getItem('wechat_work_user');
                if (userInfoJson) {
                  const userInfo = JSON.parse(userInfoJson);
                  userId = userInfo.userId;
                }
              } catch (error) {
                console.error('获取用户信息失败:', error);
              }

              // 记录工具点击
              if (userId) {
                await recordToolClick(userId, '文档可视化', '聊天界面-文档可视化');
              }
            } catch (error) {
              console.error('记录工具点击失败:', error);
            } finally {
              // 设置显示文档可视化面板
              if (setShowVisualization) setShowVisualization(true);
              if (setShowAITools) setShowAITools(false);
              if (setShowDocProcessing) setShowDocProcessing(false);

              // 在移动端点击按钮后关闭侧边栏
              if (window.innerWidth < 768 && onClose) {
                onClose();
              }

              // 在桌面端点击按钮后折叠侧边栏
              if (window.innerWidth >= 768 && onToggleCollapse && !isCollapsed) {
                onToggleCollapse();
              }
            }
          }}
          className={cn("h-8 w-8 rounded-full mt-4 border transition-all duration-200",
            showVisualization
              ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
              : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40")}
          title="文档可视化"
        >
          <BarChart className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full p-4" data-tour="sidebar">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold flex items-center gap-2">
          {/* <Bot className="h-5 w-5 text-blue-500" />
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            MengChat
          </span> */}
        </h2>
        <div className="flex gap-2 items-center">
          {mounted && (
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="h-8 w-8 rounded-full hover:bg-muted/50"
              title={theme === 'dark' ? "切换到明亮模式" : "切换到暗黑模式"}
            >
              {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>
          )}
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleCollapse}
              title="折叠侧边栏"
              className="h-8 w-8 rounded-full hover:bg-muted/50 hidden md:flex"
            >
              <PanelLeft className="h-4 w-4" />
            </Button>
          )}
          {onClose && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="md:hidden"
              title="关闭侧边栏"
            >
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>

      {/* AI聊天对话按钮 */}
      <Button
        variant="outline"
        className={cn(
          "w-[80%] mx-auto mb-4 flex items-center justify-center gap-2 border transition-all duration-200",
          !showAITools && !showDocProcessing && !showVisualization
            ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
            : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40"
        )}
        onClick={() => {
          if (showAITools && setShowAITools) setShowAITools(false);
          if (showDocProcessing && setShowDocProcessing) setShowDocProcessing(false);
          if (showVisualization && setShowVisualization) setShowVisualization(false);
          
          // 在移动端点击按钮后关闭侧边栏
          if (window.innerWidth < 768 && onClose) {
            onClose();
          }
        }}
        data-tour="ai-chats"
      >
        <MessageSquare className="h-4 w-4" />
        <span>AI聊天对话</span>
      </Button>

      {/* AI工具推荐按钮 */}
      <Button
        variant="outline"
        className={cn(
          "w-[80%] mx-auto mb-4 flex items-center justify-center gap-2 border transition-all duration-200",
          showAITools
            ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
            : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40"
        )}
        onClick={() => {
          if (setShowAITools) setShowAITools(true);
          if (setShowDocProcessing) setShowDocProcessing(false);
          if (setShowVisualization) setShowVisualization(false);

          // 在移动端点击按钮后关闭侧边栏
          if (window.innerWidth < 768 && onClose) {
            onClose();
          }
        }}
        data-tour="ai-tools"
      >
        <Sparkles className="h-4 w-4" />
        <span>AI工具推荐</span>
      </Button>

      {/* 文档处理按钮 */}
      <Button
        variant="outline"
        className={cn(
          "w-[80%] mx-auto mb-4 flex items-center justify-center gap-2 border transition-all duration-200",
          showDocProcessing
            ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
            : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40"
        )}
        onClick={() => {
          if (setShowDocProcessing) setShowDocProcessing(true);
          if (setShowAITools) setShowAITools(false);
          if (setShowVisualization) setShowVisualization(false);

          // 在移动端点击按钮后关闭侧边栏
          if (window.innerWidth < 768 && onClose) {
            onClose();
          }
        }}
        data-tour="doc-processing"
      >
        <FileText className="h-4 w-4" />
        <span>文档处理</span>
      </Button>

      {/* 文档可视化按钮 */}
      <Button
        variant="outline"
        className={cn(
          "w-[80%] mx-auto mb-4 flex items-center justify-center gap-2 border transition-all duration-200",
          showVisualization
            ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border-blue-400 hover:from-blue-600 hover:to-purple-600 shadow-md"
            : "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border-blue-200 dark:border-blue-800 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40"
        )}
        onClick={async () => {
          try {
            // 获取用户信息
            let userId = '';
            try {
              const userInfoJson = localStorage.getItem('wechat_work_user');
              if (userInfoJson) {
                const userInfo = JSON.parse(userInfoJson);
                userId = userInfo.userId;
              }
            } catch (error) {
              console.error('获取用户信息失败:', error);
            }

            // 记录工具点击
            if (userId) {
              await recordToolClick(userId, '文档可视化', '聊天界面-文档可视化');
            }
          } catch (error) {
            console.error('记录工具点击失败:', error);
          } finally {
            // 设置显示文档可视化面板
            if (setShowVisualization) setShowVisualization(true);
            if (setShowAITools) setShowAITools(false);
            if (setShowDocProcessing) setShowDocProcessing(false);

            // 在移动端点击按钮后关闭侧边栏
            if (window.innerWidth < 768 && onClose) {
              onClose();
            }

            // 在桌面端点击按钮后折叠侧边栏
            if (window.innerWidth >= 768 && onToggleCollapse && !isCollapsed) {
              onToggleCollapse();
            }
          }
        }}
        data-tour="visualization"
      >
        <BarChart className="h-4 w-4" />
        <span>文档可视化</span>
      </Button>

      <div className="border-t border-border mb-4"></div>

      <h2 className="text-lg font-semibold mb-4 px-1">会话列表</h2>

      {/* 移除了返回聊天按钮，改用AI聊天对话按钮代替 */}

      <ScrollArea className="flex-1">
        <div className="space-y-2">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`
                flex items-center justify-between p-2 rounded-md cursor-pointer transition-all duration-200
                ${activeConversationId === conversation.id
                  ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white border border-blue-400 shadow-md"
                  : "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-950/30 dark:hover:to-purple-950/30"}
              `}
              onClick={() => onSwitchConversation(conversation.id)}
            >
              <div className="truncate flex-1">
                {conversation.title}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={`h-8 w-8 ${activeConversationId === conversation.id ? "text-primary-foreground" : ""}`}
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation()
                      handleOpenRenameDialog(conversation.id, conversation.title)
                    }}
                  >
                    <Pencil className="h-4 w-4 mr-2" />
                    重命名
                  </DropdownMenuItem>
                  {activeConversationId === conversation.id && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()
                          onClearMessages()
                        }}
                      >
                        <X className="h-4 w-4 mr-2" />
                        清空消息
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDeleteConversation(conversation.id)
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    删除会话
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
      </ScrollArea>

      {conversations.length > 0 && activeConversationId && (
        <div className="border-t pt-4 pb-3">
          <Button
            variant="outline"
            className="w-full mb-2"
            onClick={() => window.open("https://www.groupama-sdig.com/cnt/sJhY9D", "_blank")}
          >
            <Lightbulb className="h-4 w-4 mr-2 text-amber-500" />
            使用意见反馈
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={onClearMessages}
          >
            <Trash className="h-4 w-4 mr-2" />
            清空当前会话
          </Button>
        </div>
      )}



      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重命名会话</DialogTitle>
          </DialogHeader>
          <Input
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            placeholder="输入新的会话名称"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleRename()
              }
            }}
          />
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
            >
              取消
            </Button>
            <Button onClick={handleRename}>
              确认
            </Button>
          </div>
        </DialogContent>
      </Dialog>


    </div>
  )
}
