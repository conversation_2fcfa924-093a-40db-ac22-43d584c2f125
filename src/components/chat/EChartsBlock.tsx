"use client"

import React, { useRef, useEffect, useState } from "react"
import * as echarts from 'echarts';
import { Download, ZoomIn, X } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogClose, DialogTitle, DialogPortal, DialogOverlay } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"

// 防抖函数
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function(this: any, ...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

// ECharts 图表组件
const EChartsBlock = React.memo(({ chartData }: { chartData: string }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const chartId = useRef(`echarts-${Math.random().toString(36).substring(2, 9)}`);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  // 保存原始图表配置
  const [chartConfig, setChartConfig] = useState<any>(null);
  // 保存图表图像用于对话框显示
  const [chartImage, setChartImage] = useState<string | null>(null);

  // 生成图表图像
  const generateChartImage = (forDownload = false) => {
    if (!chartInstanceRef.current) return null;

    try {
      // 使用ECharts内置的导出功能
      const url = chartInstanceRef.current.getDataURL({
        type: 'png',
        backgroundColor: '#fff',
        pixelRatio: 2 // 高清度
      });

      if (forDownload) {
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = `${chartId.current}.png`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      } else {
        return url;
      }
    } catch (err) {
      console.error('Error generating chart image:', err);
      return null;
    }
  };

  // 下载图表为PNG
  const downloadPng = () => {
    generateChartImage(true);
  };

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current || !chartConfig) return;

    try {
      // 如果已有实例，先销毁
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
      }

      // 创建图表实例
      chartInstanceRef.current = echarts.init(chartRef.current, 'auto');

      // 设置图表配置
      chartInstanceRef.current.setOption(chartConfig);

      // 重置错误状态
      setError(null);
    } catch (err) {
      console.error("ECharts渲染错误:", err);
      setError(`图表渲染失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }

    return () => {
      // 组件卸载时销毁图表实例
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, [chartConfig]);



  // 使用 IntersectionObserver 检测可见性
  useEffect(() => {
    if (!chartRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          setIsVisible(entry.isIntersecting);
        });
      },
      { threshold: 0.1 } // 当10%可见时触发
    );

    observer.observe(chartRef.current);

    return () => {
      if (chartRef.current) {
        observer.unobserve(chartRef.current);
      }
    };
  }, []);

  // 只在可见时处理窗口大小变化
  useEffect(() => {
    if (!isVisible || !chartInstanceRef.current) return;

    const handleResize = debounce(() => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    }, 100);

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isVisible]);

  // 当组件变为可见时，重新渲染图表
  useEffect(() => {
    if (isVisible && chartInstanceRef.current) {
      chartInstanceRef.current.resize();
    }
  }, [isVisible]);

  // 当对话框打开时生成图表图像
  useEffect(() => {
    if (isDialogOpen && chartInstanceRef.current) {
      // 生成图表图像
      const imageUrl = generateChartImage();
      setChartImage(imageUrl || null);
    }
  }, [isDialogOpen]);

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="my-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400">
        {error}
      </div>
    );
  }

  // 解析图表数据并保存配置
  useEffect(() => {
    try {
      // 解析图表数据
      let config: any;

      if (typeof chartData === 'string') {
        // 尝试不同的解析方法
        try {
          // 方法1: 尝试直接解析JSON
          config = JSON.parse(chartData);
          setChartConfig(config);
        } catch (e) {
          try {
            // 方法2: 尝试提取option对象
            // 处理类似 "option = {...}" 的格式
            const optionMatch = chartData.match(/option\s*=\s*(\{[\s\S]*\});?/);
            if (optionMatch && optionMatch[1]) {
              // 尝试将提取的JSON字符串转换为对象
              try {
                config = eval('(' + optionMatch[1] + ')');
                setChartConfig(config);
              } catch (evalError) {
                throw new Error(`无法解析选项对象`);
              }
            } else {
              // 方法3: 处理直接的JavaScript对象字面量（包含注释）
              try {
                // 清理JavaScript注释和格式化代码
                let cleanedCode = chartData.trim();

                // 移除单行注释 //
                cleanedCode = cleanedCode.replace(/\/\/.*$/gm, '');

                // 移除多行注释 /* */
                cleanedCode = cleanedCode.replace(/\/\*[\s\S]*?\*\//g, '');

                // 确保代码以 { 开始和 } 结束
                if (!cleanedCode.startsWith('{')) {
                  cleanedCode = '{' + cleanedCode;
                }
                if (!cleanedCode.endsWith('}')) {
                  cleanedCode = cleanedCode + '}';
                }

                // 尝试使用eval解析JavaScript对象字面量
                config = eval('(' + cleanedCode + ')');
                setChartConfig(config);
              } catch (evalError) {
                // 方法4: 尝试直接运行代码并获取option变量
                try {
                  // 警告: 使用eval有安全风险，仅在受控环境中使用
                  // 创建一个安全的沙盒环境
                  let sandboxOption: any;
                  const sandbox = {
                    option: {},
                    echarts: echarts,
                    document: undefined,
                    window: undefined,
                    set: function(obj: any) { sandboxOption = obj; }
                  };

                  // 添加一个set函数调用到代码结尾
                  const codeWithSet = chartData + '\n;sandbox.set(option);';

                  // 在沙盒中执行代码
                  new Function('sandbox', codeWithSet)(sandbox);

                  if (sandboxOption && typeof sandboxOption === 'object') {
                    config = sandboxOption;
                    setChartConfig(config);
                  } else if (sandbox.option && typeof sandbox.option === 'object') {
                    config = sandbox.option;
                    setChartConfig(config);
                  } else {
                    throw new Error('无法提取图表配置对象');
                  }
                } catch (sandboxError) {
                  throw new Error(`沙盒执行错误: ${sandboxError instanceof Error ? sandboxError.message : '未知错误'}`);
                }
              }
            }
          } catch (extractError) {
            console.error("ECharts数据提取错误:", extractError);
            setError(`图表数据格式错误，无法解析配置: ${extractError instanceof Error ? extractError.message : '未知错误'}`);
            return;
          }
        }
      } else {
        // 如果不是字符串，直接使用
        config = chartData;
        setChartConfig(config);
      }
    } catch (err) {
      console.error("解析图表数据错误:", err);
      setError(`图表数据解析失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  }, [chartData]);





  return (
    <>
      <div
        className="relative my-6 rounded-lg overflow-hidden bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-800 shadow-sm"
      >
        {/* 下载和放大按钮 */}
        {!error && (
          <div className="absolute flex gap-1 z-50 bg-muted/80 rounded-md px-2 py-1.5 shadow-md border border-muted top-0 right-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      downloadPng();
                    }}
                    className="p-1.5 rounded-md hover:bg-background/90 text-muted-foreground hover:text-foreground transition-colors"
                    aria-label="下载图表"
                  >
                    <Download className="h-3.5 w-3.5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>下载为PNG</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      setIsDialogOpen(true);
                    }}
                    className="p-1.5 rounded-md hover:bg-background/90 text-muted-foreground hover:text-foreground transition-colors"
                    aria-label="放大查看"
                  >
                    <ZoomIn className="h-3.5 w-3.5" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>放大查看</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}

        <div
          ref={chartRef}
          className="w-full"
          style={{
            height: '320px',
            minHeight: '320px'
          }}
        />

        {/* 添加放大提示 */}
        {!error && (
          <div className="text-xs text-center text-muted-foreground opacity-0 hover:opacity-100 transition-opacity py-1 flex items-center justify-center gap-1 mt-2">
            <ZoomIn className="h-3.5 w-3.5" /> 点击右上角按钮可放大查看
          </div>
        )}
      </div>

      {/* 图表放大对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {/* 自定义对话框内容，不使用默认的DialogContent */}
        <DialogPortal>
          <DialogOverlay className="bg-black/50 backdrop-blur-sm fixed inset-0 z-[200] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
          <DialogPrimitive.Content
            className="fixed left-[50%] top-[50%] z-[200] w-full max-w-[95vw] translate-x-[-50%] translate-y-[-50%] p-0 shadow-none duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 添加对话框标题，但不显示（为了无障碍性） */}
            <DialogTitle className="sr-only">图表放大查看</DialogTitle>

            <div className="relative w-full h-full flex items-center justify-center p-1 sm:p-2">
              <div className="bg-white dark:bg-slate-900 rounded-lg shadow-xl p-3 w-full max-h-[95vh] overflow-auto relative">
                <div className="pt-6"> {/* 添加顶部间距，为标题和按钮留出空间 */}
                  {chartImage ? (
                    <img
                      src={chartImage}
                      alt="图表放大图"
                      className="w-full h-auto max-w-full object-contain"
                      style={{ maxHeight: '80vh' }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-64">
                      <p className="text-muted-foreground">加载中...</p>
                    </div>
                  )}
                </div>

                {/* 关闭按钮 */}
                <DialogClose className="absolute top-2 right-2 rounded-full p-1.5 bg-white/90 dark:bg-slate-800/90 shadow-md hover:bg-white dark:hover:bg-slate-800 transition-colors border border-gray-200 dark:border-gray-700">
                  <X className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                </DialogClose>
              </div>
            </div>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </>
  );
});

EChartsBlock.displayName = 'EChartsBlock';

export default EChartsBlock;