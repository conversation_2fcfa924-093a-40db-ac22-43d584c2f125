"use client"

import React, { useMemo } from "react"
import { BarChart, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> as RechartsTooltip, ResponsiveContainer } from "recharts"
import { cn } from "@/lib/utils"

// 图表组件
const ChartBlock = React.memo(({ chartData }: { chartData: string }) => {
  // 使用useMemo缓存解析的数据，避免重新渲染时重复解析
  const parsedData = useMemo(() => {
    try {
      return JSON.parse(chartData);
    } catch (e) {
      console.error("Chart data parse error:", e);
      return null;
    }
  }, [chartData]);

  // 如果数据解析失败，显示错误信息
  if (!parsedData) {
    return (
      <div className="my-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400">
        图表数据格式错误，无法渲染
      </div>
    );
  }

  // 如果是柱状图数据
  if (parsedData.type === 'bar') {
    return (
      <div className="my-6 rounded-lg overflow-hidden bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-800 shadow-sm">
        <div className="p-2 bg-slate-50 dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
          <div className="text-sm font-medium">{parsedData.title || '数据图表'}</div>
          {parsedData.description && (
            <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">{parsedData.description}</div>
          )}
        </div>
        <div className="p-4">
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={parsedData.data}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 60,
                }}
              >
                <XAxis 
                  dataKey={parsedData.xAxis || "name"} 
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  tick={{ fontSize: 12 }}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <RechartsTooltip 
                  formatter={(value: number, name: string) => [
                    `${value}${parsedData.unit || ''}`, 
                    parsedData.seriesNames?.[name] || name
                  ]}
                />
                {parsedData.series ? (
                  parsedData.series.map((serie: string, index: number) => (
                    <Bar 
                      key={serie} 
                      dataKey={serie} 
                      fill={parsedData.colors?.[index] || `hsl(${index * 40 % 360}, 70%, 60%)`} 
                      name={parsedData.seriesNames?.[serie] || serie}
                    />
                  ))
                ) : (
                  <Bar 
                    dataKey={parsedData.valueKey || "value"} 
                    fill={parsedData.color || "#8884d8"} 
                    name={parsedData.valueName || "数值"}
                  />
                )}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  }

  // 默认返回简单数据表格
  return (
    <div className="my-6 rounded-lg overflow-hidden bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-800 shadow-sm">
      <div className="p-2 bg-slate-50 dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
        <div className="text-sm font-medium">{parsedData.title || '数据表格'}</div>
      </div>
      <div className="p-4 overflow-x-auto">
        <table className={cn(
          "w-full border-collapse",
          "text-sm [&_th]:text-left [&_th]:p-2 [&_th]:bg-slate-100 dark:[&_th]:bg-slate-800 [&_th]:font-medium",
          "[&_td]:p-2 [&_td]:border-t [&_td]:border-slate-200 dark:[&_td]:border-slate-700"
        )}>
          <thead>
            <tr>
              {parsedData.columns?.map((col: string, i: number) => (
                <th key={i}>{col}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {parsedData.data?.map((row: any, i: number) => (
              <tr key={i}>
                {parsedData.columns?.map((col: string, j: number) => (
                  <td key={j}>{row[col]}</td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
});

export default ChartBlock;
