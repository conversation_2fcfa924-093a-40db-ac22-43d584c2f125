"use client"

import React from "react"
import { cn } from "@/lib/utils"

// 定义头像类型
type AvatarType = "user" | "bot"

interface ChatAvatarProps {
  type: AvatarType
  className?: string
}

export default function ChatAvatar({ type, className }: ChatAvatarProps) {
  return (
    <div
      className={cn(
        "h-8 w-8 rounded-full flex-shrink-0 flex items-center justify-center overflow-hidden",
        type === "user" ? "bg-gradient-to-br from-indigo-500 to-purple-600" : "bg-gradient-to-br from-emerald-500 to-teal-600",
        className
      )}
    >
      {type === "user" ? (
        // 用户头像 - 现代化的人物图标
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
          className="h-5 w-5 text-white"
        >
          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      ) : (
        // 机器人头像 - 更现代化的AI图标
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
          className="h-5 w-5 text-white"
        >
          <rect x="3" y="11" width="18" height="10" rx="2"></rect>
          <circle cx="12" cy="5" r="2"></circle>
          <path d="M12 7v4"></path>
          <line x1="8" y1="16" x2="8" y2="16"></line>
          <line x1="16" y1="16" x2="16" y2="16"></line>
        </svg>
      )}
    </div>
  )
}
