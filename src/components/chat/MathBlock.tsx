"use client"

import React, { useEffect, useState } from 'react'
import { InlineMath, BlockMath } from 'react-katex'
import 'katex/dist/katex.min.css'
import ErrorBoundary from '@/components/ErrorBoundary'

interface MathBlockProps {
  math: string
  inline?: boolean
  className?: string
}

const MathBlock: React.FC<MathBlockProps> = ({ math, inline = false, className = "" }) => {
  const [error, setError] = useState<string | null>(null)

  // 清理数学公式内容
  const cleanMath = (mathString: string): string => {
    return mathString
      .trim()
      .replace(/^\$+/, '') // 移除开头的$符号
      .replace(/\$+$/, '') // 移除结尾的$符号
      .trim()
  }

  const cleanedMath = cleanMath(math)

  // 重置错误状态当数学内容改变时
  useEffect(() => {
    setError(null)
  }, [cleanedMath])

  const handleError = (err: Error) => {
    console.error('KaTeX渲染错误:', err)
    setError(err.message || '数学公式渲染失败')
  }

  if (error) {
    return (
      <span className={`inline-block px-2 py-1 bg-red-50 border-l-4 border-red-400 text-red-700 text-sm rounded ${className}`}>
        数学公式错误: {cleanedMath}
      </span>
    )
  }

  try {
    if (inline) {
      return (
        <ErrorBoundary>
          <InlineMath 
            math={cleanedMath}
            errorColor="#dc2626"
            onError={handleError}
            className={className}
          />
        </ErrorBoundary>
      )
    } else {
      return (
        <ErrorBoundary>
          <div className={`my-4 overflow-x-auto ${className}`}>
            <BlockMath 
              math={cleanedMath}
              errorColor="#dc2626"
              onError={handleError}
            />
          </div>
        </ErrorBoundary>
      )
    }
  } catch (err) {
    const error = err as Error
    return (
      <span className={`inline-block px-2 py-1 bg-red-50 border-l-4 border-red-400 text-red-700 text-sm rounded ${className}`}>
        数学公式错误: {cleanedMath}
      </span>
    )
  }
}

export default MathBlock