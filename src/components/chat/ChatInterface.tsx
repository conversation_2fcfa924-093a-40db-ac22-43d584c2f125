"use client"

import { useState, useRef, useEffect } from "react"
import { nanoid } from "nanoid"
import { toast } from "sonner"
import ChatMessages from "./ChatMessages"
import ChatInput from "./ChatInput"
import ChatSidebar from "./ChatSidebar"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import { chatService } from "@/lib/apiService" // 导入API服务
import { cn } from "@/lib/utils"
import { saveConversationLog } from "@/lib/chatService" // 从新的chatService导入
import conversationService from "@/lib/conversationService" // 导入会话服务
import ErrorBoundary from "@/components/ErrorBoundary" // 导入错误边界组件
import AIToolsPanel from "@/components/tools/AIToolsPanel" // 导入AI工具推荐面板
import DocProcessingPanel from "@/components/docs/DocProcessingPanel" // 导入文档处理面板
import ChatChartPanel from "@/components/docs/ChatChartPanel" // 导入ChatChart面板


// 定义附件类型
export type AttachmentType = {
  id: string
  name: string
  type: string
  url: string
  imageId?: string // 图片ID，用于发送给API
  size: number
}

// 定义消息类型
export type MessageType = {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: number
  attachments?: AttachmentType[]
  thinking?: string // 思考过程
  isStreaming?: boolean
  isThinking?: boolean
  marker?: string // 用于标识思考过程的标记格式，例如 "<think>"
  processTime?: number // 处理时间，单位为毫秒
}

// 定义会话类型
export type ConversationType = {
  id: string
  title: string
  messages: MessageType[]
  createdAt: number
  updatedAt: number
  sessionId: string // 每个会话窗口的唯一标识UUID
}

// 清理和提取思考过程
function extractThinkingContent(content: string): { mainContent: string, thinking: string | null } {
  // 思考过程标记格式
  const thinkingMarkers = [
    { start: "<思考>", end: "</思考>" },
    { start: "<thinking>", end: "</thinking>" },
    { start: "<Thinking>", end: "</Thinking>" },
    { start: "```thinking", end: "```" },
  ]

  for (const marker of thinkingMarkers) {
    const startIndex = content.indexOf(marker.start)
    const endIndex = content.lastIndexOf(marker.end)

    if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
      const thinkingContent = content.substring(
        startIndex + marker.start.length,
        endIndex
      ).trim()

      const mainContent = (
        content.substring(0, startIndex) +
        content.substring(endIndex + marker.end.length)
      ).trim()

      return { mainContent, thinking: thinkingContent }
    }
  }

  return { mainContent: content, thinking: null }
}

// 创建新会话，与组件状态分离
function createNewConversation(): ConversationType {
  return {
    id: nanoid(),
    title: `新会话`,
    messages: [],
    createdAt: 0, // 初始值为0，稍后更新
    updatedAt: 0,  // 初始值为0，稍后更新
    sessionId: nanoid()
  }
}

export default function ChatInterface() {
  // 使用状态来存储是否已经初始化
  const [isInitialized, setIsInitialized] = useState(false)

  // 添加侧边栏开关状态
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  // 添加桌面端侧边栏折叠状态
  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false)

  // 状态管理
  const [conversations, setConversations] = useState<ConversationType[]>([createNewConversation()])
  const [activeConversationId, setActiveConversationId] = useState<string>("")
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const scrollToBottomRef = useRef<(() => void) | null>(null)

  // 添加一个状态来跟踪是否有流式响应正在进行
  const [isStreamingResponse, setIsStreamingResponse] = useState(false)
  
  // 添加中断控制器
  const abortControllerRef = useRef<AbortController | null>(null)

  // 添加模型类型选择状态
  const [selectedModel, setSelectedModel] = useState('speed')

  // 添加一个状态来控制是否显示AI工具推荐面板
  const [showAITools, setShowAITools] = useState(false)

  // 添加一个状态来控制是否显示文档处理面板
  const [showDocProcessing, setShowDocProcessing] = useState(false)

  // 添加一个状态来控制是否显示文档可视化面板
  const [showVisualization, setShowVisualization] = useState(false)

  // 添加后端会话管理相关状态
  const [isLoadingConversations, setIsLoadingConversations] = useState(false)

  // 客户端初始化
  useEffect(() => {
    const initializeConversations = async () => {
      if (isInitialized) return;

      setIsLoadingConversations(true);

      try {
        // 恢复侧边栏状态
        const savedSidebarState = localStorage.getItem("desktopSidebarCollapsed");
        if (savedSidebarState === "true") {
          setIsDesktopSidebarCollapsed(true);
        }

        // 加载API配置
        const savedApiUrl = localStorage.getItem("chatApiUrl");
        const savedApiKey = localStorage.getItem("chatApiKey");
        if (savedApiUrl) chatService.apiUrl = savedApiUrl;
        if (savedApiKey) chatService.apiKey = savedApiKey;

        // 从后端加载会话列表
        let initialConversations: ConversationType[] = [];
        let initialActiveId = "";

        try {
          initialConversations = await conversationService.getConversations();
          
          // 确保所有会话都有sessionId
          initialConversations = initialConversations.map(conv => {
            if (!conv.sessionId) {
              return { ...conv, sessionId: nanoid() };
            }
            return conv;
          });

          // 如果没有会话，创建一个新的
          if (initialConversations.length === 0) {
            const newConv = createNewConversation();
            newConv.createdAt = Date.now();
            newConv.updatedAt = Date.now();
            newConv.title = `新会话 ${new Date().toLocaleString()}`;
            
            // 创建会话到后端
            const createdConv = await conversationService.createConversation(newConv);
            initialConversations = [createdConv];
          }

          // 尝试恢复活跃会话ID
          const savedActiveId = localStorage.getItem("activeConversationId");
          if (savedActiveId && initialConversations.some(conv => conv.id === savedActiveId)) {
            initialActiveId = savedActiveId;
          } else if (initialConversations.length > 0) {
            initialActiveId = initialConversations[0].id;
          }

        } catch (error) {
          console.error("从后端加载会话失败，尝试本地fallback:", error);

          // Fallback到localStorage
          try {
            const fallbackConversations = await conversationService.getConversations();
            initialConversations = fallbackConversations;
            
            if (initialConversations.length === 0) {
              const newConv = createNewConversation();
              newConv.createdAt = Date.now();
              newConv.updatedAt = Date.now();
              newConv.title = `新会话 ${new Date().toLocaleString()}`;
              initialConversations = [newConv];
            }

            const savedActiveId = localStorage.getItem("activeConversationId");
            if (savedActiveId && initialConversations.some(conv => conv.id === savedActiveId)) {
              initialActiveId = savedActiveId;
            } else if (initialConversations.length > 0) {
              initialActiveId = initialConversations[0].id;
            }
          } catch (fallbackError) {
            console.error("本地fallback也失败:", fallbackError);
            // 最后的fallback：创建一个基本会话
            const newConv = createNewConversation();
            newConv.createdAt = Date.now();
            newConv.updatedAt = Date.now();
            newConv.title = `新会话 ${new Date().toLocaleString()}`;
            initialConversations = [newConv];
            initialActiveId = newConv.id;
          }
        }

        setConversations(initialConversations);
        setActiveConversationId(initialActiveId);
        setIsInitialized(true);
      } catch (error) {
        console.error("初始化会话异常:", error);
        
        // 创建一个基本会话作为最后的fallback
        const newConv = createNewConversation();
        newConv.createdAt = Date.now();
        newConv.updatedAt = Date.now();
        newConv.title = `新会话 ${new Date().toLocaleString()}`;
        setConversations([newConv]);
        setActiveConversationId(newConv.id);
        setIsInitialized(true);
      } finally {
        setIsLoadingConversations(false);
      }
    };

    initializeConversations();
  }, [isInitialized])


  // 获取当前活跃会话
  function getActiveConversation(): ConversationType | undefined {
    return conversations.find(conv => conv.id === activeConversationId)
  }

  const activeConversation = getActiveConversation()

  // 监听活跃会话变化，保存到本地存储（用于恢复）
  useEffect(() => {
    if (isInitialized && activeConversationId) {
      localStorage.setItem("activeConversationId", activeConversationId)
    }
  }, [activeConversationId, isInitialized])

  // 保存侧边栏折叠状态
  useEffect(() => {
    if (isInitialized) {
      localStorage.setItem("desktopSidebarCollapsed", isDesktopSidebarCollapsed.toString())
    }
  }, [isDesktopSidebarCollapsed, isInitialized])

  // 将文件转换为附件对象
  const createAttachmentFromFile = (file: File): Promise<AttachmentType> => {
    return new Promise((resolve) => {
      const reader = new FileReader()

      reader.onload = (e) => {
        const dataUrl = e.target?.result as string

        resolve({
          id: nanoid(),
          name: file.name,
          type: file.type,
          url: dataUrl,
          size: file.size
        })
      }

      reader.readAsDataURL(file)
    })
  }

  // 停止流式响应
  const handleStopStreaming = async () => {
    if (abortControllerRef.current) {
      console.log('停止流式响应');
      
      // 1. 先中断前端的AbortController
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      setIsStreamingResponse(false);
      
      // 2. 异步发送后端中断请求（不阻塞前端）
      if (activeConversationId) {
        try {
          console.log(`发送后端中断请求: ${activeConversationId}`);
          const abortSuccess = await chatService.abortChatCompletion(activeConversationId);
          if (abortSuccess) {
            console.log('后端连接已成功中断');
          } else {
            console.warn('后端中断请求失败或未配置');
          }
        } catch (error) {
          console.error('后端中断请求异常:', error);
          // 不影响前端的中断流程
        }
      }
      
      // 3. 将当前流式回复的消息状态更新为完成状态，保留已生成的内容
      setConversations(prev => {
        return prev.map(conv => {
          if (conv.id === activeConversationId) {
            return {
              ...conv,
              messages: conv.messages.map(msg => {
                // 找到正在流式回复的消息并将其标记为完成
                if (msg.role === "assistant" && (msg.isStreaming || msg.isThinking)) {
                  // 提取当前内容中的思考过程
                  let extractedThinking = msg.thinking;
                  let cleanedContent = msg.content;
                  
                  // 如果还没有thinking字段，尝试从content中提取
                  if (!extractedThinking && msg.content) {
                    const thinkStart = msg.content.indexOf("<think>");
                    const thinkEnd = msg.content.indexOf("</think>");
                    
                    if (thinkStart !== -1) {
                      console.log('思考过程开始位置1:', thinkStart);
                      if (thinkEnd !== -1 && thinkEnd > thinkStart) {
                        // 完整的思考过程
                        extractedThinking = msg.content.substring(thinkStart + 7, thinkEnd).trim();
                        // 清理主内容中的思考标记
                        cleanedContent = (msg.content.substring(0, thinkStart) + 
                                        msg.content.substring(thinkEnd + 8)).trim();
                        console.log('思考过程结束位置1:', thinkEnd);
                      } else {
                        // 正在思考中，保留已有的思考内容
                        console.log('思考过程开始位置2:', thinkStart);
                        const thinkingRawContent = msg.content.substring(thinkStart + 7).trim();
                        // 确保思考内容不为空才保存
                        if (thinkingRawContent) {
                          console.log('思考过程内容3:', thinkingRawContent);
                          extractedThinking = thinkingRawContent + '</think>';
                        }
                        // 清理主内容，只保留思考标记前的内容
                        console.log('思考过程内容4:', msg.content.substring(0, thinkStart));
                        cleanedContent = msg.content.substring(0, thinkStart).trim();
                      }
                    }
                  }
                  
                  return {
                    ...msg,
                    isStreaming: false,
                    isThinking: false,
                    content: (cleanedContent === "..." || !cleanedContent.trim() 
                        ? "回复已被中断" 
                        : cleanedContent),
                    // 保留思考内容
                    thinking: extractedThinking || msg.thinking || undefined
                  };
                }
                return msg;
              }),
              updatedAt: Date.now()
            };
          }
          return conv;
        });
      });
      
      toast.info('已停止AI回复');
    }
  };

  // 调用API服务获取回复
  const callApiService = async (content: string, messageId: string, attachments?: AttachmentType[], modelTypeCalled?: string) => {
    // 记录设备类型和开始时间
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
    console.log(`调用API - 设备类型: ${isMobile ? '移动设备' : '桌面设备'}, 屏幕宽度: ${typeof window !== 'undefined' ? window.innerWidth : 'unknown'}`);
    console.log(`API配置状态 - URL: ${Boolean(chatService.apiUrl)}, Key: ${Boolean(chatService.apiKey)}`);

    const startTime = Date.now();
    let status: 0 | 1 = 1; // 默认成功
    let errorMessage = ''; // 错误信息
    let responseContent = ''; // 响应内容

    // 确定消息类型和内容
    let messageType: 'text' | 'image' | 'file' = 'text';
    let messageContent = content;
    let attachmentUrl = '';

    console.log(`消息内容: "${content}"`);
    console.log(`附件数量: ${attachments ? attachments.length : 0}`);

    // 处理附件，设置消息类型
    if (attachments && attachments.length > 0) {
      // 根据第一个附件确定类型
      const firstAttachment = attachments[0];
      attachmentUrl = firstAttachment.url; // 获取第一个附件的URL

      if (firstAttachment.type.startsWith('image/')) {
        messageType = 'image';
        // 如果没有文本内容，才设置为空字符串
        if (!content.trim()) {
          messageContent = '';
        }
      } else {
        messageType = 'file';
        // 如果没有文本内容，才设置为空字符串
        if (!content.trim()) {
          messageContent = '';
        }
      }
    }

    try {
      // 创建完整消息对象，附件将在API服务中处理
      const messageForApi: MessageType = {
        id: nanoid(),
        role: "user",
        content: messageContent, // 使用可能更新的内容
        timestamp: Date.now(),
        attachments: attachments // 直接传递附件对象
      };

      // 如果API配置未完成，使用模拟响应
      if (!chatService.apiUrl || !chatService.apiKey) {
        console.warn("API配置未完成，使用模拟响应");

        // 模拟思考过程
        const thinking = simulateThinking(messageContent);

        // 生成最终回复
        const finalResponse = simulateResponse(messageContent, undefined);
        responseContent = finalResponse; // 保存响应内容

        // 提取思考过程
        const { mainContent, thinking: extractedThinking } = extractThinkingContent(finalResponse);

        // 计算处理时间
        const processTime = Date.now() - startTime;

        // 更新消息，包含处理时间
        updateAssistantMessage(messageId, mainContent, extractedThinking || thinking, processTime);

        // 获取当前会话信息
        const currentConversation = getActiveConversation();
        
        // 异步保存对话记录
        logConversation(messageContent, responseContent, startTime, status, errorMessage, messageType, attachmentUrl);

        return;
      }

      // 使用流式API
      let accumulatedContent = "";

      // 流式回调函数
      let updateTimer: NodeJS.Timeout | null = null;
      let lastUpdateTime = 0;
      const updateInterval = 300; // 增加到每300ms最多更新一次
      let chunkCount = 0; // 用于跟踪收到的块数
      const minChunksBeforeUpdate = 5; // 至少收到 5 个块才更新

      const handleChunk = (chunk: string) => {
        // 检查是否是特殊格式的消息，包含状态和名称
        const startMarker = '__STATUS_NAME__';
        const endMarker = '__';
        const startIndex = chunk.indexOf(startMarker);

        if (startIndex !== -1 && chunk.endsWith(endMarker)) {
          // 使用索引定位提取名称，更加健壮
          const nameStartIndex = startIndex + startMarker.length;
          const nameEndIndex = chunk.length - endMarker.length;
          const name = chunk.substring(nameStartIndex, nameEndIndex);

          console.log('收到Agent名称更新:', name);

          // 触发自定义事件，更新ThinkingIndicator组件中的名称
          const nameUpdateEvent = new CustomEvent('agent-name-update', {
            detail: { name }
          });
          window.dispatchEvent(nameUpdateEvent);

          // 不将此特殊消息添加到累积内容中
          return;
        }

        accumulatedContent += chunk;
        chunkCount++;

        const now = Date.now();
        // 只有同时满足时间间隔和最小块数要求时才更新
        const timeCondition = now - lastUpdateTime >= updateInterval;
        const chunkCondition = chunkCount >= minChunksBeforeUpdate;

        // 如果同时满足时间和块数条件，或者还没有计时器且时间条件满足，则更新
        if ((timeCondition && chunkCondition) || (!updateTimer && timeCondition)) {
          // 清除之前的定时器
          if (updateTimer) {
            clearTimeout(updateTimer);
            updateTimer = null;
          }

          // 更新消息，但保持思考状态和流式状态
          updateAssistantMessageStream(messageId, accumulatedContent);
          lastUpdateTime = now;
          chunkCount = 0; // 重置块计数
        } else if (!updateTimer && timeCondition) {
          // 如果时间条件满足但块数不足，设置一个定时器
          updateTimer = setTimeout(() => {
            updateAssistantMessageStream(messageId, accumulatedContent);
            lastUpdateTime = Date.now();
            updateTimer = null;
            chunkCount = 0; // 重置块计数
          }, updateInterval - (now - lastUpdateTime));
        }
        // 如果时间条件不满足，则等待下一个块
      };

      // 获取活跃会话的信息用于API调用
      const activeConversation = getActiveConversation();
      if (!activeConversation) return;

      console.log('开始调用流式API...');

      // 创建中断控制器
      abortControllerRef.current = new AbortController();

      // 设置流式响应状态为正在进行
      setIsStreamingResponse(true);

      try {
        // 调用API服务
        await chatService.processMessageStream(
          activeConversationId,
          messageForApi,
          handleChunk,
          modelTypeCalled,
          abortControllerRef.current?.signal
        );
        console.log('API调用成功完成');

        // API调用完成后清理所有定时器
        if (updateTimer) {
          clearTimeout(updateTimer);
          updateTimer = null;
        }

        // 清理防抖计时器
        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
          debounceTimerRef.current = null;
        }

        // 确保最后的内容被更新
        performUpdate(messageId, accumulatedContent);

        // 设置流式响应状态为已结束
        setIsStreamingResponse(false);

        // 清理中断控制器
        abortControllerRef.current = null;
      } catch (error) {
        // 出错时清理所有定时器
        if (updateTimer) {
          clearTimeout(updateTimer);
          updateTimer = null;
        }

        // 清理防抖计时器
        if (debounceTimerRef.current) {
          clearTimeout(debounceTimerRef.current);
          debounceTimerRef.current = null;
        }

        // 设置流式响应状态为已结束
        setIsStreamingResponse(false);

        // 清理中断控制器
        abortControllerRef.current = null;

        // 检查是否是中断错误
        if (error instanceof Error && error.name === 'AbortError') {
          console.log('流式响应被用户中断');
          return; // 不显示错误消息，因为是用户主动中断
        }

        console.error('在ChatInterface中捕获到API调用错误:', error);
        throw error; // 重新抛出错误以便外层catch处理
      }

      // 计算处理时间
      const processTime = Date.now() - startTime;

      // 完成后进行最终更新
      // 提取思考过程（如果回复中已包含思考标签）
      const { mainContent, thinking } = extractThinkingContent(accumulatedContent);
      responseContent = accumulatedContent; // 保存完整响应内容

      // 最终更新，取消思考状态，并包含处理时间
      updateAssistantMessage(messageId, mainContent, thinking || undefined, processTime);
    } catch (error) {
      console.error("API调用异常:", error instanceof Error ? error.message : String(error));
      toast.error("获取回复失败");

      status = 0; // 设置失败状态
      errorMessage = error instanceof Error ? error.message : String(error);
      responseContent = "很抱歉，在处理您的请求时遇到了问题。请稍后再试。";

      // 计算处理时间
      const processTime = Date.now() - startTime;

      // 更新消息为错误状态，包含处理时间
      updateAssistantMessage(
        messageId,
        responseContent,
        undefined,
        processTime
      );
    } finally {
      // 异步保存对话记录
      logConversation(messageContent, responseContent, startTime, status, errorMessage, messageType, attachmentUrl);
    }
  };

  // 保存对话记录（异步执行，不影响主流程）
  const logConversation = async (
    userMessage: string,
    aiResponse: string,
    startTime: number,
    status: 0 | 1 = 1,
    errorMessage: string = '',
    messageType: 'text' | 'image' | 'file' = 'text',
    attachmentUrl: string = ''
  ) => {
    try {
      // 从企业微信中获取用户ID，如果没有则使用会话ID
      const userId = getUserInfo()?.userId || activeConversationId;

      // 获取当前活跃会话的信息
      const activeConv = getActiveConversation();
      const sessionId = activeConv?.sessionId || '';
      const sessionName = activeConv?.title || '';

      // 确保消息内容不为空（API要求）
      const safeUserMessage = userMessage || "[空消息]";

      // 计算处理时间（毫秒）
      const processTime = Date.now() - startTime;

      // 确定环境
      const environment = process.env.NEXT_PUBLIC_ENV || process.env.NODE_ENV || 'dev';

      // 异步保存记录，不等待结果
      saveConversationLog(
        userId,
        safeUserMessage,
        aiResponse,
        messageType,
        status,
        errorMessage,
        processTime,
        environment as 'dev' | 'prd',
        sessionId, // 会话ID参数
        sessionName, // 会话名称参数
        attachmentUrl // 附件URL参数
      ).catch(err => {
        console.error('保存对话记录失败，但不影响主流程:', err);
      });
    } catch (error) {
      console.error('日志记录错误，但不影响主流程:', error);
    }
  };

  // 获取用户信息的辅助函数
  const getUserInfo = () => {
    try {
      // 从localStorage获取企业微信用户信息
      const userInfoJson = localStorage.getItem('wechat_work_user');
      if (userInfoJson) {
        return JSON.parse(userInfoJson);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
    return null;
  };

  // 检查并重命名会话 - 简化版：用户发送第一条消息后立即重命名
  async function checkAndRenameConversation(conversation: ConversationType) {
    // 检查当前会话是否为默认名称
    if (conversation.title.includes("新会话")) {
      // 获取有效的用户消息（排除思考中的消息）
      const userMessages = conversation.messages.filter(
        msg => !msg.isThinking && msg.role === "user"
      );

      // 只要有第一条用户消息就立即重命名
      if (userMessages.length >= 1) {
        try {
          // 使用第一条用户消息的前10个字符来生成标题
          const firstUserMessage = userMessages[0];

          if (firstUserMessage) {
            // 从用户消息中提取前10个字符作为标题
            const userContent = firstUserMessage.content.trim();
            let newTitle = userContent.length <= 10 
              ? userContent 
              : userContent.substring(0, 10) + "...";
            
            // 确保标题不为空
            if (!newTitle.trim()) {
              newTitle = "新对话";
            }

            // 检查标题是否真的发生了变化，避免重复重命名
            if (newTitle !== conversation.title) {
              // 使用静默版本的重命名（不显示toast消息）
              await handleRenameConversationSilent(conversation.id, newTitle);
              console.log(`会话已自动重命名为: ${newTitle}`);
            }
          }
        } catch (error) {
          console.error("自动重命名会话失败:", error);
        }
      }
    }
  }

  // 处理发送消息
  async function handleSendMessage(
    content: string,
    fileUrls?: {imageUrls?: string[], imageIds?: string[], fileUrls?: string[], fileNames?: string[]},
    modelTypeCalled?: string
  ) {
    // 检查是否有内容或附件
    if (!content.trim() && (!fileUrls || (
      (!fileUrls.imageUrls || fileUrls.imageUrls.length === 0) &&
      (!fileUrls.fileUrls || fileUrls.fileUrls.length === 0)
    ))) return

    const activeConversation = getActiveConversation()
    if (!activeConversation) return

    // 消息内容，如果文本为空但有附件，则使用附件描述
    let messageContent = content.trim()

    // 处理文件URLs (如果有)
    let attachments: AttachmentType[] | undefined
    if (fileUrls) {
      try {
        // 从 imageUrls 创建图片附件，同时添加 imageId（如果有）
        const imageAttachments = fileUrls.imageUrls?.map((url, index) => ({
          id: nanoid(),
          name: url.split('/').pop() || `图片${nanoid().substring(0, 6)}`,
          type: 'image/png', // 默认类型，实际应该根据URL或MIME类型确定
          url: url,
          // 如果有对应的图片ID，则添加到附件中
          imageId: fileUrls.imageIds && fileUrls.imageIds[index] ? fileUrls.imageIds[index] : undefined,
          size: 0 // 从URL无法直接获取大小
        })) || [];

        // 从 fileUrls 创建文件附件
        const fileAttachments = fileUrls.fileUrls?.map((url, index) => ({
          id: nanoid(),
          name: fileUrls.fileNames && fileUrls.fileNames[index]
            ? fileUrls.fileNames[index]
            : url.split('/').pop() || `文件${nanoid().substring(0, 6)}`,
          type: 'application/octet-stream', // 默认类型，实际应该根据URL或扩展名确定
          url: url,
          size: 0 // 从URL无法直接获取大小
        })) || [];

        attachments = [...imageAttachments, ...fileAttachments];

        // 检查是否需要根据附件设置消息内容
        if (attachments.length > 0) {
          // 只有当没有文本内容时才设置为空字符串
          if (!messageContent) {
            messageContent = '';
          }
        }
      } catch (error) {
        console.error("文件处理错误:", error)
        toast.error("文件处理失败，请重试")
        return
      }
    }

    // 创建用户消息
    const userMessageId = nanoid()
    const userMessage: MessageType = {
      id: userMessageId,
      role: "user",
      content: messageContent, // 使用可能更新后的内容
      timestamp: Date.now(),
      attachments
    }

    // 创建临时思考消息
    const thinkingId = nanoid()
    const thinkingMessage: MessageType = {
      id: thinkingId,
      role: "assistant",
      content: "...",
      timestamp: Date.now(),
      isThinking: true
    }

    // 更新对话
    setConversations(prev => {
      const updated = prev.map(conv => {
        if (conv.id === activeConversationId) {
          const updatedConv = {
            ...conv,
            messages: [...conv.messages, userMessage, thinkingMessage],
            updatedAt: Date.now()
          };
          
          // 检查是否需要重命名会话（用户发送第一条消息后立即重命名）
          setTimeout(() => {
            checkAndRenameConversation(updatedConv);
          }, 100); // 给一个短暂延迟确保状态更新完成
          
          return updatedConv;
        }
        return conv
      });
      return updated;
    })

    // 调用API服务获取回复
    await callApiService(messageContent, thinkingId, attachments, modelTypeCalled);

    // 确保发送消息后多次尝试滚动到底部，增强可靠性
    if (scrollToBottomRef.current) {
      // 立即滚动一次
      scrollToBottomRef.current();

      // 稍后再滚动一次，确保内容已经渲染
      setTimeout(() => {
        scrollToBottomRef.current?.();
      }, 100);

      // 再次尝试滚动，处理可能的内容动态变化
      setTimeout(() => {
        scrollToBottomRef.current?.();
      }, 500);

      // 最后一次尝试，处理图表等大型内容
      setTimeout(() => {
        scrollToBottomRef.current?.();
      }, 1500);
    }
  }

  // 创建新会话 - 优化版本，直接切换无需同步提示
  async function handleNewConversation() {
    try {
      const newConversationData = {
        ...createNewConversation(),
        createdAt: Date.now(),
        updatedAt: Date.now(),
        title: `新会话 ${new Date().toLocaleString()}`,
        sessionId: nanoid()
      };

      // 立即更新本地状态，先切换到新会话
      const tempConversation = { ...newConversationData, id: newConversationData.sessionId };
      setConversations(prev => [tempConversation, ...prev]); // 新会话放在最前面
      setActiveConversationId(tempConversation.id);
      
      // 后台异步创建会话到后端
      conversationService.createConversation(newConversationData)
        .then((createdConversation) => {
          // 成功后更新会话数据
          setConversations(prev => 
            prev.map(conv => 
              conv.id === tempConversation.id ? createdConversation : conv
            )
          );
          setActiveConversationId(createdConversation.id);
        })
        .catch((error) => {
          console.error("后台创建会话失败:", error);
          // 如果后台创建失败，保持本地会话可用
        });
        
    } catch (error) {
      console.error("创建新会话失败:", error);
      toast.error("创建新会话失败，请重试");
    }
  }

  // 切换会话
  function handleSwitchConversation(id: string) {
    setActiveConversationId(id)

    // 在移动端视图下，点击会话后自动关闭侧边栏
    if (window.innerWidth < 768) {
      setIsSidebarOpen(false)
    }

    // 使用setTimeout确保在会话切换后滚动到底部
    setTimeout(() => {
      if (scrollToBottomRef.current) {
        scrollToBottomRef.current();
      }
    }, 100);
  }

  // 删除会话
  // 删除会话 - 修改为使用API
  async function handleDeleteConversation(id: string) {
    try {
      // 调用API删除会话
      await conversationService.deleteConversation(id);
      
      // 更新本地状态
      setConversations(prev => {
        const filtered = prev.filter(conv => conv.id !== id);
        
        // 如果删除的是当前活跃会话，则切换到第一个会话
        if (id === activeConversationId && filtered.length > 0) {
          setActiveConversationId(filtered[0].id);
        } else if (filtered.length === 0) {
          // 如果没有会话了，创建一个新的
          handleNewConversation();
        }
        
        return filtered;
      });
      
      toast.success("会话已删除");
    } catch (error) {
      console.error("删除会话失败:", error);
      toast.error("删除会话失败，请重试");
    }
  }

  // 重命名会话 - 修改为使用API（静默版本，不显示toast）
  async function handleRenameConversationSilent(id: string, newTitle: string) {
    try {
      // 调用API更新会话
      await conversationService.updateConversation(id, { title: newTitle });
      
      // 更新本地状态
      setConversations(prev => {
        return prev.map(conv => {
          if (conv.id === id) {
            return {
              ...conv,
              title: newTitle,
              updatedAt: Date.now()
            };
          }
          return conv;
        });
      });
      
      // 静默版本不显示toast消息
    } catch (error) {
      console.error("重命名会话失败:", error);
      // 静默版本也不显示错误toast
    }
  }

  // 重命名会话 - 修改为使用API
  async function handleRenameConversation(id: string, newTitle: string) {
    try {
      // 调用API更新会话
      await conversationService.updateConversation(id, { title: newTitle });
      
      // 更新本地状态
      setConversations(prev => {
        return prev.map(conv => {
          if (conv.id === id) {
            return {
              ...conv,
              title: newTitle,
              updatedAt: Date.now()
            };
          }
          return conv;
        });
      });
      
      toast.success("会话已重命名");
    } catch (error) {
      console.error("重命名会话失败:", error);
      toast.error("重命名会话失败，请重试");
    }
  }

  // 清空当前会话
  function handleClearMessages() {
    setConversations(prev => {
      return prev.map(conv => {
        if (conv.id === activeConversationId) {
          return {
            ...conv,
            messages: [],
            updatedAt: Date.now()
          }
        }
        return conv
      })
    })
    toast.success("会话已清空")
  }

  // 使用useRef创建一个防抖计时器
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef<number>(0);
  const pendingContentRef = useRef<{messageId: string, content: string} | null>(null);

  // 流式更新助手消息（保持思考状态），添加防抖机制
  function updateAssistantMessageStream(messageId: string, content: string) {
    // 保存最新的内容
    pendingContentRef.current = { messageId, content };

    const now = Date.now();
    // 对于 DeepSeek-R1 模型，我们使用更长的更新间隔来减少状态更新频率
    const minUpdateInterval = 300; // 最小更新间隔增加到300ms

    // 如果距离上次更新时间不足最小间隔，则设置一个定时器延迟更新
    if (now - lastUpdateTimeRef.current < minUpdateInterval) {
      // 如果已经有一个定时器在运行，则不需要再设置
      if (!debounceTimerRef.current) {
        debounceTimerRef.current = setTimeout(() => {
          // 定时器触发时，使用最新的内容进行更新
          if (pendingContentRef.current) {
            const { messageId, content } = pendingContentRef.current;
            performUpdate(messageId, content);
            pendingContentRef.current = null;
          }
          debounceTimerRef.current = null;
          lastUpdateTimeRef.current = Date.now();
        }, minUpdateInterval - (now - lastUpdateTimeRef.current));
      }
    } else {
      // 如果距离上次更新时间已经足够长，则立即更新
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      performUpdate(messageId, content);
      lastUpdateTimeRef.current = now;
    }
  }

  // 实际执行更新的函数
  function performUpdate(messageId: string, content: string) {
    setConversations(prev => {
      return prev.map(conv => {
        if (conv.id === activeConversationId) {
          return {
            ...conv,
            messages: conv.messages.map(msg => {
              if (msg.id === messageId) {
                return {
                  ...msg,
                  content: content,
                  isThinking: false, // 不再保持思考状态，以便显示内容
                  isStreaming: true // 标记为正在流式接收
                }
              }
              return msg
            }),
            updatedAt: Date.now()
          }
        }
        return conv
      })
    })
  }

  // 辅助函数：更新助手消息
  function updateAssistantMessage(messageId: string, content: string, thinking?: string, processTime?: number) {
    setConversations(prev => {
      return prev.map(conv => {
        if (conv.id === activeConversationId) {
          return {
            ...conv,
            messages: conv.messages.map(msg => {
              if (msg.id === messageId) {
                return {
                  ...msg,
                  content: content,
                  thinking: thinking,
                  isThinking: false,
                  isStreaming: false, // 完成流式接收
                  processTime: processTime
                }
              }
              return msg
            }),
            updatedAt: Date.now()
          }
        }
        return conv
      })
    })
  }

  // 模拟思考过程
  function simulateThinking(message: string): string {
    // 根据输入返回模拟的思考过程
    if (message.toLowerCase().includes("markdown")) {
      return `我需要展示Markdown的示例用法。
1. 首先我会展示基本格式，如粗体、斜体的使用方法
2. 然后展示列表的用法
3. 代码块的使用
4. 最后加入表格示例

这是一个很好的机会来展示Markdown的多种功能，用户可以看到如何使用这些语法。`
    } else if (message.toLowerCase().includes("图表")) {
      return `用户想了解图表功能。我需要：
1. 分析应该展示什么类型的图表
2. 决定了使用柱状图，因为这是最直观的图表类型
3. 设计数据结构：一月到五月的销售数据
4. 使用chart代码块格式来生成图表
5. 补充说明其他可能的图表类型`
    } else if (message.toLowerCase().includes("代码")) {
      return `用户想看代码展示示例。我应该：
1. 选择几种常用编程语言
2. JavaScript是必选的，因为它最常用
3. Python也很重要，应该包含
4. HTML/CSS组合是Web开发的基础，也应该展示
5. 为每种语言准备简单但有代表性的代码示例
6. 确保代码是正确的且格式良好`
    } else {
      return `我应该如何回应这个问题？
1. 这是一个基本问候或一般性询问
2. 我应该介绍自己的能力
3. 展示我可以执行的特殊功能会很有帮助
4. 我会列出几个关键词，引导用户使用特定功能
5. 保持友好和专业的语气`
    }
  }

  // 模拟机器人响应（在实际项目中会替换为真实的AI调用）
  function simulateResponse(message: string, attachments?: File[]): string {
    // 如果有附件，优先处理附件内容
    if (attachments && attachments.length > 0) {
      const fileDescriptions = attachments.map(file =>
        `- ${file.name} (${(file.size / 1024).toFixed(1)} KB)`
      ).join('\n');

      return `我收到了你的${attachments.length}个附件：\n${fileDescriptions}\n\n请问你想了解这些文件的什么信息？`;
    }

    // 简单示例：根据输入提供一些预设的回复
    if (message.toLowerCase().includes("markdown")) {
      return `
# Markdown 示例
这是 **粗体文本** 和 *斜体文本*

## 列表示例
- 项目 1
- 项目 2

## 代码示例
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

## 表格示例
| 名称 | 年龄 |
|------|-----|
| 张三 | 25  |
| 李四 | 30  |
      `
    } else if (message.toLowerCase().includes("图表")) {
      return `
# 图表示例

<thinking>
下面我将生成一个简单的柱状图示例，展示1-5月的销售数据。
这里使用了特殊的代码块格式 \`\`\`chart，系统会将其渲染为图表。
这个数据结构是简单的JSON格式，指定了chart类型和数据点。
</thinking>

\`\`\`chart
{
  "type": "bar",
  "data": {
    "labels": ["一月", "二月", "三月", "四月", "五月"],
    "datasets": [{
      "label": "销售数据",
      "data": [12, 19, 3, 5, 2]
    }]
  }
}
\`\`\`

你也可以尝试其他类型的图表，比如折线图、饼图等。
      `
    } else if (message.toLowerCase().includes("代码")) {
      return `
# 代码展示示例

## JavaScript
\`\`\`javascript
// JavaScript 示例
const greet = (name) => {
  return \`Hello, \${name}!\`;
};

console.log(greet('世界'));
\`\`\`

## Python
\`\`\`python
# Python 示例
def greet(name):
    return f"Hello, {name}!"

print(greet('世界'))
\`\`\`

## HTML/CSS
\`\`\`html
<!-- HTML 示例 -->
<div class="greeting">
  <h1>你好，世界！</h1>
  <p>欢迎使用我们的聊天机器人。</p>
</div>

<style>
  .greeting {
    color: blue;
    text-align: center;
  }
</style>
\`\`\`
      `
    } else {
      return `你好！我是一个现代化的聊天机器人，可以支持 Markdown 语法、展示图表和代码，以及接收你上传的文件和图片。

<thinking>
我应该展示一些基本功能，让用户了解如何使用我。
Markdown、图表和代码展示是我的主要特色功能。
用户可能还不知道如何使用这些功能，所以我需要提供关键词提示。
</thinking>

尝试输入以下关键词来查看特殊功能：
- "markdown" - 查看 Markdown 格式示例
- "图表" - 查看图表展示功能
- "代码" - 查看代码高亮示例

也可以点击输入框旁边的图标上传文件或图片。`;
    }
  }

  // 添加重新生成消息的函数
  async function handleRegenerateMessage(messageId: string) {
    const activeConversation = getActiveConversation();
    if (!activeConversation) return;

    // 找到需要重新生成的消息
    const messageIndex = activeConversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    // 找出对应的用户消息
    let userMessageIndex = messageIndex - 1;
    while (userMessageIndex >= 0) {
      if (activeConversation.messages[userMessageIndex].role === 'user') {
        break;
      }
      userMessageIndex--;
    }

    if (userMessageIndex < 0) return;

    const userMessage = activeConversation.messages[userMessageIndex];

    // 删除当前的AI回复和用户消息
    const newMessages = [...activeConversation.messages];
    // 如果AI回复在用户消息后面，先删除AI回复，再删除用户消息
    if (messageIndex > userMessageIndex) {
      newMessages.splice(messageIndex, 1); // 删除AI回复
      newMessages.splice(userMessageIndex, 1); // 删除用户消息
    } else {
      newMessages.splice(userMessageIndex, 1); // 删除用户消息
      newMessages.splice(messageIndex, 1); // 删除AI回复
    }

    // 更新对话，移除原有的消息
    setConversations(prev =>
      prev.map(conv =>
        conv.id === activeConversationId
          ? {...conv, messages: newMessages, updatedAt: Date.now()}
          : conv
      )
    );

    // 创建新的用户消息，保持原有ID以便跟踪
    const newUserMessage: MessageType = {
      ...userMessage,
      timestamp: Date.now() // 更新时间戳
    };

    // 创建思考中消息
    const thinkingId = nanoid();
    const tempMessage: MessageType = {
      id: thinkingId,
      role: "assistant",
      content: "...",
      timestamp: Date.now(),
      isThinking: true
    };

    // 添加新的用户消息和临时思考消息
    setConversations(prev =>
      prev.map(conv =>
        conv.id === activeConversationId
          ? {
              ...conv,
              messages: [...newMessages, newUserMessage, tempMessage],
              updatedAt: Date.now()
            }
          : conv
      )
    );

    try {
      // 调用API获取回复，使用当前选择的模型
      await callApiService(userMessage.content, thinkingId, userMessage.attachments, selectedModel);
    } catch (error) {
      console.error("重新生成回复失败:", error instanceof Error ? error.message : String(error));
      toast.error("重新生成回复失败，请重试");

      // 移除思考中消息和新添加的用户消息
      setConversations(prev =>
        prev.map(conv =>
          conv.id === activeConversationId
            ? {
                ...conv,
                messages: conv.messages.filter(m => m.id !== thinkingId && m.id !== newUserMessage.id),
                updatedAt: Date.now()
              }
            : conv
        )
      );
    }
  }

  // 处理桌面侧边栏折叠/展开
  const toggleDesktopSidebar = () => {
    setIsDesktopSidebarCollapsed(prev => !prev)
  }

  // 计算主内容区域的宽度样式
  const getMainContentWidth = () => {
    // 检测是否为移动端设备
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

    // 移动端始终使用100%宽度
    if (isMobile) {
      return "w-full h-full flex flex-col";
    }

    // 只有在AI聊天对话页面时使用80%宽度，其他页面使用100%宽度
    const isAIChatPage = !showAITools && !showDocProcessing && !showVisualization;

    if (isAIChatPage) {
      return "w-full max-w-[80%] mx-auto h-full flex flex-col md:max-w-[80%] sm:max-w-full";
    } else {
      return "w-full h-full flex flex-col";
    }
  };

  // 调整显示面板的逻辑
  const renderMainContent = () => {
    if (showAITools) {
      return <AIToolsPanel />;
    }

    if (showDocProcessing) {
      return <DocProcessingPanel />;
    }

    if (showVisualization) {
      return (
        <div className="relative w-full h-full" style={{ zIndex: 5 }}>
          <ChatChartPanel onSendMessage={handleSendMessage} />
        </div>
      );
    }

    // 默认显示聊天界面
    return (
      <>
        <div className="flex-1 overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          {/* 加载状态 */}
          {isLoadingConversations ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">加载会话中...</p>
              </div>
            </div>
          ) : (
            <ChatMessages
              messages={activeConversation?.messages || []}
              onRegenerateMessage={handleRegenerateMessage}
              messagesEndRef={messagesEndRef}
              scrollToBottomRef={scrollToBottomRef}
              onSendMessage={handleSendMessage}
              isInCenteredLayout={true}
            />
          )}
        </div>
        <div className="border-t p-4 md:px-6">
          <ChatInput
            onSendMessage={handleSendMessage}
            onNewConversation={handleNewConversation}
            isDisabled={isStreamingResponse}
            onCreateAttachment={createAttachmentFromFile}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
            onStopStreaming={handleStopStreaming}
          />
        </div>
      </>
    );
  };

  // 如果还未初始化，显示一个简单的加载状态
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center animate-pulse">
          <p className="text-lg">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="relative flex h-full w-full overflow-auto">
        {/* 大屏幕侧边栏 */}
        <div
          className={cn(
            "hidden md:block border-r transition-all duration-300 ease-in-out",
            isDesktopSidebarCollapsed ? "w-[60px]" : "w-[280px]"
          )}
        >
          <ErrorBoundary fallback={<div className="p-4">侧边栏加载失败</div>}>
            <ChatSidebar
              conversations={conversations}
              activeConversationId={activeConversationId}
              onNewConversation={handleNewConversation}
              onSwitchConversation={handleSwitchConversation}
              onDeleteConversation={handleDeleteConversation}
              onRenameConversation={handleRenameConversation}
              onClearMessages={handleClearMessages}
              isCollapsed={isDesktopSidebarCollapsed}
              onToggleCollapse={toggleDesktopSidebar}
              showAITools={showAITools}
              setShowAITools={setShowAITools}
              showDocProcessing={showDocProcessing}
              setShowDocProcessing={setShowDocProcessing}
              showVisualization={showVisualization}
              setShowVisualization={setShowVisualization}

            />
          </ErrorBoundary>
        </div>

        {/* 移动端侧边栏（抽屉式） */}
        <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden absolute left-2 top-2 z-10"
            >
              <Menu className="h-6 w-6" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-[280px]">
            <ErrorBoundary fallback={<div className="p-4">侧边栏加载失败</div>}>
              <ChatSidebar
                conversations={conversations}
                activeConversationId={activeConversationId}
                onNewConversation={handleNewConversation}
                onSwitchConversation={handleSwitchConversation}
                onDeleteConversation={handleDeleteConversation}
                onRenameConversation={handleRenameConversation}
                onClearMessages={handleClearMessages}
                onClose={() => setIsSidebarOpen(false)}
                showAITools={showAITools}
                setShowAITools={setShowAITools}
                showDocProcessing={showDocProcessing}
                setShowDocProcessing={setShowDocProcessing}
                showVisualization={showVisualization}
                setShowVisualization={setShowVisualization}

              />
            </ErrorBoundary>
          </SheetContent>
        </Sheet>

        {/* 主内容区域 - 根据页面类型动态调整宽度 */}
        <div className="flex-1 flex flex-col overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          <div className={getMainContentWidth()} data-tour="chat-panel">
            {renderMainContent()}
          </div>
        </div>
      </div>

    </ErrorBoundary>
  )
}