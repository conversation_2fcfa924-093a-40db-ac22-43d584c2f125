"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'

// 避免水合错误的客户端检查Hook
function useIsClient() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}

interface DateTimePickerProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  required?: boolean
  name?: string
  id?: string
  className?: string
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  value = '',
  onChange,
  placeholder = '选择日期和时间',
  required = false,
  name,
  id,
  className
}) => {
  const isClient = useIsClient()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [displayValue, setDisplayValue] = useState('')

  // 初始化值 - 只在客户端执行
  useEffect(() => {
    if (!isClient) return

    if (value) {
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        const dateStr = date.toISOString().split('T')[0]
        const timeStr = date.toTimeString().slice(0, 5)
        setSelectedDate(dateStr)
        setSelectedTime(timeStr)
        setDisplayValue(`${dateStr} ${timeStr}`)
        return
      }
    }

    // 如果没有有效值，设置默认值
    const now = new Date()
    const defaultDate = now.toISOString().split('T')[0]
    const defaultTime = now.toTimeString().slice(0, 5)
    setSelectedDate(defaultDate)
    setSelectedTime(defaultTime)
  }, [value, isClient])

  // 处理日期变化
  const handleDateChange = (newDate: string) => {
    setSelectedDate(newDate)
    updateDateTime(newDate, selectedTime)
  }

  // 处理时间变化
  const handleTimeChange = (newTime: string) => {
    setSelectedTime(newTime)
    updateDateTime(selectedDate, newTime)
  }

  // 更新日期时间值
  const updateDateTime = (date: string, time: string) => {
    if (date && time) {
      const dateTimeValue = `${date}T${time}`
      setDisplayValue(`${date} ${time}`)
      onChange?.(dateTimeValue)
    } else if (date) {
      setDisplayValue(date)
      onChange?.(date)
    }
  }

  // 确认选择
  const handleConfirm = () => {
    if (selectedDate && selectedTime) {
      const dateTimeValue = `${selectedDate}T${selectedTime}`
      setDisplayValue(`${selectedDate} ${selectedTime}`)
      onChange?.(dateTimeValue)
    }
    setIsOpen(false)
  }

  // 清除选择
  const handleClear = () => {
    setSelectedDate('')
    setSelectedTime('')
    setDisplayValue('')
    onChange?.('')
    setIsOpen(false)
  }

  // 设置为现在
  const handleToday = () => {
    if (!isClient) return

    const now = new Date()
    const today = now.toISOString().split('T')[0]
    const currentTime = now.toTimeString().slice(0, 5)
    setSelectedDate(today)
    setSelectedTime(currentTime)
    updateDateTime(today, currentTime)
  }

  // 在服务器端渲染时显示占位符，避免水合错误
  if (!isClient) {
    return (
      <div className="relative">
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal h-11 px-4 py-3 rounded-lg border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-colors shadow-sm text-muted-foreground min-w-[280px]",
            className
          )}
          disabled
        >
          <Calendar className="mr-2 h-4 w-4" />
          {placeholder}
        </Button>
        <input
          type="hidden"
          name={name}
          id={id}
          value={value}
          required={required}
        />
      </div>
    )
  }

  return (
    <div className="relative">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal h-11 px-4 py-3 rounded-lg border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-colors shadow-sm min-w-[280px]",
              !displayValue && "text-muted-foreground",
              className
            )}
          >
            <Calendar className="mr-2 h-4 w-4" />
            {displayValue || placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-6 space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">选择日期</Label>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => handleDateChange(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  选择时间
                </Label>
                <Input
                  type="time"
                  value={selectedTime}
                  onChange={(e) => handleTimeChange(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleToday}
                  className="flex-1"
                >
                  现在
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleClear}
                  className="flex-1"
                >
                  清除
                </Button>
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  size="sm"
                  onClick={handleConfirm}
                  disabled={!selectedDate || !selectedTime}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  确定
                </Button>
              </div>
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
      
      {/* 隐藏的input用于表单提交 */}
      <input
        type="hidden"
        name={name}
        id={id}
        value={value}
        required={required}
      />
    </div>
  )
}

export default DateTimePicker
