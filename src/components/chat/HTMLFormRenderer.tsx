"use client"

import React, { useState, useCallback } from 'react'
import DOMPurify from 'dompurify'
import parse, { HTMLReactParserOptions, Element } from 'html-react-parser'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DateTimePicker } from './DateTimePicker'

// 现代loading组件
const ModernLoader = ({ size = 16, className = "" }: { size?: number, className?: string }) => {
  return (
    <div className={cn("relative", className)} style={{ width: size, height: size }}>
      <div
        className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-blue-500 to-purple-500 animate-spin"
        style={{
          background: 'conic-gradient(from 0deg, transparent, #3b82f6, #8b5cf6, transparent)',
          mask: 'radial-gradient(farthest-side, transparent calc(100% - 2px), black calc(100% - 2px))',
          WebkitMask: 'radial-gradient(farthest-side, transparent calc(100% - 2px), black calc(100% - 2px))'
        }}
      />
    </div>
  )
}

interface HTMLFormRendererProps {
  htmlContent: string
  onFormSubmit?: (formData: FormData, formElement: HTMLFormElement) => void
  className?: string
  useCustomDateTimePicker?: boolean // 是否使用自定义日期时间选择器，默认true
}

// HTML表单渲染组件
export const HTMLFormRenderer: React.FC<HTMLFormRendererProps> = ({
  htmlContent,
  onFormSubmit,
  className,
  useCustomDateTimePicker = true
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // 验证表单字段
  const validateForm = useCallback((form: HTMLFormElement) => {
    const errors: Record<string, string> = {}
    const formDataObj = new FormData(form)

    // 检查必填字段
    const requiredFields = form.querySelectorAll('[required]')
    requiredFields.forEach((field) => {
      const input = field as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      const value = formDataObj.get(input.name)

      if (!value || (typeof value === 'string' && !value.trim())) {
        const label = form.querySelector(`label[for="${input.id}"]`)?.textContent ||
                     input.getAttribute('placeholder') ||
                     input.name
        errors[input.name] = `请填写 ${label}`
      }
    })

    return errors
  }, [])

  // 处理表单提交
  const handleFormSubmit = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const form = event.currentTarget
    const formDataObj = new FormData(form)

    // 验证表单
    const errors = validateForm(form)
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors)
      const firstError = Object.values(errors)[0]
      toast.error('表单验证失败', {
        description: firstError
      })
      return
    }

    // 清除验证错误
    setValidationErrors({})
    setIsSubmitting(true)

    try {
      // 收集表单数据
      const data: Record<string, any> = {}
      formDataObj.forEach((value, key) => {
        if (data[key]) {
          // 处理多选情况
          if (Array.isArray(data[key])) {
            data[key].push(value)
          } else {
            data[key] = [data[key], value]
          }
        } else {
          data[key] = value
        }
      })

      // 调用回调函数
      if (onFormSubmit) {
        onFormSubmit(formDataObj, form)
      }

      // 显示提交成功消息
      toast.success('表单提交成功！', {
        description: `成功提交了 ${Object.keys(data).length} 个字段的数据`
      })
    } catch (error) {
      console.error('表单提交失败:', error)
      toast.error('表单提交失败', {
        description: '请重试'
      })
    } finally {
      setIsSubmitting(false)
    }
  }, [onFormSubmit, validateForm, setValidationErrors, setIsSubmitting])

  // 处理输入变化
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name } = event.target

    // 清除该字段的验证错误
    if (name && validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }, [validationErrors, setValidationErrors])

  // 清理和解析HTML内容
  const parseOptions: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode instanceof Element && domNode.name) {
        const { name, attribs, children } = domNode

        // 处理表单元素
        if (name === 'form') {
          return (
            <Card className="w-full max-w-lg mx-auto mt-6 shadow-lg border-0 bg-gradient-to-br from-white to-slate-50/50">
              <CardHeader className="pb-6 pt-8 px-8">
                <CardTitle className="text-xl font-semibold text-slate-800 text-center">
                  {attribs?.title || '表单'}
                </CardTitle>
                <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mt-3"></div>
              </CardHeader>
              <CardContent className="px-8 pb-8">
                <form
                  {...attribs}
                  onSubmit={handleFormSubmit}
                  className={cn("space-y-6", attribs?.className)}
                >
                  {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
                </form>
              </CardContent>
            </Card>
          )
        }

        // 处理输入框
        if (name === 'input') {
          const { type = 'text', name: fieldName, required, ...restProps } = attribs || {}
          const hasError = fieldName && validationErrors[fieldName]
          const isRequired = required === 'true' || required === ''

          if (type === 'submit' || type === 'button') {
            return (
              <div className="">
                <Button
                  type={type as 'submit' | 'button'}
                  disabled={isSubmitting}
                  className="w-full h-9 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed"
                  {...restProps}
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <ModernLoader size={16} />
                      提交中...
                    </div>
                  ) : (
                    attribs?.value || '提交表单'
                  )}
                </Button>
              </div>
            )
          }

          if (type === 'checkbox' || type === 'radio') {
            return (
              <input
                {...restProps}
                type={type}
                name={fieldName}
                required={isRequired}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 bg-background border-2 border-border rounded focus:ring-2 focus:ring-blue-500/20 transition-colors"
              />
            )
          }

          // 处理日期相关的输入类型
          if (type === 'date' || type === 'datetime-local' || type === 'datetime' || type === 'time' || type === 'month' || type === 'week') {
            // 对于datetime-local和datetime类型，优先使用自定义组件
            if ((type === 'datetime-local' || type === 'datetime') && useCustomDateTimePicker) {
              return (
                <div className="space-y-3">
                  <DateTimePicker
                    value={restProps.value as string}
                    onChange={(value) => {
                      // 创建一个模拟的事件对象来触发handleInputChange
                      const mockEvent = {
                        target: {
                          name: fieldName,
                          value: value
                        }
                      } as React.ChangeEvent<HTMLInputElement>
                      handleInputChange(mockEvent)
                    }}
                    name={fieldName}
                    required={isRequired}
                    placeholder="选择日期和时间"
                    className={hasError ? "border-red-500" : ""}
                  />
                  {hasError && (
                    <p className="text-sm text-red-500 mt-1">{validationErrors[fieldName]}</p>
                  )}
                </div>
              )
            }

            // 使用原生input以保持日期选择器功能
            return (
              <div className="space-y-3">
                <input
                  {...restProps}
                  type={type}
                  name={fieldName}
                  required={isRequired}
                  onChange={handleInputChange}
                  onBlur={handleInputChange}
                  onClick={(e) => {
                    // 确保日期选择器能正常打开
                    const input = e.target as HTMLInputElement;
                    if (input.showPicker) {
                      input.showPicker();
                    }
                  }}
                  className={cn(
                    "flex h-11 w-full rounded-lg border border-slate-300 bg-white px-4 py-3 text-sm shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50",
                    "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
                    "[&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-100 [&::-webkit-calendar-picker-indicator]:hover:bg-gray-100",
                    "[&::-webkit-datetime-edit]:focus:outline-none [&::-webkit-datetime-edit-fields-wrapper]:focus:outline-none",
                    hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
                  )}
                />
                {hasError && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors[fieldName]}</p>
                )}
                {(type === 'datetime-local' || type === 'datetime') && !useCustomDateTimePicker && (
                  <p className="text-xs text-muted-foreground">
                    提示：选择日期和时间后，点击输入框外的区域或按回车键确认
                  </p>
                )}
              </div>
            )
          }

          return (
            <div className="space-y-3">
              <Input
                {...restProps}
                type={type}
                name={fieldName}
                required={isRequired}
                onChange={handleInputChange}
                className={cn(
                  "h-11 px-4 py-3 rounded-lg border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-colors shadow-sm",
                  hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
                )}
              />
              {hasError && (
                <p className="text-sm text-red-500 mt-1">{validationErrors[fieldName]}</p>
              )}
            </div>
          )
        }

        // 处理文本域
        if (name === 'textarea') {
          const { name: fieldName, required, ...restProps } = attribs || {}
          const hasError = fieldName && validationErrors[fieldName]
          const isRequired = required === 'true' || required === ''

          return (
            <div className="space-y-3">
              <Textarea
                {...restProps}
                name={fieldName}
                required={isRequired}
                onChange={handleInputChange}
                className={cn(
                  "min-h-[100px] resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-colors",
                  hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
                )}
                rows={4}
                defaultValue={children?.map(child => child.toString()).join('') || ''}
              />
              {hasError && (
                <p className="text-sm text-red-500 mt-1">{validationErrors[fieldName]}</p>
              )}
            </div>
          )
        }

        // 处理选择框
        if (name === 'select') {
          const { name: fieldName, required, ...restProps } = attribs || {}
          const hasError = fieldName && validationErrors[fieldName]
          const isRequired = required === 'true' || required === ''

          return (
            <div className="space-y-3">
              <select
                {...restProps}
                name={fieldName}
                required={isRequired}
                onChange={handleInputChange}
                className={cn(
                  "flex h-11 w-full rounded-lg border border-slate-300 bg-white px-4 py-3 text-sm shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-50 appearance-none bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzY0NzQ4QiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+')] bg-no-repeat bg-[right_12px_center] pr-10",
                  hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20"
                )}
              >
                {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
              </select>
              {hasError && (
                <p className="text-sm text-red-500 mt-1">{validationErrors[fieldName]}</p>
              )}
            </div>
          )
        }

        // 处理按钮
        if (name === 'button') {
          const buttonType = (attribs?.type as 'button' | 'submit' | 'reset') || 'button'

          if (buttonType === 'submit') {
            return (
              <div className="">
                <Button
                  {...attribs}
                  type={buttonType}
                  disabled={isSubmitting}
                  className="w-full h-9 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <ModernLoader size={16} />
                      提交中...
                    </div>
                  ) : (
                    parse(children?.map(child => child.toString()).join('') || '提交表单', parseOptions)
                  )}
                </Button>
              </div>
            )
          }

          return (
            <Button
              {...attribs}
              type={buttonType}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 border-0 shadow-sm hover:shadow-md cursor-pointer"
            >
              {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
            </Button>
          )
        }

        // 处理标签
        if (name === 'label') {
          const labelText = children?.map(child => child.toString()).join('') || ''
          const forField = attribs?.for
          const isRequired = forField && document.querySelector(`[name="${forField}"]`)?.hasAttribute('required')

          return (
            <Label
              {...attribs}
              className={cn("text-sm font-medium text-slate-700 flex items-center gap-1", attribs?.className)}
            >
              {labelText}
              {isRequired && (
                <span className="text-red-500 text-base leading-none">*</span>
              )}
            </Label>
          )
        }

        // 处理字段集
        if (name === 'fieldset') {
          return (
            <fieldset
              {...attribs}
              className={cn("border border-border rounded-lg p-4 my-4 bg-muted/30", attribs?.className)}
            >
              {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
            </fieldset>
          )
        }

        // 处理图例
        if (name === 'legend') {
          return (
            <legend
              {...attribs}
              className={cn("text-base font-semibold text-foreground px-3 bg-background", attribs?.className)}
            >
              {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
            </legend>
          )
        }

        // 处理div容器
        if (name === 'div') {
          return (
            <div
              {...attribs}
              className={cn("space-y-3", attribs?.className)}
            >
              {parse(children?.map(child => child.toString()).join('') || '', parseOptions)}
            </div>
          )
        }
      }
    }
  }

  // 清理HTML内容，防止XSS攻击
  const cleanHTML = DOMPurify.sanitize(htmlContent, {
    ALLOWED_TAGS: [
      'form', 'input', 'textarea', 'select', 'option', 'button', 'label', 
      'fieldset', 'legend', 'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'br', 'hr', 'strong', 'em', 'b', 'i', 'u', 'small'
    ],
    ALLOWED_ATTR: [
      'type', 'name', 'value', 'placeholder', 'required', 'disabled', 'readonly',
      'checked', 'selected', 'multiple', 'size', 'maxlength', 'minlength',
      'min', 'max', 'step', 'pattern', 'title', 'id', 'class', 'style',
      'for', 'action', 'method', 'enctype', 'autocomplete', 'rows', 'cols'
    ],
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false
  })

  return (
    <div className={cn("my-1 w-full", className)}>
      {parse(cleanHTML, parseOptions)}
    </div>
  )
}

export default HTMLFormRenderer
