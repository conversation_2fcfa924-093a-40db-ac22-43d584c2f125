"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import rehypeRaw from "rehype-raw"
import { ChevronDown, ChevronUp } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { cn } from "@/lib/utils"

interface ThinkingProcessProps {
  content: string
  title?: string
  icon?: React.ReactNode
  defaultOpen?: boolean
  variant?: "default" | "google" | "deepseek"
}

// 处理思考内容中可能嵌套的标签
function cleanThinkingContent(content: string): string {
  // 移除可能嵌套的thinking标签
  return content
    .replace(/<\/?thinking>/g, '')
    .replace(/<\/?思考>/g, '')
    .replace(/<\/?Thinking>/g, '')
    .replace(/```thinking/g, '')
    .replace(/```$/g, '')
    .trim();
}

export default function ThinkingProcess({
  content,
  title = "思考过程",
  icon,
  defaultOpen = false,
  variant = "default"
}: ThinkingProcessProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const thinkingContentRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastScrollTimeRef = useRef<number>(0)

  // 清理思考内容
  const cleanedContent = cleanThinkingContent(content)

  // 优化的滚动函数，添加节流机制减少滚动频率
  const scrollToBottom = useCallback(() => {
    const now = Date.now();
    const timeSinceLastScroll = now - lastScrollTimeRef.current;

    // 节流：至少间隔200ms才执行一次滚动
    if (timeSinceLastScroll < 200) {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        scrollToBottom();
      }, 200 - timeSinceLastScroll);
      return;
    }

    lastScrollTimeRef.current = now;

    requestAnimationFrame(() => {
      if (isOpen && thinkingContentRef.current) {
        const element = thinkingContentRef.current;
        if (element.scrollHeight > element.clientHeight) {
          element.scrollTop = element.scrollHeight;
        }
      }
    });
  }, [isOpen]);

  // 当思考内容更新时，自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [content, isOpen, scrollToBottom])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 根据不同变体返回不同样式
  const getVariantStyles = () => {
    switch (variant) {
      case "google":
        return {
          container: "rounded-lg border border-[#dadce0] overflow-hidden",
          header: "flex items-center justify-between px-4 py-3 bg-[#f8f9fa]",
          headerIcon: "text-[#4285f4]",
          headerText: "text-sm font-medium text-[#202124]",
          content: "p-4 text-sm text-[#3c4043] bg-white overflow-auto max-h-[240px] scrollbar-thin"
        }
      case "deepseek":
        return {
          container: "rounded-xl border border-[#dadce0] overflow-hidden",
          header: "flex items-center justify-between px-4 py-3 bg-gradient-to-r from-[#f0f0f5] to-[#f8f8fc]",
          headerIcon: "text-[#6366f1]",
          headerText: "text-sm font-medium text-[#374151]",
          content: "p-4 text-sm text-[#4b5563] bg-white overflow-auto max-h-[240px] scrollbar-thin"
        }
      default:
        return {
          container: "rounded-md border border-muted overflow-hidden bg-muted/20 my-4",
          header: "flex items-center justify-between px-4 py-2 bg-muted/30 border-b",
          headerIcon: "",
          headerText: "text-sm font-medium",
          content: "p-4 text-sm text-muted-foreground overflow-auto max-h-[240px] scrollbar-thin"
        }
    }
  }

  const styles = getVariantStyles()

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={styles.container}
    >
      <div className={styles.header}>
        <div className="flex items-center gap-2">
          {icon && <span className={cn("flex-shrink-0", styles.headerIcon)}>{icon}</span>}
          <span className={styles.headerText}>{title}</span>
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-transparent">
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
            <span className="sr-only">切换思考过程</span>
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent>
        <div ref={thinkingContentRef} className={styles.content}>
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {cleanedContent}
          </ReactMarkdown>
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
