import React from 'react';
import { cn } from "@/lib/utils";
import { renderIcon, IconName } from "@/config/iconMap";
import { recordToolClick } from '@/lib/chatService';
import { ChevronRight } from 'lucide-react';

export interface FeatureCardProps {
  id: string;
  title: string;
  description: string;
  message: string;
  icon: IconName;
  colorClass: string;
  iconColorClass: string;
  onSelect: (message: string) => void;
  hasSubExamples?: boolean;
}

/**
 * A component that renders a feature card with an icon, title and description
 * Responsive design for both mobile and desktop views
 */
export function FeatureCard({
  id,
  title,
  description,
  message,
  icon,
  colorClass,
  iconColorClass,
  onSelect,
  hasSubExamples = false
}: FeatureCardProps) {
  // Check if we're on mobile
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  const handleClick = async () => {
    try {
      // 获取用户信息
      let userId = '';
      try {
        const userInfoJson = localStorage.getItem('wechat_work_user');
        if (userInfoJson) {
          const userInfo = JSON.parse(userInfoJson);
          userId = userInfo.userId;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }

      // 记录功能卡片点击
      if (userId) {
        // 确定位置信息
        const toolType = '聊天界面-功能卡片';

        // 记录工具点击
        await recordToolClick(userId, title, toolType);
      }
    } catch (error) {
      console.error('记录功能卡片点击失败:', error);
    } finally {
      // 无论记录是否成功，都执行原有的onSelect功能
      // 尝试使用全局函数发送消息
      if (typeof (window as any).__handleFeatureCardClick === 'function') {
        (window as any).__handleFeatureCardClick(message);
      }
      // 如果全局函数不存在，则使用 onSelect 函数
      else if (typeof onSelect === 'function') {
        onSelect(message);
      } else {
        console.error('onSelect 函数未定义或不是函数');
      }
    }
  };

  return (
    <button
      className="flex items-start p-2.5 md:p-4 rounded-lg border border-muted/40 bg-white/50 dark:bg-slate-900/50 hover:bg-muted/20 transition-colors text-left shadow-sm h-full w-full"
      onClick={handleClick}
    >
      {/* 移动端显示较小的图标和文字 */}
      <div className="md:hidden flex flex-1">
        <div className={cn("mr-1.5 mt-0.5 p-1.5 rounded-md flex-shrink-0", colorClass)}>
          {renderIcon(icon, { className: cn("h-3.5 w-3.5", iconColorClass) })}
        </div>
        <div className="overflow-hidden flex-1 min-w-0">
          <h4 className="font-medium text-xs truncate">{title}</h4>
          <p className="text-[10px] text-muted-foreground mt-0.5 line-clamp-2 leading-tight">{description}</p>
        </div>
        {hasSubExamples && (
          <div className="flex items-center ml-1">
            <ChevronRight className="h-3 w-3 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* PC端显示原始大小的图标和文字 */}
      <div className="hidden md:flex w-full">
        <div className={cn("mr-4 mt-1 p-2 rounded-md", colorClass)}>
          {renderIcon(icon, { className: cn("h-5 w-5", iconColorClass) })}
        </div>
        <div className="overflow-hidden flex-1">
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        </div>
        {hasSubExamples && (
          <div className="flex items-center ml-2">
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        )}
      </div>
    </button>
  );
}