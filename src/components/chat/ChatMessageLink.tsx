"use client"

import React, { useMemo } from 'react'
import { ExternalBrowserLink } from '@/components/wechat/ExternalBrowserLink'
import wechatJSSDK from '@/lib/wechatJSSDK'
import { ExternalLink } from 'lucide-react'

// URL正则表达式 - 匹配常见URL格式
const URL_REGEX = /(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/g

interface ChatMessageLinkProps {
  content: string
  className?: string
  externalLinkClassName?: string
}

/**
 * 聊天消息URL链接处理组件
 *
 * 解析消息文本中的URL并转换为使用外部浏览器打开的链接
 */
export function ChatMessageLink({
  content,
  className = '',
  externalLinkClassName = 'text-blue-600 hover:underline flex items-center gap-1 break-all'
}: ChatMessageLinkProps) {
  // 解析并处理文本中的URL
  const processedContent = useMemo(() => {
    if (!content) return []

    // 找出所有URL
    const matches = content.match(URL_REGEX)

    // 如果没有URL，直接返回原始文本
    if (!matches) return [{ type: 'text', content }]

    // 切分文本，分离URL和普通文本
    const parts: { type: 'text' | 'url', content: string }[] = []
    let lastIndex = 0

    matches.forEach(url => {
      const index = content.indexOf(url, lastIndex)

      // 添加URL前的文本
      if (index > lastIndex) {
        parts.push({
          type: 'text',
          content: content.substring(lastIndex, index)
        })
      }

      // 添加URL
      if (wechatJSSDK.isValidUrl(url)) {
        parts.push({
          type: 'url',
          content: url
        })
      } else {
        // 无效的URL作为普通文本处理
        parts.push({
          type: 'text',
          content: url
        })
      }

      lastIndex = index + url.length
    })

    // 添加最后一段文本
    if (lastIndex < content.length) {
      parts.push({
        type: 'text',
        content: content.substring(lastIndex)
      })
    }

    return parts
  }, [content])

  // 如果没有内容，返回空
  if (!processedContent.length) return null

  return (
    <span className={className}>
      {processedContent.map((part, index) => (
        <React.Fragment key={index}>
          {part.type === 'url' ? (
            <ExternalBrowserLink
              href={part.content}
              className={externalLinkClassName}
              fallback={true}
            >
              <span>{part.content}</span>
              <ExternalLink className="h-3.5 w-3.5 inline-block flex-shrink-0" />
            </ExternalBrowserLink>
          ) : (
            <span>{part.content}</span>
          )}
        </React.Fragment>
      ))}
    </span>
  )
}

/**
 * 简化版URL解析组件
 * 只识别单个URL并转换为外部链接
 */
export function SimpleExternalLink({
  url,
  className = '',
  children
}: {
  url: string
  className?: string
  children?: React.ReactNode
}) {
  // 验证URL
  if (!wechatJSSDK.isValidUrl(url)) {
    return <span>{children || url}</span>
  }

  return (
    <ExternalBrowserLink
      href={url}
      className={className || 'text-blue-600 hover:underline flex items-center gap-1 break-all'}
      fallback={true}
    >
      {children || url}
      <ExternalLink className="h-3.5 w-3.5 inline-block flex-shrink-0" />
    </ExternalBrowserLink>
  )
}

export default ChatMessageLink