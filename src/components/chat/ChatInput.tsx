"use client"

import { useState, useRef, ChangeEvent, KeyboardEvent, useEffect, useMemo, DragEvent, ClipboardEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  PaperclipIcon,
  SendIcon,
  ImageIcon,
  Image,
  Languages,
  Globe,
  BookUser,
  Plus,
  CheckSquare,
  ClipboardCheck,
  GitBranch,
  Database,
  Newspaper,
  HelpCircle,
  Loader2,
  Search,
  Globe2,
  Square
} from "lucide-react"
import { nanoid } from "nanoid"
import { cn } from "@/lib/utils"
import { uploadFile, UploadFileResponse } from "@/lib/api"
import { toast } from 'sonner'
import { recordToolClick } from '@/lib/chatService'

// 导入拖拽排序相关的库
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  horizontalListSortingStrategy
} from '@dnd-kit/sortable'
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers'
import { CSS } from '@dnd-kit/utilities'
import ocrFunctionButtons from "@/config/ocrFunctionButtons"
import fileFunctionButtons from "@/config/fileFunctionButtons"

// 定义功能按钮类型
interface FunctionButton {
  id: string;
  name: string;
  prefix: string;
  icon: React.ReactNode;
  description?: string; // 命令的简短描述
}

// 自定义文件类型，包含上传状态和URL
interface UploadFile {
  id: string;
  file: File;
  url: string; // 用于显示的URL
  imageId?: string; // 图片ID，用于发送给API
  uploading: boolean;
  error: boolean;
}

interface ChatInputProps {
  onSendMessage: (message: string, fileUrls?: {imageUrls?: string[], imageIds?: string[], fileUrls?: string[], fileNames?: string[]}, modelTypeCalled?: string) => void
  onNewConversation?: () => void // 添加创建新会话的回调函数
  isDisabled?: boolean // 将isStreamingResponse改为isDisabled
  onCreateAttachment?: (file: File) => Promise<any> // 添加创建附件的回调函数
  selectedModel?: string // 添加选择的模型类型
  onModelChange?: (model: string) => void // 添加模型改变的回调
  onStopStreaming?: () => void // 添加停止流式响应的回调
}

export default function ChatInput({ 
  onSendMessage, 
  onNewConversation, 
  isDisabled = false, 
  onCreateAttachment,
  selectedModel = 'speed',
  onModelChange,
  onStopStreaming
}: ChatInputProps) {
  const [message, setMessage] = useState("")
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [selectedFunction, setSelectedFunction] = useState<string | null>(null)
  // 移除内部的modelTypeCalled状态，使用从props传入的selectedModel
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [showCommands, setShowCommands] = useState(false)
  const [commandFilter, setCommandFilter] = useState("")
  const [selectedCommandIndex, setSelectedCommandIndex] = useState(0)
  const commandsRef = useRef<HTMLDivElement>(null)
  const chatInputRef = useRef<HTMLDivElement>(null)
  const [isDraggingOver, setIsDraggingOver] = useState(false)
  const [showOcrButtons, setShowOcrButtons] = useState(false)
  const [showFileButtons, setShowFileButtons] = useState(false)

  // 定义默认功能按钮列表
  const defaultFunctionButtons: FunctionButton[] = [
    // {
    //   id: "help",
    //   name: "帮助",
    //   prefix: "/帮助 ",
    //   icon: <HelpCircle className="h-4 w-4 mr-1" />,
    //   description: "显示帮助信息和可用命令"
    // },
    {
      id: "news",
      name: "新闻",
      prefix: "/新闻 ",
      icon: <Newspaper className="h-4 w-4 mr-1" />,
      description: "请输入需要查询的新闻关键词，点击发送即可查询"
    },
    {
      id: "contacts",
      name: "通讯录",
      prefix: "/通讯录 ",
      icon: <BookUser className="h-4 w-4 mr-1" />,
      description: "请输入需要查询的内部员工姓名或工号，点击发送即可查询"
    },
    {
      id: "knowledge-base",
      name: "知识库",
      prefix: "/知识库 ",
      icon: <Database className="h-4 w-4 mr-1" />,
      description: "请输入需要搜索的内部知识关键词，点击发送即可搜索"
    },
    {
      id: "todo",
      name: "待办流程",
      prefix: "/待办流程 ",
      icon: <CheckSquare className="h-4 w-4 mr-1" />,
      description: ""
    },
    {
      id: "done",
      name: "已办流程",
      prefix: "/已办流程 ",
      icon: <ClipboardCheck className="h-4 w-4 mr-1" />,
      description: ""
    },
    {
      id: "web-search",
      name: "网络搜索",
      prefix: "/搜索 ",
      icon: <Globe2 className="h-4 w-4 mr-1" />,
      description: "请输入需要联网搜索的关键词，点击发送即可搜索"
    },
    {
      id: "zh-to-en",
      name: "中译英",
      prefix: "/中译英 ",
      icon: <Languages className="h-4 w-4 mr-1" />,
      description: "请输入需要翻译的中文内容，点击发送即可翻译"
    },
    {
      id: "en-to-zh",
      name: "英译中",
      prefix: "/英译中 ",
      icon: <Globe className="h-4 w-4 mr-1" />,
      description: "请输入需要翻译的英文内容，点击发送即可翻译"
    },
    {
      id: "payment-report",
      name: "保费快报",
      prefix: "/保费快报 ",
      icon: <Newspaper className="h-4 w-4 mr-1" />,
      description: "请输入需要查询的保费快报关键词，点击发送即可查询"
    },
    {
      id: "image-generation",
      name: "图像生成",
      prefix: "/图像生成 ",
      icon: <Image className="h-4 w-4 mr-1" />,
      description: "请输入需要生成的图像描述，点击发送即可生成"
    }
  ]

  // 从本地存储加载按钮排序
  const [functionButtons, setFunctionButtons] = useState<FunctionButton[]>(() => {
    // 清除本地存储中的排序，使用默认排序
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('functionButtonsOrder');
    }

    // 使用默认排序（按照defaultFunctionButtons中的元素下标顺序）
    return [...defaultFunctionButtons];
  });

  // 当按钮排序变化时保存到本地存储
  useEffect(() => {
    if (typeof localStorage !== 'undefined') {
      const orderIds = functionButtons.map(btn => btn.id);
      localStorage.setItem('functionButtonsOrder', JSON.stringify(orderIds));
    }
  }, [functionButtons]);

  // 更新 showOcrButtons 状态基于 files 状态
  useEffect(() => {
    const hasImageAttachment = files.some(f =>
      f.file.type.startsWith('image/') && !f.error && !f.uploading
    );
    setShowOcrButtons(hasImageAttachment);
  }, [files]);

  // 更新 showFileButtons 状态基于 files 状态
  useEffect(() => {
    const hasFileAttachment = files.some(f =>
      !f.file.type.startsWith('image/') && !f.error && !f.uploading
    );
    setShowFileButtons(hasFileAttachment);
  }, [files]);

  // 根据上传的文件状态确定显示哪种功能按钮
  const currentFunctionButtons = useMemo(() => {
    // 优先级：图片功能按钮 > 文件功能按钮 > 默认按钮
    if (showOcrButtons) {
      return ocrFunctionButtons;
    } else if (showFileButtons) {
      return fileFunctionButtons;
    } else {
      return functionButtons;
    }
  }, [showOcrButtons, showFileButtons, ocrFunctionButtons, fileFunctionButtons, functionButtons]);

  // 监听文件变化，当文件列表变为空时恢复默认功能按钮
  useEffect(() => {
    if (!showOcrButtons) {
      // 如果此时有选中的OCR功能按钮，清除选择
      if (selectedFunction &&
          ["ocr-recognition", "image-summary", "image-analysis", "image-extraction"]
            .includes(selectedFunction)) {
        setSelectedFunction(null);
      }
    }
  }, [showOcrButtons, selectedFunction]);

  // 监听文件功能按钮变化
  useEffect(() => {
    if (!showFileButtons) {
      // 如果此时有选中的文件功能按钮，清除选择
      if (selectedFunction &&
          ["data-qa", "doc-summary", "content-extraction"]
            .includes(selectedFunction)) {
        setSelectedFunction(null);
      }
    }
  }, [showFileButtons, selectedFunction]);

  // 获取过滤后的命令列表
  const getFilteredCommands = () => {
    // 使用当前功能按钮列表
    const buttonsToFilter = currentFunctionButtons;

    // 如果没有过滤字符串，返回所有命令
    if (!commandFilter) {
      return buttonsToFilter;
    }

    // 过滤命令
    return buttonsToFilter.filter(cmd =>
      cmd.name.toLowerCase().includes(commandFilter.toLowerCase()) ||
      (cmd.description && cmd.description.toLowerCase().includes(commandFilter.toLowerCase()))
    );
  }

  // 更新handleCommandSelect函数以处理OCR命令
  const handleCommandSelect = (command: FunctionButton) => {
    setSelectedFunction(command.id);
    setShowCommands(false);
    
    // 在文本框中添加命令前缀
    if (command.prefix) {
      // 如果消息框有内容，将前缀添加到消息最前面并用空格分割
      if (message.trim()) {
        setMessage(command.prefix + message);
      } else {
        // 如果消息框没有内容，直接设置前缀
        setMessage(command.prefix);
      }
    }
    
    // 延迟聚焦到文本框
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        
        // 将光标放在文本的最后
        const length = textareaRef.current.value.length;
        textareaRef.current.setSelectionRange(length, length);
      }
    }, 0);
  }

  // 处理消息输入变化
  const handleMessageChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    // 设置消息内容
    const newMessage = e.target.value;
    setMessage(newMessage);

    // 如果消息以斜杠开头，且不是在选择命令状态下，显示命令选择菜单
    if (newMessage.startsWith('/') && !showCommands) {
      setShowCommands(true);
      setCommandFilter(newMessage.slice(1)); // 去掉斜杠作为过滤条件
      setSelectedCommandIndex(0); // 重置选中的命令索引
    } else if (showCommands) {
      if (newMessage.startsWith('/')) {
        // 更新命令过滤
        setCommandFilter(newMessage.slice(1));
        setSelectedCommandIndex(0);
      } else {
        // 不再以斜杠开头，关闭命令菜单
        setShowCommands(false);
      }
    }

    // 通过JS更新文本框高度
    adjustTextareaHeight();
  }

  // 处理功能按钮选择
  const handleFunctionSelect = async (id: string) => {
    // 检查是否是需要立即发送的功能按钮（待办流程和已办流程）
    const needsSendImmediately = ["todo", "done"].includes(id);
    const isOcrButton = ["ocr-recognition", "image-summary", "image-analysis", "image-extraction"].includes(id);
    
    // 检查是否点击了当前已选中的按钮
    if (selectedFunction === id) {
      // 取消选中当前按钮
      setSelectedFunction(null);
      
      // 清空消息
      setMessage("");
      
      // 聚焦到文本域
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
        }
      }, 100);
      
      return; // 提前返回，避免执行后续逻辑
    }
    
    // 检查是否从另一个功能按钮切换过来
    const isSwitchingBetweenButtons = selectedFunction !== null && selectedFunction !== id;
    
    // 设置选中的功能按钮ID - 立即更新UI
    setSelectedFunction(id);
    
    // 如果是在功能按钮间切换，先清空消息内容
    if (isSwitchingBetweenButtons) {
      setMessage("");
    }
    
    // 如果是待办流程或已办流程，添加前缀并立即发送
    if (needsSendImmediately && !showOcrButtons) {
      // 找到对应按钮
      const button = functionButtons.find(btn => btn.id === id);
      if (button && button.prefix) {
        // 设置消息为按钮前缀
        setMessage(button.prefix.trim());
        
        // 确保状态更新后再发送消息
        // 使用更可靠的方式来确保状态已更新后发送消息
        setTimeout(() => {
          // 直接调用发送函数，不再尝试等待状态更新
          const prefix = button.prefix.trim();
          console.log(`立即发送消息: ${prefix}`);
          
          // 直接调用onSendMessage，不再通过handleSendMessage
          onSendMessage(prefix, undefined, selectedModel);
          
          // 发送后重置状态
          setMessage("");
          setSelectedFunction(null);
        }, 10);
      }
    } 
    // 如果是OCR按钮，设置对应的OCR指令
    else if (isOcrButton && showOcrButtons) {
      // 找到对应按钮
      const button = ocrFunctionButtons.find(btn => btn.id === id);
      if (button) {
        // 获取按钮的描述作为OCR指令
        setMessage(button.description);
        
        // 不自动发送，让用户可以编辑消息
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.focus();
          }
        }, 100);
      }
    }
    else {
      // 检查是否是文件功能按钮
      const isFileButton = ["data-qa", "doc-summary", "content-extraction"].includes(id);

      if (isFileButton && showFileButtons) {
        // 找到对应的文件功能按钮
        const button = fileFunctionButtons.find(btn => btn.id === id);
        if (button) {
          // 获取按钮的描述作为文件处理指令
          setMessage(button.description || "");

          // 不自动发送，让用户可以编辑消息
          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.focus();
            }
          }, 100);
        }
      } else {
        // 添加前缀到消息
        const button = showOcrButtons
          ? ocrFunctionButtons.find(btn => btn.id === id)
          : functionButtons.find(btn => btn.id === id);

        if (button) {
          // 如果是功能按钮间切换，直接设置新前缀
          // 否则保留已有内容并添加前缀
          if (isSwitchingBetweenButtons || !message.trim()) {
            setMessage(button.prefix);
          } else {
            setMessage(button.prefix + message);
          }
        }

        // 聚焦到文本域
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.focus();
          }
        }, 100);
      }
    }

    // 异步记录功能按钮点击，不阻塞UI更新
    (async () => {
      try {
        // 获取用户信息
        let userId = '';
        try {
          const userInfoJson = localStorage.getItem('wechat_work_user');
          if (userInfoJson) {
            const userInfo = JSON.parse(userInfoJson);
            userId = userInfo.userId;
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
        }

        // 记录功能按钮点击
        if (userId) {
          // 确定工具名称
          let toolName = id;
          if (showOcrButtons) {
            toolName = ocrFunctionButtons.find(btn => btn.id === id)?.name || id;
          } else if (showFileButtons) {
            toolName = fileFunctionButtons.find(btn => btn.id === id)?.name || id;
          } else {
            toolName = functionButtons.find(btn => btn.id === id)?.name || id;
          }
            
          // 确定位置信息
          const toolType = '聊天界面-功能按钮';
          
          // 记录工具点击
          await recordToolClick(userId, toolName, toolType);
        }
      } catch (error) {
        console.error('记录功能按钮点击失败:', error);
      }
    })();
  };

  // 上传文件到服务器
  const uploadFileToServer = async (file: File): Promise<UploadFileResponse> => {
    try {
      const response = await uploadFile(file);
      return response;
    } catch (error) {
      console.error('上传文件失败:', error);
      toast.error('文件上传失败，请重试');
      return { displayUrl: '' };
    }
  }

  // 处理文件上传的通用函数
  const handleFilesUpload = async (files: File[]) => {
    if (files.length === 0) return;

    const newFiles = files.map(file => ({
      id: nanoid(),
      file,
      url: '',
      uploading: true,
      error: false
    }));

    setFiles(prev => [...prev, ...newFiles]);
    setIsUploading(true);

    // 确保焦点回到输入框的函数
    const focusTextarea = () => {
      if (textareaRef.current) {
        // 使用setTimeout确保在DOM更新后再聚焦
        setTimeout(() => {
          try {
            textareaRef.current?.focus();
            console.log('已将焦点返回到输入框');

            // 在移动设备上，尝试滚动到输入框位置
            if (window.innerWidth < 768) {
              textareaRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          } catch (err) {
            console.error('聚焦输入框失败:', err);
          }
        }, 100);
      }
    };

    // 对每个文件进行上传
    for (const fileItem of newFiles) {
      try {
        const response = await uploadFileToServer(fileItem.file);

        setFiles(prev => prev.map(f =>
          f.id === fileItem.id
            ? { 
                ...f, 
                url: response.displayUrl, 
                imageId: response.imageId,
                uploading: false, 
                error: response.displayUrl === '' 
              }
            : f
        ));

        // 每个文件上传成功后都尝试聚焦
        focusTextarea();
      } catch (error) {
        setFiles(prev => prev.map(f =>
          f.id === fileItem.id
            ? { ...f, uploading: false, error: true }
            : f
        ));
      }
    }

    setIsUploading(false);

    // 所有文件上传完成后再次尝试聚焦
    focusTextarea();
  };

  // 处理文件选择
  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      await handleFilesUpload(Array.from(e.target.files));

      // 清空文件输入，以便可以再次选择同一文件
      e.target.value = '';
    }
  }

  // 打开文件选择器
  const handleOpenFileSelect = () => {
    // 在打开选择器前先保存当前焦点状态
    const activeElement = document.activeElement;

    fileInputRef.current?.click();

    // 使用setTimeout确保在选择器关闭后恢复焦点
    setTimeout(() => {
      if (textareaRef.current && activeElement === textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 100);
  }

  // 打开图片选择器
  const handleOpenImageSelect = () => {
    // 在打开选择器前先保存当前焦点状态
    const activeElement = document.activeElement;

    imageInputRef.current?.click();

    // 使用setTimeout确保在选择器关闭后恢复焦点
    setTimeout(() => {
      if (textareaRef.current && activeElement === textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 100);
  }

  // 移除已选择的文件
  const handleRemoveFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));

    // 移除文件后将焦点返回到输入框
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 50);
  }

  // 处理发送消息
  const handleSendMessage = () => {
    if (isDisabled) return; // 如果禁用了，则不执行任何操作

    // 如果命令菜单打开，先关闭命令菜单
    if (showCommands) {
      setShowCommands(false);
    }

    // 记录设备类型
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
    console.log(`发送消息 - 设备类型: ${isMobile ? '移动设备' : '桌面设备'}`);

    // 获取选中功能的ID
    const selectedButtonId = selectedFunction;

    // 检查是否是允许空消息的功能按钮
    const allowEmptyMessage = ["help", "news", "todo", "done"].includes(selectedButtonId || "");

    // 检查是否有任何文件正在上传
    const hasUploadingFiles = files.some(f => f.uploading);

    // 过滤出成功上传的文件URL
    const successfulImageUrls = files
      .filter(f => !f.error && !f.uploading && f.file.type.startsWith('image/'))
      .map(f => f.url);
      
    // 过滤出成功上传的图片ID（用于发送给API）
    const successfulImageIds = files
      .filter(f => !f.error && !f.uploading && f.file.type.startsWith('image/') && f.imageId)
      .map(f => f.imageId!);

    const successfulFileUrls = files
      .filter(f => !f.error && !f.uploading && !f.file.type.startsWith('image/'))
      .map(f => f.url);

    const successfulFileNames = files
      .filter(f => !f.error && !f.uploading && !f.file.type.startsWith('image/'))
      .map(f => f.file.name);

    // 只有当没有文件正在上传时才允许发送
    if (!hasUploadingFiles &&
        ((allowEmptyMessage && selectedButtonId) || message.trim() || successfulImageUrls.length > 0 || successfulFileUrls.length > 0)) {

      try {
        console.log('开始准备发送消息...');

        // 获取选中功能的前缀
        let prefix = "";
        if (selectedFunction) {
          // 检查是否是OCR按钮
          const isOcrButton = ["ocr-recognition", "image-summary", "image-analysis", "image-extraction"].includes(selectedFunction);
          // 检查是否是文件功能按钮
          const isFileButton = ["data-qa", "doc-summary", "content-extraction"].includes(selectedFunction);

          if (isOcrButton && showOcrButtons) {
            // 使用OCR按钮的前缀
            prefix = ocrFunctionButtons.find(btn => btn.id === selectedFunction)?.prefix || "";
          } else if (isFileButton && showFileButtons) {
            // 使用文件功能按钮的前缀
            prefix = fileFunctionButtons.find(btn => btn.id === selectedFunction)?.prefix || "";
          } else {
            // 使用普通功能按钮的前缀
            prefix = functionButtons.find(btn => btn.id === selectedFunction)?.prefix || "";
          }
        }

        // 拼接消息内容
        let finalMessage = message; // 默认最终消息是当前输入框的内容

        // 如果有选中的功能，并且该功能定义了前缀
        if (selectedFunction && prefix) {
            // 检查当前消息是否已经以该功能的前缀开头
            if (!message.startsWith(prefix)) {
                // 如果没有，则将前缀加到消息前面
                finalMessage = prefix + message;
            }
            // 如果消息已经以功能前缀开头 (常见情况，因为 handleFunctionSelect 会设置前缀),
            // 那么 finalMessage (其初始值为 message) 就是正确的，不需要额外操作。
        }
        // 如果没有选择功能 (selectedFunction 为 null), 或者选中的功能没有前缀 (prefix 为空),
        // 那么 finalMessage (其初始值为 message) 也是正确的。
        
        console.log(`消息内容: "${finalMessage}"`);

        // 准备文件URL
        const fileUrls: {imageUrls?: string[], imageIds?: string[], fileUrls?: string[], fileNames?: string[]} = {};

        if (successfulImageUrls.length > 0) {
          fileUrls.imageUrls = successfulImageUrls;
          console.log(`包含 ${successfulImageUrls.length} 个图片附件`);

          // 如果有图片ID，也添加到请求中
          if (successfulImageIds.length > 0) {
            fileUrls.imageIds = successfulImageIds;
            console.log(`包含 ${successfulImageIds.length} 个图片ID`);
          }
        }

        if (successfulFileUrls.length > 0) {
          fileUrls.fileUrls = successfulFileUrls;
          fileUrls.fileNames = successfulFileNames;
          console.log(`包含 ${successfulFileUrls.length} 个文件附件`);
        }

        // 在移动设备上，先将文本框失去焦点收起键盘，然后再发送消息
        if (isMobile && textareaRef.current) {
          console.log('移动设备: 先使文本框失去焦点');
          textareaRef.current.blur();

          // 使用小延迟确保键盘收起后再发送消息
          setTimeout(() => {
            console.log('移动设备: 延迟后发送消息');
            onSendMessage(finalMessage, Object.keys(fileUrls).length > 0 ? fileUrls : undefined, selectedModel);
            setMessage("");
            setFiles([]);

            // 重置选中的功能按钮
            setSelectedFunction(null);
            console.log('已重置选中的功能按钮');

            // 重置文本框高度
            if (textareaRef.current) {
              textareaRef.current.style.height = 'auto';
            }
          }, 100);
        } else {
          // 桌面设备直接发送
          console.log('桌面设备: 直接发送消息');
          onSendMessage(finalMessage, Object.keys(fileUrls).length > 0 ? fileUrls : undefined, selectedModel);
          setMessage("");
          setFiles([]);

          // 重置选中的功能按钮
          setSelectedFunction(null);
          console.log('已重置选中的功能按钮');

          // 重置文本框高度
          if (textareaRef.current) {
            textareaRef.current.style.height = 'auto';
          }
        }
      } catch (error) {
        console.error('发送消息时出错:', error);
        toast.error('发送消息失败，请重试');
      }
    } else if (hasUploadingFiles) {
      toast.info('请等待文件上传完成');
    }
  }

  // 处理按键事件（Enter发送消息，Shift+Enter换行）
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    // 如果命令菜单打开，处理上下箭头和回车键
    if (showCommands) {
      const filteredCommands = getFilteredCommands();

      if (e.key === "ArrowDown") {
        e.preventDefault();
        setSelectedCommandIndex(prev =>
          prev < filteredCommands.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        setSelectedCommandIndex(prev => prev > 0 ? prev - 1 : 0);
      } else if (e.key === "Enter" || e.key === "Tab") {
        e.preventDefault();
        if (filteredCommands.length > 0) {
          // 有匹配的命令，选择命令
          const index = Math.min(selectedCommandIndex, filteredCommands.length - 1);
          handleCommandSelect(filteredCommands[index]);
        } else {
          // 没有匹配的命令，关闭命令菜单并发送消息
          setShowCommands(false);
          handleSendMessage();
        }
        return;
      } else if (e.key === "Escape") {
        e.preventDefault();
        setShowCommands(false);
        return;
      }
    }

    // 正常的发送消息处理
    if (e.key === "Enter" && !showCommands) {
      if (e.shiftKey) {
        // Shift+Enter 换行，正常处理
        return;
      } else {
        // 普通 Enter 发送消息
        e.preventDefault();
        handleSendMessage();
      }
    }
  }

  // 自动调整文本框高度
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // 重置高度，以便正确计算新高度
    textarea.style.height = 'auto';

    // 计算新高度，但不超过最大高度
    const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
    const defaultHeight = lineHeight * 6; // 默认4行高度
    const maxHeight = lineHeight * 8; // 最大8行高度

    // 取滚动高度和最大高度中的较小值
    const newHeight = Math.min(textarea.scrollHeight, maxHeight);

    // 设置新高度，确保至少有默认高度
    textarea.style.height = `${Math.max(newHeight, defaultHeight)}px`;
  };

  // 监听消息变化，自动调整高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // 滚动到选中的命令
  useEffect(() => {
    if (showCommands && commandsRef.current) {
      const selectedElement = commandsRef.current.querySelector(`[data-index="${selectedCommandIndex}"]`);
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [selectedCommandIndex, showCommands]);

  // 点击外部关闭命令菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (commandsRef.current && !commandsRef.current.contains(event.target as Node)) {
        setShowCommands(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 检查是否可以发送消息
  const canSendMessage = () => {
    return (
      !isDisabled && // 使用isDisabled
      ((message && message.trim().length > 0) || 
       (files.length > 0 && !files.some(f => f.uploading || f.error)))
    )
  };

  // 添加拖拽状态管理
  const [isDraggingAny, setIsDraggingAny] = useState(false);

  // 处理拖拽开始事件
  const handleDragStart = (event: DragStartEvent) => {
    setIsDraggingAny(true);

    // 在移动设备上添加触觉反馈
    if (isMobile && navigator.vibrate) {
      navigator.vibrate(50); // 短振动50ms
    }
  };

  // 处理拖拽结束事件
  const handleDragEnd = (event: DragEndEvent) => {
    setIsDraggingAny(false);
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setFunctionButtons((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        // 更新按钮排序
        const newOrder = arrayMove(items, oldIndex, newIndex);
        return newOrder;
      });

      // 排序成功时的触觉反馈
      if (isMobile && navigator.vibrate) {
        navigator.vibrate([50, 50, 50]); // 排序成功时的脉冲模式
      }
    }
  };

  // 检测是否为移动设备
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  // 创建传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 移动设备上使用更小的距离和延迟，使拖拽更容易触发
      activationConstraint: isMobile
        ? {
            delay: 100, // 移动设备上添加小延迟，防止意外触发
            tolerance: 5, // 允许小幅度移动而不取消拖拽
          }
        : {
            distance: 8, // 桌面设备上需要拖动至少8像素才触发拖拽
          },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 可排序按钮组件
  interface SortableButtonProps {
    button: FunctionButton;
    isSelected: boolean;
    onSelect: (id: string) => void;
  }

  const SortableButton = ({ button, isSelected, onSelect }: SortableButtonProps) => {
    // 在流式响应期间禁用拖拽功能
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging
    } = useSortable({
      id: button.id,
      disabled: isDisabled // 在流式响应期间禁用拖拽
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      zIndex: isDragging ? 10 : 1,
      opacity: isDragging ? 0.8 : 1,
    };

    return (
      <Button
        ref={setNodeRef}
        style={style}
        variant={isSelected ? "default" : "outline"}
        size="sm"
        className={cn(
          "h-8 text-xs flex items-center shrink-0",
          // 在流式响应期间显示禁用状态
          isDisabled
            ? "cursor-default opacity-70"
            : "cursor-grab active:cursor-grabbing",
          isSelected
            ? "bg-primary text-primary-foreground"
            : "hover:bg-muted/50"
        )}
        onClick={() => onSelect(button.id)}
        {...attributes}
        {...(isDisabled ? {} : listeners)} // 在流式响应期间不添加拖拽监听器
      >
        {button.icon}
        {/* <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> */}
          {button.name}
        {/* </span> */}
      </Button>
    );
  };

  // 过滤后的命令列表
  const filteredCommands = getFilteredCommands();

  // 处理拖拽事件
  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(true);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDraggingOver) setIsDraggingOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 确保只有当鼠标真正离开容器时才重置状态
    // 检查relatedTarget是否是容器的子元素
    const relatedTarget = e.relatedTarget as Node;

    // 如果离开目标不在容器内部，或者为null（离开浏览器窗口），则重置状态
    if (!relatedTarget || (chatInputRef.current && !chatInputRef.current.contains(relatedTarget))) {
      setIsDraggingOver(false);
    }
  };

  const handleDrop = async (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    // 检查是否有文件被拖放
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await handleFilesUpload(Array.from(e.dataTransfer.files));
    }
  };

  // 处理粘贴事件
  const handlePaste = async (e: ClipboardEvent<HTMLTextAreaElement>) => {
    // 如果粘贴的是文本，让默认行为处理
    if (!e.clipboardData.files.length) {
      return;
    }

    // 如果粘贴包含文件（通常是图片），则处理文件上传
    const files = Array.from(e.clipboardData.files);
    if (files.length > 0) {
      // 阻止默认粘贴行为，以防止图片被粘贴为文本
      e.preventDefault();
      await handleFilesUpload(files);
    }
  };

  return (
    <div
      ref={chatInputRef}
      className={cn(
        "p-2 pb-3 sm:p-3 bg-background",
        isDraggingOver && "ring-2 ring-primary ring-offset-2 bg-muted/50 transition-all duration-200"
      )}
      data-tour="chat-input"
      onDragEnter={handleDragEnter}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* 新对话按钮 */}
      <div className="flex justify-center mb-3">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1.5 px-3 py-1 h-8 border-dashed"
          onClick={onNewConversation}
          data-tour="new-conversation"
        >
          <Plus className="h-4 w-4" />
          <span className="gradient-text-safe gradient-text-fallback">
            开启新对话
          </span>
        </Button>
      </div>

      {/* 功能按钮组 */}
      <div className="mb-2 overflow-x-auto no-scrollbar" data-tour="function-buttons">
        {/* 流式响应期间显示简化版按钮组 */}
        {isDisabled ? (
          <div className="flex gap-2 pb-2">
            {functionButtons.map((button) => (
              <Button
                key={button.id}
                variant={selectedFunction === button.id ? "default" : "outline"}
                size="sm"
                className={cn(
                  "h-8 text-xs flex items-center shrink-0 opacity-70",
                  selectedFunction === button.id
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted/50"
                )}
                onClick={() => handleFunctionSelect(button.id)}
                disabled={true}
              >
                {button.icon}
                {/* <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> */}
                  {button.name}
                {/* </span> */}
              </Button>
            ))}
          </div>
        ) : (
          <>
            {/* 如果切换显示OCR按钮，显示提示信息 */}
            {showOcrButtons && (
              <div className="text-xs text-primary mb-1 px-2">
                已上传图片，请选择图像处理功能
              </div>
            )}
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={isMobile ? [restrictToHorizontalAxis] : []}
            >
              <SortableContext
                items={currentFunctionButtons.map(button => button.id)}
                strategy={horizontalListSortingStrategy}
              >
                <div className={cn(
                  "flex gap-2 pb-2 transition-colors duration-200",
                  isDraggingAny && "bg-muted/50 rounded-md p-1"
                )}>
                  {currentFunctionButtons.map((button) => (
                    <SortableButton
                      key={button.id}
                      button={button}
                      isSelected={selectedFunction === button.id}
                      onSelect={handleFunctionSelect}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </>
        )}
      </div>

      {/* 显示已选择的文件 */}
      {files.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {files.map((fileItem) => (
            <div
              key={fileItem.id}
              className={cn(
                "border rounded px-2.5 py-1 text-xs flex items-center gap-1.5",
                fileItem.error ? "bg-red-50 border-red-200 text-red-500" : "bg-muted"
              )}
            >
              <span className="truncate max-w-[150px]">
                {fileItem.uploading && (
                  <Loader2 className="h-3 w-3 mr-1 inline animate-spin" />
                )}
                {fileItem.error && "❌ "}
                {fileItem.file.name}
              </span>
              <Button
                size="icon"
                variant="ghost"
                className="h-4 w-4 rounded-full hover:bg-background"
                onClick={() => handleRemoveFile(fileItem.id)}
              >
                <span className="text-xs">×</span>
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* 拖拽上传提示 */}
      {isDraggingOver && (
        <div className="text-center text-sm text-primary mb-2 animate-pulse">
          释放鼠标以上传文件
        </div>
      )}

      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={message}
          onChange={handleMessageChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          disabled={isDisabled}
          placeholder={selectedFunction
            ? (() => {
                const selectedBtn = functionButtons.find(btn => btn.id === selectedFunction);
                return selectedBtn?.description 
                  ? selectedBtn.description 
                  : `使用 ${selectedBtn?.name} 功能...`;
              })()
            : isDraggingOver
              ? "拖放图片到此处上传"
              : isDisabled
                ? "AI正在回复中，请稍等..."
                : "有问题请问，输入 / 查看可用快捷命令，Enter 发送，Shift+Enter 换行，可拖放或粘贴上传图片"}
          className={cn(
            "min-h-20 resize-none py-2.5 px-3 text-base leading-tight pr-24 overflow-y-auto",
            isDisabled && "opacity-70 cursor-not-allowed"
          )}
          style={{
            minHeight: '8rem', // 默认高度约4行
            maxHeight: '10rem', // 最大高度约8行
          }}
        />

        {/* 命令菜单 */}
        {showCommands && (
          <div
            ref={commandsRef}
            className="absolute bottom-full left-0 w-full bg-background border rounded-md shadow-md mb-1 max-h-[240px] overflow-y-auto z-10"
          >
            {filteredCommands.length === 0 ? (
              <div className="p-3 text-muted-foreground text-sm flex items-center gap-2">
                <Search className="h-4 w-4" />
                没有匹配的命令
              </div>
            ) : (
              <>
                <div className="p-1.5 text-xs text-muted-foreground border-b">
                  可用命令
                </div>
                {filteredCommands.map((command, index) => (
                  <div
                    key={command.id}
                    data-index={index}
                    className={cn(
                      "p-2 hover:bg-muted flex items-center gap-2 cursor-pointer",
                      selectedCommandIndex === index && "bg-muted"
                    )}
                    onClick={() => handleCommandSelect(command)}
                  >
                    <div className="flex items-center justify-center w-6 h-6">
                      {command.icon}
                    </div>
                    <div className="flex flex-col">
                      <div className="font-medium text-sm">/{command.name}</div>
                      {command.description && (
                        <div className="text-xs text-muted-foreground">
                          {command.description}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        )}

        <div className="absolute bottom-2 right-2 flex items-center gap-2">
          {/* 模型选择下拉框 */}
          <div className="flex items-center gap-1">
            <Select
              value={selectedModel}
              onValueChange={(value) => onModelChange?.(value)}
            >
              <SelectTrigger className="h-6 w-40 text-base">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="speed"><span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">极速模式</span></SelectItem>
                <SelectItem value="deep"><span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">深度思考</span></SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 p-1.5 rounded-full"
            onClick={handleOpenImageSelect}
            disabled={isUploading}
            data-tour="image-upload"
          >
            <ImageIcon className="h-8 w-8" />
            <span className="sr-only">上传图片</span>
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 p-1.5 rounded-full"
            onClick={handleOpenFileSelect}
            disabled={isUploading}
            data-tour="file-upload"
          >
            <PaperclipIcon className="h-4 w-4" />
            <span className="sr-only">附加文件</span>
          </Button>
          <Button
            size="icon"
            className="h-8 w-8 p-1.5 rounded-full"
            onClick={(e) => {
              // 防止事件冒泡和默认行为
              e.preventDefault();
              e.stopPropagation();
              
              if (isDisabled && onStopStreaming) {
                console.log('停止按钮被点击');
                onStopStreaming();
              } else {
                console.log('发送按钮被点击');
                handleSendMessage();
              }
            }}
            disabled={!isDisabled && !canSendMessage()}
            type="button"
            aria-label={isDisabled ? "停止AI回复" : "发送消息"}
            variant={isDisabled ? "destructive" : "default"}
          >
            {isDisabled ? (
              <Square className="h-4 w-4" />
            ) : (
              <SendIcon className="h-4 w-4" />
            )}
            <span className="sr-only">{isDisabled ? "停止AI回复" : "发送消息"}</span>
          </Button>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        multiple
      />
      {/* 隐藏的图片输入 */}
      <input
        type="file"
        ref={imageInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept="image/*"
      />
    </div>
  )
}
