import React, { useState } from 'react';
import { FeatureCard } from './FeatureCard';
import featureCardsConfig from '@/config/featureCards';
import { ChevronLeft } from 'lucide-react';

export interface FeatureCardsProps {
  onSendMessage: (message: string) => void;
}

/**
 * A component that renders multiple feature cards from the configuration
 * On mobile devices, displays cards in a horizontally scrollable container with 2 cards per row
 * On desktop devices, displays cards in a grid layout
 * Supports second-level examples for specific feature cards
 */
export function FeatureCards({ onSendMessage }: FeatureCardsProps) {
  const [currentView, setCurrentView] = useState<'main' | { id: string, title: string }>('main');

  // Get current cards to display based on view state
  const currentCards = currentView === 'main'
    ? featureCardsConfig
    : featureCardsConfig.find(card => card.id === (currentView as { id: string }).id)?.subExamples || [];

  // Handler for when a card with sub-examples is clicked
  const handleCardWithSubExamplesClick = (card: typeof featureCardsConfig[0]) => {
    if (card.subExamples?.length) {
      setCurrentView({ id: card.id, title: card.title });
      return true;
    }
    return false;
  };

  // 直接处理消息发送，不依赖于子卡片状态
  const handleDirectMessageSend = (message: string) => {
    if (typeof onSendMessage === 'function') {
      onSendMessage(message);
    } else {
      console.error('onSendMessage 函数未定义或不是函数');
    }
  };

  // Go back to main view
  const handleBackClick = () => {
    setCurrentView('main');
  };

  return (
    <>
      {/* Back button when showing sub-examples */}
      {currentView !== 'main' && (
        <div className="flex items-center mb-3 text-sm font-medium cursor-pointer" onClick={handleBackClick}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>返回</span>
          <span className="mx-2">|</span>
          <span>{(currentView as { title: string }).title}</span>
        </div>
      )}

      {/* Mobile view with horizontal scrolling */}
      <div className="md:hidden w-full overflow-x-auto pb-4 no-scrollbar">
        <div className="flex flex-nowrap px-1 pb-1">
          {/* Group cards into pairs for two-column layout */}
          {Array.from({ length: Math.ceil(currentCards.length / 2) }).map((_, rowIndex) => (
            <div key={`row-${rowIndex}`} className="flex-none w-[290px] pr-3 pl-0.5">
              <div className="grid grid-cols-2 gap-2 h-full">
                {currentCards.slice(rowIndex * 2, rowIndex * 2 + 2).map((card, index) => (
                  <div key={card.id} className="h-full">
                    {/* 确保最后一组卡片存在时正确显示 */}
                    {(rowIndex < Math.floor(currentCards.length / 2) || index < currentCards.length % 2 || currentCards.length % 2 === 0) && (
                      <FeatureCard
                        id={card.id}
                        title={card.title}
                        description={card.description}
                        message={card.message}
                        icon={card.icon}
                        colorClass={card.colorClass}
                        iconColorClass={card.iconColorClass}
                        onSelect={(message) => {
                          if (!handleCardWithSubExamplesClick(card)) {
                            handleDirectMessageSend(message);
                          }
                        }}
                        hasSubExamples={!!card.subExamples?.length}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
          {/* 添加额外的空间确保最后一组卡片完全可见 */}
          <div className="flex-none w-8"></div>
        </div>
      </div>

      {/* Desktop view with grid layout */}
      <div className="hidden md:grid grid-cols-2 gap-4 max-w-4xl w-full">
        {currentCards.map((card) => (
          <FeatureCard
            key={card.id}
            id={card.id}
            title={card.title}
            description={card.description}
            message={card.message}
            icon={card.icon}
            colorClass={card.colorClass}
            iconColorClass={card.iconColorClass}
            onSelect={(message) => {
              if (!handleCardWithSubExamplesClick(card)) {
                handleDirectMessageSend(message);
              }
            }}
            hasSubExamples={!!card.subExamples?.length}
          />
        ))}
      </div>
    </>
  );
}