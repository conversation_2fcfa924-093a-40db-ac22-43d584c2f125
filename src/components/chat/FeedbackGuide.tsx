"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ThumbsUp, ThumbsDown, Lightbulb, X, Heart } from "lucide-react"
import { useWechatWorkAuth } from "@/components/auth/WechatWorkAuth"

interface FeedbackGuideProps {
  onClose: () => void
}

export function FeedbackGuide({ onClose }: FeedbackGuideProps) {
  const { userInfo } = useWechatWorkAuth()

  const handleClose = () => {
    // 记录用户已看过引导
    try {
      const guideKey = `feedback_guide_shown_${userInfo?.userId || 'anonymous'}`
      localStorage.setItem(guideKey, 'true')
    } catch (error) {
      console.error('保存引导状态失败:', error)
    }
    onClose()
  }

  return (
    <Card className="mt-4 border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg animate-in slide-in-from-bottom-2 duration-500">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            <h3 className="font-semibold text-gray-800">您的反馈很重要！</h3>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-500 hover:text-gray-700"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">
          帮助我们改进AI助手的回答质量，您可以：
        </p>
        
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
              <ThumbsUp className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">点赞好回答</p>
              <p className="text-xs text-gray-500">对有帮助的回答点赞，让AI学习您的偏好</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-red-100">
              <ThumbsDown className="h-4 w-4 text-red-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">点踩差回答</p>
              <p className="text-xs text-gray-500">对不满意的回答点踩，帮助AI改进</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-amber-100">
              <Lightbulb className="h-4 w-4 text-amber-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800">问题/意见反馈</p>
              <p className="text-xs text-gray-500">遇到问题或有改进建议可以反馈给我们</p>
            </div>
          </div>
        </div>
        
        <div className="mt-4 flex justify-end">
          <Button
            onClick={handleClose}
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white text-sm px-4 py-2"
          >
            知道了
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default FeedbackGuide
