server {
    listen 80;
    server_name your-domain.com; # 替换为你的域名

    # 日志配置
    access_log /var/log/nginx/your-domain.access.log;
    error_log /var/log/nginx/your-domain.error.log;

    # 根目录配置
    root /usr/share/nginx/html;
    index index.html;

    # 静态文件缓存设置
    location /_next/static/ {
        alias /usr/share/nginx/html/_next/static/;
        expires 365d;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # 处理Next.js应用
    location / {
        try_files $uri $uri.html $uri/ /index.html;
    }

    # API路由代理
    location /api/ {
        proxy_pass http://localhost:3000;  # 如果使用了后端服务，这里替换为实际的后端URL
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "OK";
    }

    # 处理404错误
    error_page 404 /404.html;
    location = /404.html {
        internal;
    }

    # 处理其他错误
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        internal;
    }
}