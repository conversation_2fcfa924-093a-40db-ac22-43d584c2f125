/** @type {import('next').NextConfig} */
const nextConfig = {
  /* config options here */
  eslint: {
    // 在构建时不进行ESLint检查
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true, // 忽略 TypeScript 检查
  },
  images: {
    domains: ['localhost', '127.0.0.1', 'onlinesales-prod-ftp.groupama-sdig.com', 'oa.groupama-sdig.com'],
  },
  devIndicators: false,
  // 配置SWC编译器以支持旧版浏览器
  compiler: {
    // 移除console.log语句（生产环境）
    removeConsole: process.env.NODE_ENV === 'production',
  },
};

export default nextConfig;
