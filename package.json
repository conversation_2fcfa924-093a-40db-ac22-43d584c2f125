{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint", "package": "npm run build && rm -rf dist && mkdir -p dist && cp -R .next dist/ && cp -R public dist/ && cp package.json package-lock.json next.config.mjs dist/ && [ -f .env.local ] && cp .env.local dist/ || true && zip -r chatbot-deployment.zip dist/"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@types/dompurify": "^3.0.5", "@types/echarts": "^5.0.0", "@types/highlight.js": "^10.1.0", "@types/katex": "^0.16.7", "@types/prismjs": "^1.26.5", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.6", "echarts": "^5.6.0", "highlight.js": "^11.11.1", "html-react-parser": "^5.2.5", "katex": "^0.16.22", "lucide-react": "^0.475.0", "mdast-util-gfm-autolink-literal": "2.0.0", "mermaid": "^11.5.0", "nanoid": "^5.1.5", "next": "^15.2.0", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "unist-util-visit": "^5.0.0", "xlsx": "^0.18.5"}, "overrides": {"mdast-util-gfm-autolink-literal": "2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}